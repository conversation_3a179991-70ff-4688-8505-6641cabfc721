<?php

// use function GuzzleHttp\json_encode;

require '../session_setting.php';

ini_set('memory_limit', '1G');
ini_set('max_execution_time', 300);

if (!isset($_SESSION['user']) || empty($_SESSION['user']) || $_SESSION['user'] == null || $_SESSION['user'] == '') {
	flash('notlogin', 'กรุณา Login เพื่อใช้งาน', 'alert alert-danger');
	header("Location: ../index.php");
}

require '../helpers/pagesvisited.php';

extract(fetch_prep_for_hospital($conn));

// error_reporting(E_ALL);
// ini_set('display_errors', 1);

$this_fy = checkQuarter()['this_fy'];
$this_fy_text = "FY{$this_fy}";
?>

<?php include '../layouts/header.php' ?>

<div class="wrapper">

	<style>
		#performanceTable th,
		#performanceTable td {
			border: 1px solid #000;
			text-align: center;
		}

		#nhsoTable th,
		#nhsoTable td {
			border: 1px solid #000;
			text-align: center;
		}

		/* pre {
			margin: 0px;
		} */
	</style>

	<?php include '../layouts/topmenu.php' ?>
	<?php include '../layouts/sidebarmenu.php' ?>

	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header">
			<h1>
				Main Dashboard
				<small>Link ลัดเข้าสู่ส่วนงาน</small>
			</h1>
			<ol class="breadcrumb">
				<li><a href="#"><i class="fas fa-tachometer-alt"></i> Home</a></li>
				<li class="active">Main</li>
			</ol>
		</section>

		<!-- Main content -->
		<section id="dashboardMainSection" class="content">
			<!-- Info boxes -->
			<div class="row">
				<div class="col-lg-12">
					<div class="box box-success box-solid">
						<div class="box-header with-border" data-widget="collapse">
							<h3 class="box-title">Hospital link</h3>
						</div>
						<div class="box-body">
							<div style="display:flex;justify-content:center;flex-wrap:wrap;">
								<div style="margin: 0px 10px;" class="<?= in_array($_SESSION['site_specific']->sitename2, ['CAREMAT', 'Mplus', 'ACTSE']) ? '' : 'hidden' ?>">
									<a target="_blank" href="https://www.caremat.actse-clinic.com" class="btn btn-primary">CAREMAT
										<span class='badge bg-yellow' style='font-size:1.4rem;' id="caremat">0</span>
									</a>
								</div>
								<div style="margin: 0px 10px;" class="<?= in_array($_SESSION['site_specific']->sitename2, ['CAREMAT', 'Mplus', 'ACTSE']) ? '' : 'hidden' ?>">
									<a target="_blank" href="https://www.mplus-cmi.actse-clinic.com" class="btn btn-success">Mplus CMI
										<span class='badge bg-blue' style='font-size:1.4rem;' id="mplus_cmi">0</span>
									</a>
									<a target="_blank" href="https://www.mplus-cri.actse-clinic.com" class="hidden btn btn-success">Mplus CRI
										<span class='badge bg-blue' style='font-size:1.4rem;' id="mplus_cri_1">0</span>
									</a>
									<a target="_blank" href="https://www.mplus-plk.actse-clinic.com" class="hidden btn btn-success">Mplus PLK</a>
								</div>
								<div style="margin: 0px 10px;" class="<?= in_array($_SESSION['site_specific']->sitename2, ['RSAT', 'ACTSE']) ? '' : 'hidden' ?>">
									<a target="_blank" href="https://www.rsat-bkk.actse-clinic.com" class="btn btn-info">RSAT BKK
										<span class='badge bg-blue' style='font-size:1.4rem;' id="rsat_bkk">0</span>
									</a>
									<a target="_blank" href="https://www.rsat-ska.actse-clinic.com" class="btn btn-info">RSAT SKA
										<span class='badge bg-blue' style='font-size:1.4rem;' id="rsat_ska">0</span>
									</a>
									<a target="_blank" href="https://www.rsat-ubn.actse-clinic.com" class="btn btn-info">RSAT UBN
										<span class='badge bg-blue' style='font-size:1.4rem;' id="rsat_ubn">0</span>
									</a>
									<a target="_blank" href="https://www.rsat-cbi.actse-clinic.com" class="btn btn-info">RSAT CBI
										<span class='badge bg-blue' style='font-size:1.4rem;' id="rsat_cbi">0</span>
									</a>
									<!-- <a target="_blank" href="https://www.rsat-npt.actse-clinic.com" class="btn btn-info">RSAT NPT
										<span class='badge bg-blue' style='font-size:1.4rem;' id="rsat_npt">0</span>
									</a> -->
									<a target="_blank" href="https://www.mplus-cri.actse-clinic.com" class="btn btn-success">Mplus CRI
										<span class='badge bg-blue' style='font-size:1.4rem;' id="mplus_cri_2">0</span>
									</a>
								</div>
							</div>
							<div style="display:flex;justify-content:space-around;flex-wrap:wrap;">
								<div style="margin-bottom:10px">
									<div style="display: inline-block; padding: 15px;">
										<div class="text-center">
											<a style="margin:0px;padding: 5px;" target="_blank" href="../prep/prep_stock.php" class="btn btn-app bg-blue">
												<span class="hidden badge bg-green">New</span>
												<i style="padding: 5px;" class="fa fa-capsules"></i> <span class="h5">PrEP Stock</span>
											</a>
										</div>
										<div class="text-center">
											<small>หน้า stock และการจ่ายยา</small>
										</div>
									</div>
									<div style="display: inline-block; padding: 15px;">
										<div class="text-center">
											<a style="margin:0px;padding: 5px;" target="_blank" href="../prep/prep_lists.php" class="btn btn-app bg-yellow">
												<span class="hidden badge bg-green">New</span>
												<i style="padding: 5px;" class="fa fa-list-ul"></i> <span class="h5">PrEP List</span>
											</a>
										</div>
										<div class="text-center">
											<small>รายการเคส PrEP ทั้งหมด</small>
										</div>
									</div>
									<div style="display: inline-block; padding: 15px;">
										<div class="text-center">
											<a style="margin:0px;padding: 5px;" target="_blank" href="../prep/prep_hospital_enterlab.php" class="btn btn-app bg-purple">
												<span class="hidden badge bg-green">New</span>
												<?= $bloodtest > 0 ? "<span class='badge bg-blue' style='font-size:1.4rem;'>$bloodtest</span>" : "" ?>
												<i style="padding: 5px;" class="fa fa-stethoscope"></i> <span class="h5">Blood Test List</span>
											</a>
										</div>
										<div class="text-center">
											<small>รายแจ้งตรวจเลือด ยืนยันอนุมัติยา</small>
										</div>
									</div>
									<div style="display: inline-block; padding: 15px;">
										<div class="text-center">
											<a style="margin:0px;padding: 5px;" target="_blank" href="../prep/prep_hospital_approve.php" class="btn btn-app bg-maroon">
												<span class="hidden badge bg-green">New</span>
												<?= $requestPrEP > 0 ? "<span class='badge bg-blue' style='font-size:1.4rem;'>$requestPrEP</span>" : "" ?>
												<i style="padding: 5px;" class="fa fa-check"></i> <span class="h5">PrEP Approve</span>
											</a>
										</div>
										<div class="text-center">
											<small>รายการรออนุมัติ PrEP</small>
										</div>
									</div>
									<div style="display: inline-block; padding: 15px;">
										<div class="text-center">
											<a style="margin:0px;padding: 5px;" target="_blank" href="../pep/pep_hospital_approve.php" class="btn btn-app bg-aqua">
												<span class="hidden badge bg-blue">New</span>
												<?= $requestnPEP > 0 ? "<span class='badge bg-blue' style='font-size:1.4rem;'>$requestnPEP</span>" : "" ?>
												<i style="padding: 5px;" class="fa fa-check"></i> <span class="h5">nPEP Approve</span>
											</a>
										</div>
										<div class="text-center">
											<small>รายการรออนุมัติ nPEP</small>
										</div>
									</div>
									<div style="display: inline-block; padding: 15px;">
										<div class="text-center">
											<a style="margin:0px;padding: 5px;" target="_blank" href="../samedayart/sdart_hospital_approve.php" class="btn btn-app bg-red">
												<span class="hidden badge bg-blue">New</span>
												<?= $requestSDART > 0 ? "<span class='badge bg-blue' style='font-size:1.4rem;'>$requestSDART</span>" : "" ?>
												<i style="padding: 5px;" class="fa fa-check"></i> <span class="h5">SDART Approve</span>
											</a>
										</div>
										<div class="text-center">
											<small>รายการรออนุมัติ SDART</small>
										</div>
									</div>
									<div style="display: inline-block; padding: 15px;">
										<div class="text-center">
											<a style="margin:0px;padding: 5px;" target="_blank" href="../prep/prep_enter_nap.php?hospital=hospital" class="btn btn-app bg-teal">
												<span class="hidden badge bg-green">New</span>
												<i style="padding: 5px;" class="fa fa-list-ul"></i> <span class="h5">Nap Data List</span>
											</a>
										</div>
										<div class="text-center">
											<small>ข้อมูลเคสเพื่อบันทึก Nap</small>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<section class="content" id="prepVue">
				<div class="row">
					<div class="col-lg-6">
						<div class="box box-info box-solid">
							<div class="box-header with-border" data-widget="collapse">
								<h3 class="box-title">สรุป จำนวนจ่าย PrEP รายเดือน (ขวด)</h3>
							</div>
							<div class="box-body">
								<div class="row">
									<div class="col-lg-4">
										<div class="form-group">
											<label for="monthly_range">เลือกช่วงเวลา</label>
											<input type="text" v-model="monthly_range" class="form-control" id="monthly_range">
										</div>
									</div>
									<div class="col-lg-3">
										<div class="form-group">
											<label for="monthly_fy">เลือก ปี FY</label>
											<select class="form-control" v-model="monthly_fy" @change="updateMonthlyDespensing()">
												<option></option>
												<option v-for="fy in lists.fy_lists" v-text="fy"></option>
											</select>
										</div>
									</div>
									<div class="col-lg-3">
										<div class="form-group">
											<label for="monthly_quarter">เลือก Quarter</label>
											<select class="form-control" v-model="monthly_quarter" @change="updateMonthlyDespensing()">
												<option></option>
												<option v-for="quarter in lists.quarter_lists" v-text="quarter"></option>
											</select>
										</div>
									</div>
								</div>
								<div class="row">
									<div class="col-lg-12">
										<div id="prep_despensing_monthly" style="min-width: 310px; height: 400px; margin: 0 auto"></div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="col-lg-6">
						<div class="box box-warning box-solid">
							<div class="box-header with-border" data-widget="collapse">
								<h3 class="box-title">สรุปจำนวนเคส จ่าย PrEP รายวัน (คน)</h3>
							</div>
							<div class="box-body">
								<div class="row">
									<div class="col-lg-4">
										<div class="form-group">
											<label for="daily_range">เลือกช่วงเวลา</label>
											<input type="text" v-model="daily_range" class="form-control" id="daily_range">
										</div>
									</div>
									<div class="col-lg-3">
										<div class="form-group">
											<label for="daily_fy">เลือก ปี FY</label>
											<select class="form-control" v-model="daily_fy" @change="updateDailyDespensing()">
												<option></option>
												<option v-for="fy in lists.fy_lists" v-text="fy"></option>
											</select>
										</div>
									</div>
									<div class="col-lg-3">
										<div class="form-group">
											<label for="daily_month">เลือก เดือน</label>
											<select class="form-control" v-model="daily_month" @change="updateDailyDespensing()">
												<option></option>
												<option v-for="month in lists.month_lists" :value="month.value" v-text="month.label"></option>
											</select>
										</div>
									</div>
								</div>
								<div class="row">
									<div class="col-lg-12">
										<div id="prep_despensing_daily" style="min-width: 310px; height: 400px; margin: 0 auto"></div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-lg-6">
						<div class="box box-primary box-solid">
							<div class="box-header with-border" data-widget="collapse" style="background:#ff7200;">
								<h3 class="box-title">PrEP Summary</h3>
							</div>
							<div class="box-body">
								<small class="text-primary">อ้างอิงความถูกต้องจากการบันทึกเข้าระบบ</small>
								<pre>
													<table>
														<tbody style="padding: 0px; margin: 0px;">
															<tr>
																<td style="max-width:100%;white-space:nowrap;">PrEP ทั้งหมด</td>
																<td style="max-width:100%;white-space:nowrap;" class="text-right">{{ prep_data.prep_total }} เคส</td>
															</tr>
															<tr>
																<td style="max-width:100%;white-space:nowrap;">PrEP มาล่าสุด FY<?= $this_fy ?> (CURR)</td>
																<td style="max-width:100%;white-space:nowrap;" class="text-right">{{ prep_data.prep_current }} เคส</td>
															</tr>
															<tr>
																<td style="max-width:100%;white-space:nowrap;">PrEP ครั้งแรกที่นี่ FY<?= $this_fy ?></td>
																<td style="max-width:100%;white-space:nowrap;" class="text-right">{{ prep_data.prep_new_in_site }} เคส</td>
															</tr>
															<tr>
																<td style="max-width:100%;white-space:nowrap;">PrEP ครั้งแรกในชีวิต FY<?= $this_fy ?> (NEW)</td>
																<td style="max-width:100%;white-space:nowrap;" class="text-right">{{ prep_data.prep_new_in_life }} เคส</td>
															</tr>
															<tr>
																<td style="max-width:100%;white-space:nowrap;">PrEP_CURR สปสช FY<?= $this_fy ?></td>
																<td style="max-width:100%;white-space:nowrap;" class="text-right">{{ prep_data.prep_curr_nhso }} เคส</td>
															</tr>
															<tr>
																<td style="max-width:100%;white-space:nowrap;">PrEP_CURR Princess FY<?= $this_fy ?></td>
																<td style="max-width:100%;white-space:nowrap;" class="text-right">{{ prep_data.prep_curr_princess }} เคส</td>
															</tr>
															<tr>
																<td style="max-width:100%;white-space:nowrap;">PrEP_CURR GF FY<?= $this_fy ?></td>
																<td style="max-width:100%;white-space:nowrap;" class="text-right">{{ prep_data.prep_curr_gf }} เคส</td>
															</tr>
															<tr>
																<td style="max-width:100%;white-space:nowrap;">PrEP_CURR Daily FY<?= $this_fy ?></td>
																<td style="max-width:100%;white-space:nowrap;" class="text-right">{{ prep_data.prep_curr_daily }} เคส</td>
															</tr>
															<tr>
																<td style="max-width:100%;white-space:nowrap;">PrEP_CURR On-demand FY<?= $this_fy ?></td>
																<td style="max-width:100%;white-space:nowrap;" class="text-right">{{ prep_data.prep_curr_ondemand }} เคส</td>
															</tr>
														</tbody>
													</table>	
												</pre>
								<div class="hidden">
									<a href="http://dmis.nhso.go.th/NAPPLUS/login.jsp" target="_blank" class="btn btn-success btn-xs" style="margin-bottom:5px;">ไป NAP หน้าเหลือง</a>
									<pre>
												<span class="<?= $prepNapColor ?>"><a target="_blank" href="../prep/prep_enter_nap.php"><?= labelbig('NAP', 'primary', '1rem') ?></a> -> PrEP บันทึก NAP คลินิก [ค้าง <?= $prepNap ?> เคส] </span>
												<span class="<?= $prepLabColor ?>"><a target="_blank" href="../prep/prep_enter_lab.php"><?= labelbig('LAB', 'teal', '1rem') ?></a> -> PrEP บันทึกผล Lab นอก [ค้าง <?= $prepLab ?> เคส] </span>
											</pre>
								</div>
							</div>
						</div>
					</div>
					<div class="col-lg-6">
						<div class="box box-primary box-solid">
							<div class="box-header with-border" data-widget="collapse" style="background:black;">
								<h3 class="box-title">สรุป PrEP <?= $this_fy_text ?></h3>
							</div>
							<div class="box-body">
								<figure class="highcharts-figure">
									<div id="container"></div>
								</figure>
							</div>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-lg-6">
						<div class="box box-primary box-solid">
							<div class="box-header with-border" data-widget="collapse" style="background:#058DC7;">
								<h3 class="box-title">สรุป PrEP ตามกลุ่มโครงการ</h3>
							</div>
							<div class="box-body">
								<figure class="highcharts-figure">
									<div id="Nhso_group"></div>
								</figure>
							</div>
						</div>
					</div>
					<div class="col-lg-6">
						<div class="box box-primary box-solid">
							<div class="box-header with-border" data-widget="collapse" style="background:#6495ED;">
								<h3 class="box-title">สรุป กลุ่มประเภทการทานยา</h3>
							</div>
							<div class="box-body">
								<figure class="highcharts-figure">
									<div id="daily_Ondemand"></div>
								</figure>
							</div>
						</div>
					</div>
				</div>
			</section>
		</section>
		<!-- /.content -->
	</div>
	<!-- /.content-wrapper -->

	<?php include '../layouts/footer.php' ?>
	<!-- /.control-sidebar -->
	<div class="control-sidebar-bg"></div>
</div>
<!-- ./wrapper -->



<?php include '../layouts/javascript.php' ?>

<script>
	let prepVue = new Vue({
		el: '#prepVue',
		data: {
			prep_data: {},
			prep_despensing: [],
			monthly_range: `2025-10-01 to 2026-10-01`,
			monthly_start: '2025-10-01',
			monthly_end: '2026-10-01',
			monthly_fy: '2026',
			monthly_quarter: 'All',
			daily_range: `2025-10-01 to 2026-10-01`,
			daily_start: '2025-10-01',
			daily_end: '2026-10-01',
			daily_fy: '2026',
			daily_month: 'All',
			flatpickrDate_monthly: '',
			flatpickrDate_daily: '',
			lists: {
				month_lists: [{
						value: '01',
						label: 'มกราคม'
					},
					{
						value: '02',
						label: 'กุมภาพันธ์'
					},
					{
						value: '03',
						label: 'มีนาคม'
					},
					{
						value: '04',
						label: 'เมษายน'
					},
					{
						value: '05',
						label: 'พฤษภาคม'
					},
					{
						value: '06',
						label: 'มิถุนายน'
					},
					{
						value: '07',
						label: 'กรกฎาคม'
					},
					{
						value: '08',
						label: 'สิงหาคม'
					},
					{
						value: '09',
						label: 'กันยายน'
					},
					{
						value: '10',
						label: 'ตุลาคม'
					},
					{
						value: '11',
						label: 'พฤศจิกายน'
					},
					{
						value: '12',
						label: 'ธันวาคม'
					},
					{
						value: 'All',
						label: 'All'
					},
				],
				quarter_lists: [
					'Q1',
					'Q2',
					'Q3',
					'Q4',
					'All'
				],
				fy_lists: [
					'2020',
					'2021',
					'2022',
					'2023',
					'2024',
					'2025',
					'2026',
					'All'
				]
			}
		},
		methods: {
			updateMonthlyDespensing() {

				let start = '2025-10-01'
				let end = '2026-10-01'

				if (this.monthly_fy && this.monthly_quarter) {

					start = checkQuarter(`${this.monthly_fy}-01-01`)[`${this.monthly_quarter.toLowerCase()}_start`]
					end = checkQuarter(`${this.monthly_fy}-01-01`)[`${this.monthly_quarter.toLowerCase()}_end`]

					if (this.monthly_quarter == 'All') {
						start = checkQuarter(`${this.monthly_fy}-01-01`).q1_start
						end = checkQuarter(`${this.monthly_fy}-01-01`).q4_end
					}

					if (this.monthly_fy == 'All') {
						this.monthly_quarter = 'All'
						start = checkQuarter('2020-01-01').q1_start
						end = checkQuarter('2026-01-01').q4_end
					}

					this.monthly_range = `${start} to ${end}`

					this.flatpickrDate_monthly.selectedDates = [
						new Date(start),
						new Date(end),
					]
					this.flatpickrDate_monthly.setDate = [
						new Date(start),
						new Date(end),
					]
					this.updateMonthlyRange(start, end)
				}
			},
			updateDailyDespensing() {

				let start = '2025-10-01'
				let end = '2026-10-01'

				if (this.daily_fy && this.daily_month) {

					let check_fy = this.daily_fy
					if (['10', '11', '12'].includes(this.daily_month)) check_fy--

					start = checkQuarter(`${check_fy}-${this.daily_month}-01`).this_month_start
					end = checkQuarter(`${check_fy}-${this.daily_month}-01`).this_month_end

					if (this.daily_month == 'All') {
						start = checkQuarter(`${this.daily_fy}-01-01`).q1_start
						end = checkQuarter(`${this.daily_fy}-01-01`).q4_end
					}

					if (this.daily_fy == 'All') {
						this.daily_month = 'All'
						start = checkQuarter('2020-01-01').q1_start
						end = checkQuarter('2023-01-01').q4_end
					}

					this.daily_range = `${start} to ${end}`

					this.flatpickrDate_daily.selectedDates = [
						new Date(start),
						new Date(end),
					]
					this.flatpickrDate_daily.setDate = [
						new Date(start),
						new Date(end),
					]
					this.updateDailyRange(start, end)
				}
			},
			fetch_prep_data() {
				axios.get('../dashboard/ajaxPrEPSummary.php').then(response => {

					// console.log(response.data);

					this.prep_data = response.data

					this.generate_highchart()
					this.general_Nhso_group()
					this.general_daily_Ondemand()

				}).catch(error => console.log(error.response.data));
			},
			fetch_prep_despensing_monthly() {

				let monthlyFormData = new FormData()

				monthlyFormData.append('startDate', this.monthly_start)
				monthlyFormData.append('endDate', this.monthly_end)

				axios.post('../dashboard/ajaxPrEPDespensing.php', monthlyFormData)
					.then(response => {

						this.prep_despensing.monthly = response.data.monthly

						this.generate_despensing_monthly()

					}).catch(error => console.log(error.response.data));
			},
			fetch_prep_despensing_daily() {

				let dailyFormData = new FormData()

				dailyFormData.append('startDate', this.daily_start)
				dailyFormData.append('endDate', this.daily_end)

				axios.post('../dashboard/ajaxPrEPDespensing.php', dailyFormData)
					.then(response => {

						// console.log(response.data)

						this.prep_despensing.daily = response.data.daily

						this.generate_despensing_daily()

					}).catch(error => console.log(error.response.data));
			},
			generate_despensing_monthly() {



				Highcharts.chart('prep_despensing_monthly', {
					credits: {
						enabled: false
					},
					chart: {
						type: 'column'
					},
					title: {
						text: 'จ่าย PrEP รายเดือน'
					},
					yAxis: {
						allowDecimals: false,
						min: 0,
						title: {
							text: 'จำนวน (ขวด)',
							align: 'high'
						}
					},
					xAxis: {
						categories: this.prep_despensing.monthly.category,
						allowDecimals: false,
						title: {
							text: null
						}
					},
					tooltip: {
						formatter: function() {
							// console.log(this)
							return '<b>' + this.x + ' : ' + this.series.name + '</b><br/>' +
								this.point.y + ' ขวด';
						}
					},
					plotOptions: {
						column: {
							dataLabels: {
								enabled: true,
								// rotation: -90,
								// color: '#FFFFFF',
								// align: 'right',
								// y: -20, // 20 pixels down from the top
								// format: '{point.y:.1f}', // one decimal
								crop: false,
								overflow: 'none',
								style: {
									fontSize: '10px'
								}
							}
						}
					},
					series: this.prep_despensing.monthly.series,

				});
			},
			generate_despensing_daily() {



				Highcharts.chart('prep_despensing_daily', {
					credits: {
						enabled: false
					},
					chart: {
						type: 'column'
					},
					title: {
						text: 'เคสจ่าย PrEP รายวัน'
					},
					yAxis: {
						allowDecimals: false,
						min: 0,
						title: {
							text: 'จำนวน (คน)',
							align: 'high'
						}
					},
					xAxis: {
						categories: this.prep_despensing.daily.category,
						allowDecimals: false,
						title: {
							text: null
						}
					},
					tooltip: {
						formatter: function() {
							// console.log(this)
							return '<b>' + this.x + ' : ' + this.series.name + '</b><br/>' +
								this.point.y + ' ขวด';
						}
					},
					plotOptions: {
						column: {
							dataLabels: {
								enabled: true,
								// rotation: -90,
								// color: '#FFFFFF',
								// align: 'right',
								// y: -20, // 20 pixels down from the top
								// format: '{point.y:.1f}', // one decimal
								crop: false,
								overflow: 'none',
								style: {
									fontSize: '10px'
								}
							}
						}
					},
					series: this.prep_despensing.daily.series,

				});
			},
			updateMonthlyRange(start, end) {
				this.monthly_start = start
				this.monthly_end = end

				this.fetch_prep_despensing_monthly()
			},
			updateDailyRange(start, end) {
				this.daily_start = start
				this.daily_end = end

				this.fetch_prep_despensing_daily()
			},
			generate_highchart() {
				Highcharts.chart('container', {
					credits: {
						enabled: false
					},
					chart: {
						type: 'column'
					},
					title: {
						align: 'center',
						text: ''
					},
					xAxis: {
						type: 'category'
					},
					yAxis: {
						title: {
							text: 'PrEP Summary'
						}

					},
					legend: {
						enabled: false
					},
					plotOptions: {
						series: {
							borderWidth: 0,
							dataLabels: {
								enabled: true,
								format: '{point.y}'
							}
						}
					},

					tooltip: {
						headerFormat: '<span style="font-size:11px">{series.name}</span><br>',
						pointFormat: '<span style="color:{point.color}">{point.name}</span>: <b>{point.y}</b><b> case </b><br/>'
					},

					series: [{
						name: "PrEP Summary",
						colorByPoint: true,
						data: [{
								name: 'จำนวนทั้งหมด',
								y: this.prep_data.prep_total,
							},
							{
								name: 'มาล่าสุด <?= $this_fy_text ?>',
								y: this.prep_data.prep_current,
							},
							{
								name: 'ครั้งแรกที่นี่ <?= $this_fy_text ?>',
								y: this.prep_data.prep_new_in_site,
							},
							{
								name: 'ครั้งแรกในชีวิต <?= $this_fy_text ?>',
								y: this.prep_data.prep_new_in_life,
							},
							{
								name: 'โครงการ สปสช <?= $this_fy_text ?>',
								y: this.prep_data.prep_curr_nhso,
							},
							{
								name: 'Princess <?= $this_fy_text ?>',
								y: this.prep_data.prep_curr_princess,
							},
							{
								name: 'GF <?= $this_fy_text ?>',
								y: this.prep_data.prep_curr_gf,
							},
							{
								name: 'Daily <?= $this_fy_text ?>',
								y: this.prep_data.prep_curr_daily,
							},
							{
								name: 'On-demand <?= $this_fy_text ?>',
								y: this.prep_data.prep_curr_ondemand,
							},

						]
					}],
				})
			},
			general_Nhso_group() {
				Highcharts.setOptions({
					colors: ['pink', '#9370DB', '#6AF9C4']
				});
				Highcharts.chart('Nhso_group', {
					chart: {
						plotBackgroundColor: null,
						plotBorderWidth: null,
						plotShadow: false,
						type: 'pie'
					},
					credits: {
						enabled: false
					},
					title: {
						text: `แยกตามโครงการ <?= $this_fy_text ?> (N=${this.prep_data.prep_current})`
					},
					tooltip: {
						pointFormat: '{series.name}: <b>{point.y:1f}<b> case </b></b>'
					},
					accessibility: {
						point: {
							valueSuffix: ''
						}
					},
					plotOptions: {
						pie: {
							allowPointSelect: true,
							cursor: 'pointer',
							dataLabels: {
								enabled: true,
								format: '<b>{point.name}</b>: {point.y:1f} <b> case </b>'
							}
						}
					},
					series: [{
						name: 'PrEP <?= $this_fy_text ?>',
						colorByPoint: true,
						data: [{
								name: 'สปสช <?= $this_fy_text ?>',
								y: this.prep_data.prep_curr_nhso,
								sliced: true,
								selected: true
							}, {
								name: 'Princess <?= $this_fy_text ?>',
								y: this.prep_data.prep_curr_princess,

							},
							{
								name: 'GF <?= $this_fy_text ?>',
								y: this.prep_data.prep_curr_gf
							}
						]
					}]
				});
			},
			general_daily_Ondemand() {
				Highcharts.setOptions({
					colors: ['#FF9655', '#6495ED']
				});
				Highcharts.chart('daily_Ondemand', {
					chart: {
						plotBackgroundColor: null,
						plotBorderWidth: null,
						plotShadow: false,
						type: 'pie'
					},
					credits: {
						enabled: false
					},
					title: {
						text: `แยกตามวิธีกินยา <?= $this_fy_text ?> (N=${this.prep_data.prep_current})`
					},
					tooltip: {
						pointFormat: '{series.name}: <b>{point.y:1f}<b> case </b></b>'
					},
					accessibility: {
						point: {
							valueSuffix: ''
						}
					},
					plotOptions: {
						pie: {
							allowPointSelect: true,
							cursor: 'pointer',
							dataLabels: {
								enabled: true,
								format: '<b>{point.name}</b>: {point.y:1f} <b> case </b>'
							}
						}
					},
					series: [{
						name: 'Daily Ondemand <?= $this_fy_text ?>',
						colorByPoint: true,
						data: [{
								name: 'Daily <?= $this_fy_text ?>',
								y: this.prep_data.prep_curr_daily,
								sliced: true,
								selected: true
							},
							{
								name: 'On-demand <?= $this_fy_text ?>',
								y: this.prep_data.prep_curr_ondemand
							}
						]
					}]
				});
			}

		},
		mounted() {
			this.fetch_prep_data()
			this.fetch_prep_despensing_monthly()
			this.fetch_prep_despensing_daily()

			this.flatpickrDate_monthly = $(`#monthly_range`).flatpickr({
				mode: "range",
				// minDate: "today",
				dateFormat: "Y-m-d",
				defaultDate: [checkQuarter().q1_start, checkQuarter().q4_end],
				// enable: [
				//   {
				//     from: moment().subtract(7, 'days').format('YYYY-MM-DD'),
				//     to: moment().add(7, 'd').format('YYYY-MM-DD')
				//   },
				// ],
				onChange: (selectedDates, dateStr, instance) => {
					// console.log(selectedDates)
					if (selectedDates.length == 2) {
						this.updateMonthlyRange(moment(selectedDates[0]).format('YYYY-MM-DD'), moment(selectedDates[1]).format('YYYY-MM-DD'))
					}
				}
			});

			this.flatpickrDate_daily = $(`#daily_range`).flatpickr({
				mode: "range",
				// minDate: "today",
				dateFormat: "Y-m-d",
				defaultDate: [checkQuarter().q1_start, checkQuarter().q4_end],
				// enable: [
				//   {
				//     from: moment().subtract(7, 'days').format('YYYY-MM-DD'),
				//     to: moment().add(7, 'd').format('YYYY-MM-DD')
				//   },
				// ],
				onChange: (selectedDates, dateStr, instance) => {
					// console.log(selectedDates)
					if (selectedDates.length == 2) {
						this.updateDailyRange(moment(selectedDates[0]).format('YYYY-MM-DD'), moment(selectedDates[1]).format('YYYY-MM-DD'))
					}
				}
			});
		}
	})
</script>

<script>
	// axios.get("fetch_prep_request.php")
	// 	.then(response => {
	// 		// console.log(response.data)
	// 		$("#caremat").html(response.data.caremat)
	// 		$("#mplus_cmi").html(response.data.mplus_cmi)
	// 		$("#mplus_cri_1").html(response.data.mplus_cri)
	// 		$("#mplus_cri_2").html(response.data.mplus_cri)
	// 		$("#rsat_bkk").html(response.data.rsat_bkk)
	// 		$("#rsat_ska").html(response.data.rsat_ska)
	// 		$("#rsat_ubn").html(response.data.rsat_ubn)
	// 		$("#rsat_cbi").html(response.data.rsat_cbi)
	// 	})
	// 	.catch(error => console.log(error))
</script>
<!-- ********Chart Data Script******** -->

<?php include '../layouts/end.php' ?>