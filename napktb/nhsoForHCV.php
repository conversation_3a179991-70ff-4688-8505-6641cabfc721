<?php
require '../session_setting.php';

ini_set('memory_limit', '1G');

if (!isset($_SESSION['user']) || empty($_SESSION['user']) || $_SESSION['user'] == null || $_SESSION['user'] == '') {
  header("Location: ../login.php?redirect=../napktb/nhsoForHCV.php");
}

require '../helpers/pagesvisited.php'; //

// error_reporting(E_ALL);
// ini_set('display_errors', 1);

if (!isset($_GET['start'])) {
  $startDate = checkQuarter()['this_month_start'];
  $endDate = date('Y-m-d') . " 23:59:59";
  $query = "?start={$startDate}&end={$endDate}";
}

$select_fy = $_GET['select_fy'] ?? '2026';

$query =  (isset($_GET['start']) && isset($_GET['end'])) ? "?start={$_GET['start']}&end={$_GET['end']}&select_fy={$select_fy}" : '';

$query_ecascade =  (isset($_GET['start']) && isset($_GET['end'])) ? "?start={$_GET['start']}&end={$_GET['end']}&select_fy={$select_fy}&ecascade=true" : '?ecascade=true';

?>

<?php include '../layouts/header.php' ?>
<div class="wrapper">

  <?php include '../layouts/topmenu.php' ?>
  <?php include '../layouts/sidebarmenu.php' ?>

  <style>
    .ktb_code_input.form-control {
      padding: 2px;
      height: 100%;
    }

    .ktb-position-absolute {
      width: 200px !important;
      position: absolute;
      top: -20px !important;
      right: -120px !important;
    }
  </style>

  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        NHSO บันทึก HCV ลง KTB
        <small>รายการเคส HCV Testing เพื่อบันทึกลง KTB หลังบันทึก HIV แล้ว</small>
      </h1>
      <ol class="breadcrumb">
        <li><a href="../index.php"><i class="fas fa-tachometer-alt"></i> Home</a></li>
        <li class="">NAPPlus & KTB</li>
        <li class="active"> HCV To KTB</li>
      </ol>
    </section>

    <!-- Main content -->
    <section class="content">
      <?php flash('invalidroute'); ?>
      <input type='hidden' value='<?= $_SESSION['user']; ?>' name='cbs' id="cbs" />
      <div class="row">
        <div class="col-lg-12">
          <div class="box box-warning">
            <div class="box-header with-border">
              <h3 class="box-title">HCV List</h3>
              <div class="box-tools pull-right">
                <input type="text" class="input-style" id="date_range">
                <select name="select_fy" id="select_fy" class="input-select">
                  <option <?= $select_fy == '2023' ? 'selected' : '' ?>>2023</option>
                  <option <?= $select_fy == '2024' ? 'selected' : '' ?>>2024</option>
                  <option <?= $select_fy == '2025' ? 'selected' : '' ?>>2025</option>
                  <option <?= $select_fy == '2026' ? 'selected' : '' ?>>2026</option>
                </select>
                <a href="./nhsoForHCV.php<?= $query ?>" id="filter_link" class="input-style w100 btn-primary">ดึงข้อมูล</a>
                <!-- <a href="./nhsoForHCV.php?period=all" class="input-style w100 btn-danger">ทั้งหมด</a> -->
                <a href="./nhsoForHCV.php<?= $query_ecascade ?>" id="filter_nap" class="input-style w100 btn-danger">ยังไม่บันทึก KTB</a>
              </div>
            </div>
            <!-- /.box-header -->

            <!-- form start -->
            <div class="box-body">
            <pre>
  คำแนะนำ
    : ข้อมูลจะถูก List จากเคสตรวจในคลินิก ที่มีการตรวจ HCV
    : ข้อมูลแสดงเพียงรายการแรกที่เข้ามาตรวจใน ปีงบประมาณ 
    : ปุ่ม "ยังไม่บันทึก KTB" มุมบนขวา จะแสดงเฉพาะรายการเคสที่ยังไม่ได้บันทึก KTB เท่านั้นเพื่อดูยอดคงค้าง
    : สามารถเลือกช่วงเวลา และ กดดึงข้อมูล ได้
    : แต่ละช่องรายงาน สามารถกดปุ่ม เพื่อ copy ข้อมูล
    : สามารถเพิ่ม Comment หากต้องการ
    : การบันทึก KTB HCV ใช้เพื่อเบิกจ่าย ตามระบบ สปสช โปรดบันทึกทุกรายให้ถูกต้อง
</pre>
              <div class="row">
                <div class="col-lg-12">
                  <hr>
                  <div>
                    <h5 class="text-bold text-primary">แสดง/ซ่อน Column</h5>
                    <!-- <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="0">No</button> -->
                    <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="1">ที่มา</button>
                    <!-- <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="2">ที่มา</button> -->
                    <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="3">Type</button>
                    <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="4">Source</button>
                    <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="5">CBS</button>
                    <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="6">UIC</button>
                    <!-- <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="7">UIC</button> -->
                    <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="8">count</button>
                    <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="9">KP</button>
                    <!-- <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="10">KP</button> -->
                    <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="11">Sub KP</button>
                    <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="12">Firstname</button>
                    <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="13">Lastname</button>
                    <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="14">Phone</button>
                    <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="15">Service_Date</button>
                    <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="16">ระยะ (วัน)</button>
                    <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="17">อาชีพ</button>
                    <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="18">สิทธิรักษา</button>
                    <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="19">HIV</button>
                    <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="20">Syphilis</button>
                    <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="21">HCV</button>
                    <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="22">CT</button>
                    <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="23">NG</button>
                    <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="24">ID_Card</button>
                    <!-- <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="25">ID_Card</button> -->
                    <!-- <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="26">nap_code</button> -->
                    <!-- <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="27">nap_comment</button> -->
                    <!-- <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="28">NAP_Code</button> -->
                    <!-- <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="29">NAP_Comment</button> -->
                    <!-- <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="30">NAP_Action</button> -->
                    <!-- <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="31">NAP_Staff</button> -->
                    <!-- <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="32">NAP_Date</button> -->
                    <!-- <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="33">Source_ID</button> -->
                    <!-- <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="34">BirthDay</button> -->
                    <!-- <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="35">thisRowId</button> -->
                    <!-- <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="36">location</button> -->
                  </div>
                  <hr>

                  <div class="table-responsive">
                    <table id="nhsoListTable" class="table text-center display table-hover">
                      <thead>
                        <tr>
                          <th>No</th>
                          <th>ที่มา</th>
                          <th>ที่มา</th>
                          <th>Type</th>
                          <th>Source</th>
                          <th>CBS</th>
                          <th>UIC</th>
                          <th>UIC</th>
                          <th>count</th>
                          <th>KP</th>
                          <th>KP</th>
                          <th>Sub KP</th>
                          <th>Firstname</th>
                          <th>Lastname</th>
                          <th>Phone</th>
                          <th>Service_Date</th>
                          <th>ระยะ (วัน)</th>
                          <th>อาชีพ</th>
                          <th>สิทธิรักษา</th>
                          <th>HIV</th>
                          <th>Syphilis</th>
                          <th>HCV</th>
                          <th>CT</th>
                          <th>NG</th>
                          <th>ID_Card</th>
                          <th>ID_Card</th>
                          <th>hcv_nap_code</th>
                          <th>hcv_nap_comment</th>
                          <th style='background:#87ceeb;border:1px solid #EEE;text-align:center;'>KTB_HCV_Comment</th>
                          <th style='background:#87ceeb;border:1px solid #EEE;text-align:center;'>KTB_HCV_Action</th>
                          <th style='background:#87ceeb;border:1px solid #EEE;text-align:center;'>KTB_HCV_Staff</th>
                          <th style='background:#87ceeb;border:1px solid #EEE;text-align:center;'>KTB_HCV_Date</th>
                          <th>Source_ID</th>
                          <th>BirthDay</th>
                          <th>thisRowId</th>
                          <th>location</th>
                        </tr>
                      </thead>
                      <tfoot>
                        <tr>
                          <th>No</th>
                          <th>ที่มา</th>
                          <th>ที่มา</th>
                          <th>Type</th>
                          <th>Source</th>
                          <th>CBS</th>
                          <th>UIC</th>
                          <th>UIC</th>
                          <th>count</th>
                          <th>KP</th>
                          <th>KP</th>
                          <th>Sub KP</th>
                          <th>Firstname</th>
                          <th>Lastname</th>
                          <th>Phone</th>
                          <th>Service_Date</th>
                          <th>ระยะ (วัน)</th>
                          <th>อาชีพ</th>
                          <th>สิทธิรักษา</th>
                          <th>HIV</th>
                          <th>Syphilis</th>
                          <th>HCV</th>
                          <th>CT</th>
                          <th>NG</th>
                          <th>ID_Card</th>
                          <th>ID_Card</th>
                          <th>hcv_nap_code</th>
                          <th>hcv_nap_comment</th>
                          <th style='background:#87ceeb;border:1px solid #EEE;text-align:center;'>KTB_HCV_Comment</th>
                          <th style='background:#87ceeb;border:1px solid #EEE;text-align:center;'>KTB_HCV_Action</th>
                          <th style='background:#87ceeb;border:1px solid #EEE;text-align:center;'>KTB_HCV_Staff</th>
                          <th style='background:#87ceeb;border:1px solid #EEE;text-align:center;'>KTB_HCV_Date</th>
                          <th>Source_ID</th>
                          <th>BirthDay</th>
                          <th>thisRowId</th>
                          <th>location</th>
                        </tr>
                      </tfoot>
                      <tbody>
                        <?php
                        $no = 0;
                        $kpList = ['MSM', 'MSW', 'TG', 'TGM', 'TGSW', 'PWID-Male', 'PWID-Female', 'FSW', 'Male', 'Female'];

                        // 
                        $eCascadeUnDone = false;
                        $period = 'all';

                        if (isset($_GET['start'])) $eCascadeUnDone = false;

                        $startDate = $_GET['start'];
                        $endDate = $_GET['end'] . " 23:59:59";

                        if (!isset($_GET['start'])) $startDate = checkQuarter()['this_month_start'];
                        if (!isset($_GET['end'])) $endDate = date('Y-m-d') . " 23:59:59";

                        if ($_GET['period'] == 'all') {
                          $startDate = '2014-10-01';
                          $endDate = date('Y-m-d H:i:s');
                        }

                        if (isset($_GET['ecascade']) && $_GET['ecascade'] == true) {
                          $eCascadeUnDone = true;
                        }
                        
                        $check_fy = $_GET['select_fy'] ?? '2026';

                        $datefilter = selectDatabase("nhso_config", [], ["year" => ["=", checkQuarter($check_fy)['this_fy']]])->fetch()->nhso_clinic_start ?? '2021-07-27';

                        $start_query = checkQuarter($check_fy)['q1_start'];
                        $end_query = checkQuarter($check_fy)['q4_end'];

                        $clinicDatas = caseHCVForNap($conn, $eCascadeUnDone, $period, $check_fy, $datefilter, $start_query, $end_query);

                        $filter_data = array_filter($clinicDatas, function ($item) use ($startDate, $endDate) {
                          return $item['service_date'] >= $startDate && $item['service_date'] < $endDate;
                        });

                        // dd($filter_data);

                        $data_count = count($filter_data);

                        if ($data_count > 5000) {
                          $toomuch = 'hidden';
                          echo "<label class='alert alert-danger'>ข้อมูลมากเกินไปที่จะแสดง ($data_count) โปรดกด Export Excel</label>";
                        }

                        foreach ($filter_data as $key => $item) {

                          if (in_array($item['kp'], $kpList) && !in_array(substr($item['id_card'], 0, 1), [0, 6, 7])) {
                            // if (!in_array($item['uic'], $uicLists)) {

                            $source = '';
                            $kp = '';

                            $no++;

                            $service_date = dateOnly($item['service_date']);
                            $ktb_staff = $item['nap_staff'] ?? '';
                            $ktb_date = $item['nap_date'] ? dateOnly($item['nap_date']) : '';

                            $id_card = checkPID($item['id_card']) ? labelbig($item['id_card'], 'success') : labelbig($item['id_card'], 'danger');

                            $source = in_array($item['source'], ['hcv', 'testing']) ? labelbig('Clinic', 'maroon') : $source;

                            $kp = labelbig($item['kp']);
                            $kp = $item['kp'] == 'MSM' ? labelbig($item['kp'], 'primary') : $kp;
                            $kp = $item['kp'] == 'MSW' ? labelbig($item['kp'], 'teal') : $kp;
                            $kp = $item['kp'] == 'TG' ? labelbig($item['kp'], 'warning') : $kp;
                            $kp = $item['kp'] == 'TGSW' ? labelbig($item['kp'], 'purple') : $kp;
                            $kp = $item['kp'] == 'FSW' ? labelbig($item['kp'], 'maroon') : $kp;

                            // ตรวจสอบสถานะการบันทึก KTB จาก nap_status
                            $ktb_status = $item['nap_status'] == 'true' ? 'checked' : '';
                            $readonly = $item['nap_status'] == 'true' ? 'readonly' : '';
                            $readonlyColor = $item['nap_status'] == 'true' ? '#DDD' : '#FFF';
                            $disabled = '';

                            $input_id = 'Clinic_' . $item['source'] . '_' . $item['id'] . '_' . $item['id_card'];
                            $checkBoxId = $input_id . '_checkbox';
                            $commentId = $input_id . '_cmd';
                            $service_date_copy = thaidate($item['service_date'], '/');

                            $hiv_result = testColor($item['hiv_result']);
                            $sti_result = testColor($item['sti_result']);

                            $ct_result = testColor($item['ct_result']);
                            $ng_result = testColor($item['ng_result']);

                            $hcv = '-';
                            if ($item['hcv_result'] == 'Anti-HCV Non-reactive' || $item['hcv_result'] == 'Anti-HCV Negative') $hcv = 'N';
                            if ($item['hcv_result'] == 'Anti-HCV Reactive' || $item['hcv_result'] == 'Anti-HCV Positive') $hcv = 'R';
                            if ($item['hcv_result'] == 'Anti-HCV Invalie') $hcv = 'I';
                            $hcv_result = testColor($hcv);

                            $type = labelbig($item['clinic_type'], 'primary');
                            if ($item['clinic_type'] == 'DIC') $type = labelbig($item['clinic_type'], 'teal');

                            $recruit_array = $item['cbs'] ? explode(',', $item['cbs']) : ['', ''];

                            $recruit_type = '';
                            $recruit_cbs = '';

                            if ($recruit_array[0] != 'walk in') {
                              $recruit_type = $recruit_array[0];
                              $recruit_cbs = $recruit_array[1];
                            }

                            if ($item['cbs'] == 'walk in') {
                              $recruit_type = 'walk in';
                              $recruit_cbs = '';
                            }

                            if ($recruit_type == 'reach') $recruit_type = labelbig($recruit_type, 'info', '1.2rem');
                            if ($recruit_type == 'retain') $recruit_type = labelbig($recruit_type, 'maroon', '1.2rem');
                            if ($recruit_type == 'walk in') $recruit_type = labelbig($recruit_type, 'purple', '1.2rem');

                            $sub_kp = $item['sub_kp'] ? implode(',', json_decode($item['sub_kp'])) : '';

                            $id_card_id = 'id_card_' . $input_id;

                            echo "
                              <tr class='{$toomuch}'>
                                  <td class='text-center'>$no</td>
                                  <td class='text-center'>{$source}</td>
                                  <td>{$item['source']}</td>
                                  <td>{$type}</td>
                                  <td>{$recruit_type}</td>
                                  <td>{$recruit_cbs}</td>
                                  " . tdCopy($item['uic'], $item['uic'], '', 'text-center', 'nowrap') . "
                                  <td>{$item['uic']}</td>
                                  <td>{$item['uic_count']}</td>
                                  <td>{$kp}</td>
                                  <td>{$item['kp']}</td>
                                  <td>{$sub_kp}</td>
                                  " . tdCopy($item['firstname'], $item['firstname'], '', 'text-center', 'nowrap') . "
                                  " . tdCopy($item['lastname'], $item['lastname'], '', 'text-center', 'nowrap') . "
                                  " . tdCopy($item['phone'], $item['phone'], '', 'text-center', 'nowrap') . "
                                  " . tdCopy($service_date_copy, $service_date, '', 'text-center', 'nowrap') . "
                                  <td>{$item['formLastTest']}</td>
                                  <td>{$item['occupation']}</td>
                                  <td>{$item['healthcare']}</td>
                                  <td>{$hiv_result}</td>
                                  <td>{$sti_result}</td>
                                  <td>{$hcv_result}</td>
                                  <td>{$ct_result}</td>
                                  <td>{$ng_result}</td>
                                  " . tdCopy($item['id_card'], $id_card, '', 'text-center', "nowrap id='$id_card_id'") . "
                                  <td>{$item['id_card']}</td>

                                  <td>{$item['nap_code']}</td>
                                  <td>{$item['nap_comment']}</td>
                                  <td style='background:#87ceeb;border:1px solid #EEE;text-align:center;'><input {$readonly} type='text' id='{$commentId}' class='text-center ktb_code_input form-control' style='width:100px;background:{$readonlyColor};' value='{$item['nap_comment']}'></td>
                                  <td class='text-center' style='background:#87ceeb;border:1px solid #EEE;text-align:center;'>
                                  <div style='position: relative;' id='{$input_id}_checkbox_container'>
                                    <div class='mt-ios' style='font-size:7px;'> 
                                    <input {$disabled} id='{$checkBoxId}' type='checkbox' onclick='updateKtbStatus(this)' {$ktb_status}/>
                                    <label style='margin:0px;' for='{$checkBoxId}'></label>
                                    </div>
                                  </div>
                                  </td>
                                  <td style='background:#87ceeb;border:1px solid #EEE;text-align:center;'>{$ktb_staff}</td>
                                  <td style='background:#87ceeb;border:1px solid #EEE;text-align:center;'>{$ktb_date}</td>
                                  
                                  <td class='text-center'>{$item['id']}</td>
                                  <td class='text-center'>{$item['birth_day']}</td>
                                  <td class='text-center'>{$input_id}</td>
                                  <td class='text-center'>{$item['location']}</td>
                                </tr> ";
                            // }
                          }
                        }
                        ?>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>

        </div>
        <!-- modal -->
        <div class="modal modal-gray fade" id="modal-default" tabindex="-1">
          <div class="modal-dialog">
            <div class="modal-content ">

            </div>
          </div>
        </div>
        <!-- /modal -->
      </div>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->

  <?php include '../layouts/footer.php' ?>
  <?php // include '../layouts/controlslidebar.php' 
  ?>

  <!-- /.control-sidebar -->
  <div class="control-sidebar-bg"></div>
</div>
<!-- ./wrapper -->

<?php include '../layouts/javascript.php' ?>
<script>
  var selected = [];

  const currentDate = new Date();
  // Setup - add a text input to each footer cell
  $('#nhsoListTable tfoot th').each(function() {
    var title = $(this).text();
    if (title != '') {
      $(this).html('<input type="text" style="width:100%" />');
    } else {
      $(this).html('');
    }
    $('[data-toggle="popover"]').popover();
  });

  var table = $('#nhsoListTable').DataTable({
    // sDom: 'C<"clear">lfrtip',
    autoWidth: false,
    pageLength: 10,
    paging: true,
    columnDefs: [{
      "targets": [2, 7, 10, 25, 26, 27, 34],
      "visible": false,
      "searchable": false,
    }, ],
    order: [
      [31, 'asc'],
      [0, 'desc'],
      [24, 'desc'],
    ],
    dom: 'Bfrtip',
    "buttons": [
      {
        extend: 'excel',
        text: 'Excel',
        title: null, // ✅ ไม่ใส่ title แถวแรก
        filename: 'hcv_enter_nap_export_' + currentDate.getFullYear() + '_' + ("0" + (currentDate.getMonth() + 1)).slice(-2) + '_' + ("0" + currentDate.getDate()).slice(-2) + '_' + ("0" + currentDate.getHours()).slice(-2) + ("0" + currentDate.getMinutes()).slice(-2) + ("0" + currentDate.getSeconds()).slice(-2),
      },
      'pageLength'
      // 'copy', 'excel', 'print', 'pageLength'
    ],
  });

  $('#nhsoListTable tbody').on('click', 'tr', function() {
    var id = this.id;
    var index = $.inArray(id, selected);

    if (index === -1) {
      selected.push(id);
    } else {
      selected.splice(index, 1);
    }

    $(this).toggleClass('selected');
  });

  // Apply the search
  table.columns().every(function() {
    var that = this;

    $('input', this.footer()).on('keyup change', function() {
      if (that.search() !== this.value) {
        that
          .search(this.value)
          .draw();
      }
    });
  });

  $('#nhsoListTable tfoot tr').appendTo('#nhsoListTable thead');



  function updateKtbStatus(e) {

    // const allData = table.rows().data();

    // console.log(e.id)

    let dataForUpdate = [];

    table.rows().data().filter(data => {

      if (e.id == data[34] + '_checkbox') {
        dataForUpdate = data
      }
    })

    if (dataForUpdate.length > 0) {

      let case_id = dataForUpdate[34]

      const source = dataForUpdate[2]
      const source_id = dataForUpdate[32]
      const uic = dataForUpdate[7]
      const kp = dataForUpdate[11]
      const id_card = $(`#id_card_${case_id}`).text()
      const nap_code = ''
      const nap_comment = $(`#${case_id}_cmd`).val()
      const nap_status = e.checked
      const nap_staff = $('#userName').text()
      const nap_date = moment().format("YYYY-MM-DD HH:mm:ss")
      const input_id = case_id

      const data = {
        year: '2026',
        type: 'Clinic',
        source: source,
        source_id: source_id,
        uic: uic,
        kp: kp,
        id_card: id_card,
        nap_code: nap_code,
        nap_comment: nap_comment,
        nap_status: nap_status,
        nap_staff: nap_staff,
        nap_date: nap_date,
        input_id: input_id
      };

      $.ajax({
        url: '../services/ajaxNHSO_NAP.php',
        type: 'POST',
        data: data,
        success: function(data) {
          const response = JSON.parse(data)
          table.row(dataForUpdate[0] - 1).every((rowIdx) => {
            if (response.data.nap_status === 'true' || response.data.nap_status === true) {

              // sweet alert
              Swal.fire({
                position: 'top-end',
                target: `#${input_id}_checkbox_container`,
                icon: 'success',
                title: 'บันทึก KTB สำเร็จ',
                customClass: {
                  container: 'ktb-position-absolute'
                },
                showConfirmButton: false,
                toast: true,
                timer: 2000
              })

              table.cell(rowIdx, 30).data(nap_staff).draw('page')
              table.cell(rowIdx, 31).data(nap_date).draw('page')
              $(`#${response.data.input_id}_cmd`).prop('readonly', true)
              $(`#${response.data.input_id}_cmd`).css('background', '#DDD')
            } else {
              table.cell(rowIdx, 30).data('').draw('page')
              table.cell(rowIdx, 31).data('').draw('page')
              $(`#${response.data.input_id}_cmd`).val('')
              $(`#${response.data.input_id}_cmd`).prop('readonly', false)
              $(`#${response.data.input_id}_cmd`).css('background', '#FFF')
            }
          })
        }
      });
    }
  }

  let default_start = moment().startOf('month').format('YYYY-MM-DD')
  let default_end = moment().format('YYYY-MM-DD')

  if (getRequests().period == 'all') {
    default_start = '2014-10-01'
    default_end = moment().format('YYYY-MM-DD HH:mm:ss')
  }

  if (getRequests().start) default_start = getRequests().start
  if (getRequests().end) default_end = getRequests().end

  $("#select_fy").on('change', (e) => {

    let select_fy = e.target.value
    if(e.target.value == '') select_fy = '2026'

    let filter_link = $("#filter_link").attr('href')
    let filter_nap = $("#filter_nap").attr('href')

    if(!getRequests().start) {
      filter_link += `?start=${default_start}&end=${default_end}`
      filter_nap += `&start=${default_start}&end=${default_end}`
    }

    filter_link += `&select_fy=${select_fy}`
    filter_nap += `&select_fy=${select_fy}`

    $("#filter_link").attr("href", filter_link.toString());
    $("#filter_nap").attr("href", filter_nap.toString());

  })

  $(`#date_range`).flatpickr({
    mode: "range",
    // minDate: "today",
    dateFormat: "Y-m-d",
    defaultDate: [default_start, default_end],

    plugins: [
      ShortcutButtonsPlugin({
        button: [{
            label: "Month"
          },
          {
            label: "Quarter"
          },
          {
            label: "FY"
          },
          {
            label: "last FY"
          },
        ],
        // label: "or",
        onClick: (index, fp) => {

          let start = moment().startOf('month').format('YYYY-MM-DD')
          let end = moment().endOf('month').format('YYYY-MM-DD')

          if (index == 0) {
            start = moment().startOf('month').format('YYYY-MM-DD')
            end = moment().endOf('month').format('YYYY-MM-DD')
          }

          if (index == 1) {
            start = moment().startOf('quarter').format('YYYY-MM-DD')
            end = moment().endOf('quarter').format('YYYY-MM-DD')
          }

          if (index == 2) {
            start = moment().startOf('year').subtract(3, 'months').format('YYYY-MM-DD')
            end = moment().endOf('year').subtract(3, 'months').format('YYYY-MM-DD')

            if (moment().month() > 8) {
              start = moment().add(1, 'y').startOf('year').subtract(3, 'months').format('YYYY-MM-DD')
              end = moment().add(1, 'y').endOf('year').subtract(3, 'months').format('YYYY-MM-DD')
            }
          }

          if (index == 3) {
            start = moment().subtract(1, 'y').startOf('year').subtract(3, 'months').format('YYYY-MM-DD')
            end = moment().subtract(1, 'y').endOf('year').subtract(3, 'months').format('YYYY-MM-DD')

            if (moment().month() > 8) {
              start = moment().startOf('year').subtract(3, 'months').format('YYYY-MM-DD')
              end = moment().endOf('year').subtract(3, 'months').format('YYYY-MM-DD')
            }
          }

          fp.setDate([start, end]);
          $('#filter_link').attr("href", `./nhsoForHCV.php?start=${start}&end=${end}`);
        }
      })
    ],

    // enable: [
    //   {
    //     from: moment().subtract(7, 'days').format('YYYY-MM-DD'),
    //     to: moment().add(7, 'd').format('YYYY-MM-DD')
    //   },
    // ],
    onChange: (selectedDates, dateStr, instance) => {
      if (selectedDates.length == 2) {

        let start = moment(selectedDates[0]).format('YYYY-MM-DD')
        let end = moment(selectedDates[1]).format('YYYY-MM-DD')

        $('#filter_link').attr("href", `./nhsoForHCV.php?start=${start}&end=${end}`);
        // update input for start_date and end_date
        // this.fetchTestMeNowLists(moment(selectedDates[0]).format('YYYY-MM-DD'), moment(selectedDates[1]).format('YYYY-MM-DD'))
      }
    }
  });

  function button_toggle(e) {

    // console.log(e.target)

    if (e.target.classList.contains("btn-default")) {
      e.target.classList.remove("btn-default")
      e.target.classList.add("btn-primary")
    } else {
      e.target.classList.remove("btn-primary")
      e.target.classList.add("btn-default")
    }

    var column = table.column(e.target.dataset.column);

    // Toggle the visibility
    column.visible(!column.visible());
  }
</script>
<?php include '../layouts/end.php' ?>
