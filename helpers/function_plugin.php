<?php

use Carbon\Carbon;
use GuzzleHttp\Client;
// session_start();
// require '../connect.php';
// require '../session_setting.php';

//flash message session
function labelbig($input, $color = "default", $size = "1.4rem")
{
  $label = '<span class="label label-' . $color . '" style = "font-size: ' . $size . ';font-weight: normal;">' . $input . '</span>';
  return $label;
}

function getAge($birthDate = '')
{

  if ($birthDate == '') $birthDate = date('Y-m-d H:i:s');

  $interval = date_diff(date_create(), date_create($birthDate));

  return explode(',', $interval->format("%Y,%M,%d,%H,%i,%s"));
}

function calculateAge($dateBirth, $dateCal = '')
{

  if ($dateCal == '') $dateCal = date('Y-m-d H:i:s');

  $diff = abs(strtotime($dateCal) - strtotime($dateBirth));

  $years = floor($diff / (365 * 60 * 60 * 24));
  $months = floor(($diff - $years * 365 * 60 * 60 * 24) / (30 * 60 * 60 * 24));
  $days = floor(($diff - $years * 365 * 60 * 60 * 24 - $months * 30 * 60 * 60 * 24) / (60 * 60 * 24));

  $data = [];

  $data['years'] = $years;
  $data['months'] = $months;
  $data['days'] = $days;

  return $data;
}

function uicBirthDate($uic)
{
  $birthDate = (int)substr($uic, -2) < (date('Y') + 543 - 2500) ? ((int)substr($uic, -2) - 543 + 2500) . '-' . (int)substr($uic, -4, 2) . '-' . (int)substr($uic, -6, 2) : ((int)substr($uic, -2) - 543 + 2500) . '-' . (int)substr($uic, -4, 2) . '-' . (int)substr($uic, -6, 2);

  return $birthDate;
}

function uicBirthDateThai($uic)
{
  $dayDigit = (int)substr($uic, -6, 2);
  $monthDigit = (int)substr($uic, -4, 2);
  $yearDigit = (int)substr($uic, -2);

  $thaiThisYear2Digit = date('Y') + 543 - 2500;

  if ($yearDigit < $thaiThisYear2Digit) {
    $thaiFullYear = 2500 + $yearDigit;
  } else {
    $thaiFullYear = 2400 + $yearDigit;
  }

  return "$dayDigit/$monthDigit/$thaiFullYear";
}

// function dd($var)
// {
//   dump($var);
//   die();
// }

//Buddist Calendar $d format yyyy-mm-dd from mysql
function thaidate($d, $split = '-')
{
  if ($d == 'NA') {
    return 'NA';
  }
  if ($d == 0) {
    return '';
  }
  $datestring = strtotime($d);
  $date = date('d', $datestring);
  $month = date('m', $datestring);
  $year = date('Y', $datestring) + 543;

  return $date . $split . $month . $split . $year;
}

function thaidateNHSO($d)
{
  if ($d == 'NA') {
    return 'NA';
  }
  if ($d == 0) {
    return '';
  }

  $datestring = strtotime($d);
  $date = date('d', $datestring);
  $month = date('m', $datestring);
  $year = date('Y', $datestring) + 543;

  return "{$year}{$month}{$date}";
}

function lightGallery($pictureLinks, $count = false)
{
  $pictures = "";
  $countPic = 0;

  $picCheck = @unserialize($pictureLinks);

  if ($picCheck) {
    foreach (unserialize($pictureLinks) as $pic) {
      if ($pic != 4) {
        $pictures .= "<a href='$pic'><img style='border:1px solid' src='$pic' height='100px'></a>";
        $countPic++;
      }
    }
  }

  if ($count) {
    return $countPic;
  }

  return $pictures;
}

function listPicture($pictureLinks, $count = false)
{
  $pictures = "";
  $countPic = 0;

  if (is_array($pictureLinks)) {
    foreach ($pictureLinks as $pic) {
      if ($pic != 4) {
        $pictures .= "<a target='_blank' href='$pic'><img style='border:1px solid' src='$pic' height='50px'></a>";
        $countPic++;
      }
    }

    if ($count) return $countPic;
    return $pictures;
  }

  $picCheck = @unserialize($pictureLinks);

  if ($picCheck) {
    foreach (unserialize($pictureLinks) as $pic) {
      if ($pic != 4) {
        $pictures .= "<a target='_blank' href='$pic'><img style='border:1px solid' src='$pic' height='50px'></a>";
        $countPic++;
      }
    }

    if ($count) return $countPic;
    return $pictures;
  }
}

function dateColorNotify($date)
{
  if (!$date || $date == '' || $date == '0000-00-00' || $date == '0000-00-00 00:00:00') return '';

  if (strtotime($date) >= strtotime(Date("Y-m-d")) + 172800) {
    return labelbig(dateOnly($date), 'success');
  } elseif (strtotime($date) >= strtotime(Date("Y-m-d")) + 86400) {
    return labelbig(dateOnly($date), 'teal');
  } elseif (strtotime($date) >= strtotime(Date("Y-m-d"))) {
    return labelbig(dateOnly($date), 'info');
  } elseif (strtotime($date) >= strtotime(Date("Y-m-d")) - 259200) {
    return labelbig(dateOnly($date), 'warning');
  } else {
    return labelbig(dateOnly($date), 'danger');
  }
}

//age calculation
/*
      birthDay format dd-mm-yyyy
    */
function calAge($dateOfBirth)
{
  $date = new DateTime($dateOfBirth);
  $now = new DateTime();
  $interval = $now->diff($date);
  return $interval->y;
}

//no 0 Date
function datenozero($d)
{
  if ($d == 0) {
    return $d = '';
  }
  return $d;
}

function thaiMonth($month)
{
  $monthLists = ['มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน', 'กรกฎาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม'];
  return $monthLists[$month - 1];
}

function time_diff($start, $end)
{
  if ($start == 0) return 0;

  $datestart = strtotime($start);
  $dateend = strtotime($end);
  $different = ($dateend - $datestart);
  return $different;
}

function days_different($start, $end)
{
  if ($start == 0) {
    return $result = '';
  }
  $datestart = strtotime($start);
  $dateend = strtotime($end);
  $different = ($dateend - $datestart) / 86400;
  return $different;
}

function date_different($start, $end = '')
{
  if ($start == 0 || $start == '0000-00-00 00:00:00') {
    return $result = '';
  }

  $start = dateOnly($start);

  $end = $end == '' ? date('Y-m-d') : $end;

  $datestart = strtotime($start);
  $dateend = strtotime($end);
  $date_different = ($dateend - $datestart);
  $years_dif = date('Y', $date_different) - 1970;
  $months_dif = date('m', $date_different) - 01;
  $days_diff = date('d', $date_different);

  $different = $years_dif . ' ปี ' . $months_dif . ' เดือน ' . $days_diff . ' วัน';

  if ($date_different < 0) return 'invalid';

  return $different;
}

function dateDiffForEcascade($start, $end = '')
{
  if ($start == 0) {
    return 'ไม่เคยตรวจมาก่อน';
  }

  $end = $end == '' ? date('Y-m-d') : $end;

  $datestart = strtotime($start);
  $dateend = strtotime($end);
  $date_different = ($dateend - $datestart);

  // return $date_different;

  if ($date_different > '31557600') {
    return 'เคยตรวจมาก่อน มากกว่า 1 ปี';
  }
  if ($date_different >= '15778800') {
    return 'เคยตรวจ ช่วง 6 เดือน - 1 ปี ที่ผ่านมา';
  }
  if ($date_different >= '7889400‬') {
    return 'เคยตรวจ ช่วง 3 - 6 เดือน ที่ผ่านมา';
  }
  if ($date_different < '7889400‬') {
    return 'เคยตรวจ ไม่เกิน 3 เดือนที่ผ่านมา';
  }

  return 'ไม่เคยตรวจมาก่อน';
}

function time_different($start, $end)
{
  if ($start == 0) {
    return $result = '';
  }
  $datestart = strtotime($start);
  $dateend = strtotime($end);
  $date_different = ($dateend - $datestart);
  $hour_diff = intdiv($date_different, 3600);
  $minutes_diff = intdiv($date_different % 3600, 60);
  $seconds_diff = $date_different % 60;

  $different = $hour_diff . ' ชั่วโมง ' . $minutes_diff . ' นาที ' . $seconds_diff . ' วินาที';
  return $different;
}

//getdate
function getdateinput($inputdate)
{
  $date_array = explode("-", $inputdate);
  $new_date_array = array($date_array[2], $date_array[1], $date_array[0]);
  $new_date = implode("-", $new_date_array);
  return $new_date;
}

//getdateinputreach
function getDateTimeInput($inputdate, $inputtime)
{
  $date_array = explode("-", $inputdate);
  $new_date_array = array($date_array[2], $date_array[1], $date_array[0]);
  $new_date = implode("-", $new_date_array);
  $time_arry = explode(" ", $inputtime);
  $time_arry_split = explode(":", $time_arry[0]);
  if ($time_arry[1] == "PM") {
    if ($time_arry_split[0] != 12) {
      $time_arry_split[0] += 12;
    }
  }
  if ($time_arry[1] == "AM") {
    if ($time_arry_split[0] == 12) {
      $time_arry_split[0] = '00';
    }
  }
  $new_time_array_merge = array($time_arry_split[0], $time_arry_split[1], '00');
  $new_time = implode(":", $new_time_array_merge);
  $dateresult = $new_date . " " . $new_time;
  return $dateresult;
}

//date and time flatpickr
function getDateTimeFlatpickr($inputDate, $inputTime = '00:00:00')
{

  $date = $inputDate . ' ' . $inputTime;

  return $date;
}

//findtesteddate
function findtesteddate($t, $r)
{
  $date_array = explode("-", $r);
  $d_test = 1;

  if ($t == '3month') {
    if ($date_array[1] <= 3) {
      $m_test = 9 + $date_array[1];
      $y_test = $date_array[0] - 1;
    } else {
      $m_test = $date_array[1] - 3;
      $y_test = $date_array[0];
    }
  } else if ($t == '6month') {
    if ($date_array[1] <= 6) {
      $m_test = 6 + $date_array[1];
      $y_test = $date_array[0] - 1;
    } else {
      $m_test = $date_array[1] - 6;
      $y_test = $date_array[0];
    }
  } else if ($t == '1year') {
    $m_test = $date_array[1];
    $y_test = $date_array[0] - 1;
  } else {
    $m_test = $date_array[1];
    $y_test = $date_array[0] - 2;
  }

  $new_date_array = array($y_test, $m_test, $d_test);
  $new_date = implode("-", $new_date_array);
  return $new_date;
}

//gettime
function gettimeinput($inputtime)
{
  $time_arry = explode(" ", $inputtime);
  $time_arry_split = explode(":", $time_arry[0]);
  if ($time_arry[1] == "PM") {
    if ($time_arry_split[0] != 12) {
      $time_arry_split[0] += 12;
    }
  }
  if ($time_arry[1] == "AM") {
    if ($time_arry_split[0] == 12) {
      $time_arry_split[0] = '00';
    }
  }
  $new_time_array_merge = array($time_arry_split[0], $time_arry_split[1], '00');
  $new_time = implode(":", $new_time_array_merge);
  return $new_time;
}

function getTimeSql($sqlDate)
{
  $time_arry = explode(" ", $sqlDate);
  return $time_arry[1];
}

//date yyyy-mm-dd to dd-mm-yyyy
function dateSqlToNormal($inputdate)
{
  $date_array_all = explode(" ", $inputdate);
  $date_array = explode("-", $date_array_all[0]);
  $new_date_array = array($date_array[2], $date_array[1], $date_array[0]);
  $new_date = implode("-", $new_date_array);
  return $new_date;
}



//date for fetch close clinic
function mixCloseDate($d, $m, $y)
{
  $datestring = strtotime($y . '-' . $m . '-' . $d);
  $date = date('Y-m-d', $datestring);
  return $date;
}

//datetime cut
function dateOnly($d)
{
  if (!$d || $d == '' || $d == '0000-00-00 00:00:00') {
    return '';
  }

  $date_arry = explode(" ", $d);
  return $date_arry[0];
}

function birthDayNhso($d)
{
  $date_arry = explode(" ", $d);
  $birthDayArr = explode('-', $date_arry[0]);
  $birthDay = $birthDayArr[2] . $birthDayArr[1] . $birthDayArr[0];
  return $birthDay;
}

//shift 14 days
function add14days($d)
{
  $dateinput = strtotime($d) + 1209600; // + 14 วัน
  $result = date('Y-m-d', $dateinput);
  return $result;
}
//shift 7 days
function add7days($d)
{
  $dateinput = strtotime($d) + 604800; // + 7 วัน
  $result = date('Y-m-d', $dateinput);
  return $result;
}
//shift 1 day
function add1day($d)
{
  $dateinput = strtotime($d) + 86400; // + 1 วัน
  $result = date('Y-m-d', $dateinput);
  return $result;
}

//shift 15 days
function add15days($d)
{
  $dateinput = strtotime($d) + 1296000; // + 15 วัน
  $result = date('Y-m-d', $dateinput);
  return $result;
}

//shift 1 month
function add30days($d)
{
  $dateinput = strtotime($d) + 2592000; // + 30 วัน
  $result = date('Y-m-d', $dateinput);
  return $result;
}

//shift 45 days
function add45days($d)
{
  $dateinput = strtotime($d) + 3888000; // + 45 วัน
  $result = date('Y-m-d', $dateinput);
  return $result;
}

//shift 3 months
function add3month($d)
{
  $dateinput = strtotime($d) + 7776000; // + 90 วัน
  $result = date('Y-m-d', $dateinput);
  return $result;
}

//shift 6 months
function add6month($d)
{
  $dateinput = strtotime($d) + 15552000; // + 90 วัน
  $result = date('Y-m-d', $dateinput);
  return $result;
}

//shift days months
function shiftDays($date, $shift)
{
  $dateinput = strtotime($date) + (86400 * $shift); // + 90 วัน
  $result = date('Y-m-d', $dateinput);
  return $result;
}

//add 5 min for Line notify retain important
function mixDateAndTime($d, $t)
{
  $string = $d . ' ' . $t;
  $datetime = strtotime($string);
  $result = date('Y-m-d H:i:s', $datetime);
  return $datetime;
}

//subtract 10 min for Line notify retain important
function mixDateAndTimeSub10($d, $t)
{
  $string = $d . ' ' . $t;
  $datetime = strtotime($string) - 600;
  $result = date('Y-m-d H:i:s', $datetime);
  return $datetime;
}

//Generate care code
function careCode($d, $num)
{
  if ($num < 1) {
    $code = "";
  } else if ($num < 10) {
    $code = $d . '-000' . $num;
  } else if ($num < 100) {
    $code = $d . '-00' . $num;
  } else if ($num < 1000) {
    $code = $d . '-0' . $num;
  } else {
    $code = $d . '-' . $num;
  }
  return $code;
}

function color_status($data)
{
  if ($data == 'positive') {
    $status = labelbig($data, 'danger');
  } else {
    $status = labelbig($data, 'success');
  }
  return $status;
}


function checkLineSecret($action = 'search', $root = 1)
{

  if ($action == '') {
    $action = 'search';
  }

  if ($root == 0) {
    require 'connect.php';
  } else if ($root == 2) {
    require '../../connect.php';
  } else {
    require '../connect.php';
  }

  $check = $conn->prepare("SELECT * FROM linenotifysetting where action = :action");
  $check->bindParam(':action', $action);
  $check->execute();

  $data = $check->fetch();

  return $data;
}

function checkSecret($id = 1, $root = 1)
{

  if ($id < 1) {
    $id = 1;
  }

  if ($root == 0) {
    require 'connect.php';
  } else if ($root == 2) {
    require '../../connect.php';
  } else {
    require '../connect.php';
  }

  $check = $conn->prepare("SELECT * FROM linenotify where id = :id");
  $check->bindParam(':id', $id);
  $check->execute();

  $data = $check->fetch();

  return $data;
}

function checkPID($pid)
{
  if ($pid !== null && $pid !== '') {
    $pid = trim($pid);
    $pid = str_replace('-', '', $pid);
    $pid = str_replace(' ', '', $pid);
    if (strlen($pid) != 13) return false;
    for ($i = 0, $sum = 0; $i < 12; $i++)
      $sum += (int) ($pid[$i]) * (13 - $i);
    if ((11 - ($sum % 11)) % 10 == (int) ($pid[12])) return true;
  }
  return false;
}

function fetchNewCase($clinic_id, $uic, $queue)
{
  global $conn;
  $stmt = $conn->prepare("SELECT * FROM clinicnewcase WHERE clinic_id = ? and uic = ? and queue = ?");
  $stmt->execute([$clinic_id, $uic, $queue]);

  return $stmt;
}

function fetchClinicNewCaseByQueue($clinic_id, $queue)
{
  global $conn;
  $stmt = $conn->prepare("SELECT * FROM clinicnewcase WHERE clinic_id = ? and queue = ?");
  $stmt->execute([$clinic_id, $queue]);

  return $stmt;
}
function fetchClinicDoctor($clinic_id, $queue, $uic)
{
  global $conn;
  $stmt = $conn->prepare("SELECT * FROM clinicdoctor WHERE clinic_id = ? and queue = ? and uic = ? ");
  $stmt->execute([$clinic_id, $queue, $uic]);

  return $stmt;
}

function fetchClinicPharmacy($clinic_id, $queue, $uic)
{
  global $conn;
  $stmt = $conn->prepare("SELECT * FROM clinic_pharmacy WHERE clinic_id = ? and queue = ? and uic = ? ");
  $stmt->execute([$clinic_id, $queue, $uic]);

  return $stmt;
}

function fetchLabData($clinic_id, $queue, $uic, $type = 'clinic')
{
  global $conn;
  $stmt = $conn->prepare("SELECT 
    a.*, 
    b.*,
    c.lab_topic as late_lab_topic,
    c.lab_result_ng as late_lab_result_ng,
    c.lab_result_ct as late_lab_result_ct,
    c.lab_result_cd4 as late_lab_result_cd4,
    c.lab_result_vl as late_lab_result_vl,
    c.lab_date as late_lab_date,
    c.lab_report_staff as late_lab_report_staff,
    c.lab_report_time as late_lab_report_time,
    c.lab_confirm_staff as late_lab_confirm_staff,
    c.lab_confirm_time as late_lab_confirm_time,
    c.lab_files as late_lab_files,
    c.lab_remarks as late_lab_remarks,

    led.cliniclabresult_id as edit_cliniclabresult_id,
    led.uic as edit_uic,
    led.services_edited as edit_services_edited,
    led.hiv_test_alere_hiv_combo as edit_hiv_test_alere_hiv_combo,
    led.hiv_test_colloidal_gold_device as edit_hiv_test_colloidal_gold_device,
    led.hiv_test_sd_bioline as edit_hiv_test_sd_bioline,
    led.hiv_result as edit_hiv_result,
    led.hiv_test_alere_hiv_combo2 as edit_hiv_test_alere_hiv_combo2,
    led.hiv_test_colloidal_gold_device2 as edit_hiv_test_colloidal_gold_device2,
    led.hiv_test_sd_bioline2 as edit_hiv_test_sd_bioline2,
    led.hiv_result2 as edit_hiv_result2,
    led.tpha_result as edit_tpha_result,
    led.vdrl_result as edit_vdrl_result,
    led.cd4_result as edit_cd4_result,
    led.cd4_value as edit_cd4_value,
    led.cd4_percent as edit_cd4_percent,
    led.hcv_result as edit_hcv_result,
    led.hcv_rna_result as edit_hcv_rna_result,
    led.hcv_rna_value as edit_hcv_rna_value,
    led.hcv_doc_result as edit_hcv_doc_result,
    led.ct_result as edit_ct_result,
    led.ng_result as edit_ng_result,
    led.wet_smear_result as edit_wet_smear_result,
    led.gram_stain_result as edit_gram_stain_result,
    led.vl_result as edit_vl_result,
    led.vl_result_value as edit_vl_result_value,
    led.edited_staff as edit_edited_staff,
    led.edited_at as edit_edited_at
    
  FROM clinicnewcase a
  LEFT JOIN cliniclabresult b ON a.clinic_id = b.clinic_id AND a.queue = b.queue AND a.uic = b.uic
  LEFT JOIN cliniclabresult_edit led ON b.id = led.cliniclabresult_id and b.uic = led.uic
  LEFT JOIN late_lab_result c ON a.clinic_id = c.clinic_id AND a.queue = c.queue AND a.uic = c.uic
  WHERE a.clinic_id = ? and a.queue = ? and a.uic = ?");
  $stmt->execute([$clinic_id, $queue, $uic]);

  return $stmt;
}

function fetchLab($clinic_id, $uic, $queue)
{
  global $conn;

  $stmt = $conn->prepare("SELECT 
      a.*, 

      d.clinicQueue,
      d.queueStatus,
      d.queueCbs,
      d.queueUpdate,

      b.serviceProvide, 
      b.prep_taken_check, 
      b.prep_taken_route, 
      b.prep_location, 
      b.prep_code, 
      b.prep_provide, 
      b.prep_no_provide_reason, 
      b.prep_not_pass_criteria, 
      b.prep_today_route, 
      b.prep_service_step, 
      b.prep_service_visit, 
      b.prep_service_method, 
      b.prep_service_express, 
      b.prep_adherence, 
      b.prep_check_consent, 
      b.preConsent, 
      b.preComment,

      post.postStatus,
      phar.pharmacy_status,

      led.cliniclabresult_id as edit_cliniclabresult_id,
      led.uic as edit_uic,
      led.services_edited as edit_services_edited,
      led.hiv_test_alere_hiv_combo as edit_hiv_test_alere_hiv_combo,
      led.hiv_test_colloidal_gold_device as edit_hiv_test_colloidal_gold_device,
      led.hiv_test_sd_bioline as edit_hiv_test_sd_bioline,
      led.hiv_result as edit_hiv_result,
      led.hiv_test_alere_hiv_combo2 as edit_hiv_test_alere_hiv_combo2,
      led.hiv_test_colloidal_gold_device2 as edit_hiv_test_colloidal_gold_device2,
      led.hiv_test_sd_bioline2 as edit_hiv_test_sd_bioline2,
      led.hiv_result2 as edit_hiv_result2,
      led.tpha_result as edit_tpha_result,
      led.vdrl_result as edit_vdrl_result,
      led.cd4_result as edit_cd4_result,
      led.cd4_value as edit_cd4_value,
      led.cd4_percent as edit_cd4_percent,
      led.hcv_result as edit_hcv_result,
      led.hcv_rna_result as edit_hcv_rna_result,
      led.hcv_rna_value as edit_hcv_rna_value,
      led.hcv_doc_result as edit_hcv_doc_result,
      led.ct_result as edit_ct_result,
      led.ng_result as edit_ng_result,
      led.wet_smear_result as edit_wet_smear_result,
      led.gram_stain_result as edit_gram_stain_result,
      led.vl_result as edit_vl_result,
      led.vl_result_value as edit_vl_result_value,
      led.edited_staff as edit_edited_staff,
      led.edited_at as edit_edited_at
      

    FROM cliniclabresult a 
    LEFT JOIN cliniclabresult_edit led ON a.id = led.cliniclabresult_id and a.uic = led.uic and led.services_edited != '[]'
    LEFT JOIN clinicprecounseling b ON a.clinic_id = b.clinic_id and a.queue = b.queue AND a.uic = b.uic 
    LEFT JOIN clinicnewcase d ON d.clinic_id = a.clinic_id AND d.queue = a.queue
    LEFT JOIN clinicpostcounseling post ON post.clinic_id = a.clinic_id AND post.queue = a.queue
    LEFT JOIN clinic_pharmacy phar ON phar.clinic_id = a.clinic_id AND phar.queue = a.queue
    where a.clinic_id = ? and a.uic = ? AND a.queue = ? ");
  $stmt->execute([
    $clinic_id,
    $uic,
    $queue,
  ]);

  return $stmt;
}

function fetchClinicOperationTime($clinic_id = '', $uic = '', $start = '', $end = '')
{
  global $conn;

  if ($start != '' && $end != '') {
    $stmt = $conn->prepare("SELECT
    clg.location,
    clg.created_at as clinic_date_time,
    newcase.clinic_id, 
    newcase.queue,
    newcase.clinicQueue,
    newcase.uic,
    newcase.kp,
    newcase.counselor,
    newcase.testMeNowCode,
    newcase.love2testCode,
    newcase.rsatbookingCode,
    newcase.created_at as reg_started,
    newcase.bra_started as bra_started,
    newcase.bra_submitted as bra_submitted,
    pre.created_at as pre_started,
    pre.submitted_at as pre_submitted,
    lab.created_at as lab_started,
    lab.reported_at as lab_reported,
    lab.confirmed_at as lab_confirmed,
    post.created_at as post_started,
    post.submitted_at as post_submitted
  
     FROM clinicnewcase newcase
     LEFT JOIN cliniclog clg ON newcase.clinic_id = clg.id
     LEFT JOIN clinicprecounseling pre ON newcase.clinic_id = pre.clinic_id and newcase.queue = pre.queue
     LEFT JOIN cliniclabresult lab ON newcase.clinic_id = lab.clinic_id and newcase.queue = lab.queue
     LEFT JOIN clinicpostcounseling post ON newcase.clinic_id = post.clinic_id and newcase.queue = post.queue
     
     WHERE newcase.registered_at >= ? AND newcase.registered_at < ? 
     order by newcase.clinic_id ASC, newcase.queue ASC");

    $stmt->execute([$start, $end]);
  } else {

    if ($uic == '') {

      $stmt = $conn->prepare("SELECT 
  newcase.queue,
  newcase.clinicQueue,
  newcase.uic,
  newcase.kp,
  newcase.counselor,
  newcase.testMeNowCode,
  newcase.love2testCode,
  newcase.rsatbookingCode,
  newcase.created_at as reg_started,
  newcase.bra_started as bra_started,
  newcase.bra_submitted as bra_submitted,
  pre.created_at as pre_started,
  pre.submitted_at as pre_submitted,
  lab.created_at as lab_started,
  lab.reported_at as lab_reported,
  lab.confirmed_at as lab_confirmed,
  post.created_at as post_started,
  post.submitted_at as post_submitted

   FROM clinicnewcase newcase

   LEFT JOIN clinicprecounseling pre ON newcase.clinic_id = pre.clinic_id and newcase.queue = pre.queue
   LEFT JOIN cliniclabresult lab ON newcase.clinic_id = lab.clinic_id and newcase.queue = lab.queue
   LEFT JOIN clinicpostcounseling post ON newcase.clinic_id = post.clinic_id and newcase.queue = post.queue
   
   WHERE newcase.clinic_id = ? order by newcase.queue");

      $stmt->execute([$clinic_id]);
    } else {
      $stmt = $conn->prepare("SELECT 
    newcase.queue,
    newcase.clinicQueue,
    newcase.uic,
    newcase.kp,
    newcase.counselor,
    newcase.created_at as reg_started,
    newcase.bra_started as bra_started,
    newcase.bra_submitted as bra_submitted,
    pre.created_at as pre_started,
    pre.submitted_at as pre_submitted,
    lab.created_at as lab_started,
    lab.reported_at as lab_reported,
    lab.confirmed_at as lab_confirmed,
    post.created_at as post_started,
    post.submitted_at as post_submitted
  
     FROM clinicnewcase newcase
  
     LEFT JOIN clinicprecounseling pre ON newcase.clinic_id = pre.clinic_id and newcase.queue = pre.queue
     LEFT JOIN cliniclabresult lab ON newcase.clinic_id = lab.clinic_id and newcase.queue = lab.queue
     LEFT JOIN clinicpostcounseling post ON newcase.clinic_id = post.clinic_id and newcase.queue = post.queue
     
     WHERE newcase.clinic_id = ? AND newcase.uic = ? order by newcase.queue");

      $stmt->execute([$clinic_id, $uic]);
    }
  }

  return $stmt;
}

function checkFileCodeFormClinicNewCase($clinic_id, $file_code, $uic, $cbs)
{
  global $conn;

  $data = [$clinic_id, $uic];

  $stmt = $conn->prepare("SELECT * FROM clinicnewcase WHERE clinic_id = ? and uic = ? and (file_code != '' and file_code is not null)");
  $stmt->execute($data);

  $check = $stmt->fetch();

  if ($check) return $check->file_code;

  // if not found check in temp_file_code if found use it first
  $stmt = $conn->prepare("SELECT * FROM temp_file_code WHERE clinic_id = ? AND uic = ? AND status = 'prepare'");
  $stmt->execute([$clinic_id, $uic]);

  $temp_check = $stmt->fetch();

  if ($temp_check) return $temp_check->file_code;

  // send last file_code in clinicnewcase to check
  $stmt = $conn->prepare("SELECT * FROM clinicnewcase WHERE clinic_id = ? AND char_length(file_code) = 15 order by file_code DESC limit 1");
  $stmt->execute([$clinic_id]);

  $check = $stmt->fetch();

  if ($check) {

    $last_clinicnewcase_file_code = $check->file_code;

    if ($check->file_code >= $file_code) {

      $file_codes = explode('/', $last_clinicnewcase_file_code);
      $file_code_number = (int)substr($file_codes[0], 7, 5);
      $file_code_text = substr($file_codes[0], 0, 6);

      $year = date('Y') + 543 - 2500;

      if (userHasSiteName('rsat_bkk')) $file_code_year = $year - 59; // 64 is year 5
      if (userHasSiteName('rsat_ska')) $file_code_year = $year - 60; // 64 is year 4
      if (userHasSiteName('rsat_ubn')) $file_code_year = $year - 60; // 64 is year 4
      if (userHasSiteName('rsat_cbi')) $file_code_year = $year - 60; // 64 is year 4
      if (userHasSiteName('rsat_npt')) $file_code_year = $year - 66; // 66 is year 1
      if (userHasSiteName('rsat_nsn')) $file_code_year = $year - 63; // 64 is year 1

      $add_file_code = ($file_code_year * 100000) + ($file_code_number + 1);

      $file_code = $file_code_text . $add_file_code . '/' . $year;
    }
  }

  return check_temp_file_code($clinic_id, $file_code, $uic, $cbs);
}

function check_temp_file_code($clinic_id, $file_code, $uic, $cbs)
{

  global $conn;

  $valid_file_code = $file_code;
  $last_temp_file_code_exist = $file_code;

  $stmt_check_file_code = $conn->prepare("SELECT * FROM temp_file_code WHERE file_code = ? AND cbs != ? AND status = 'prepare'");
  $stmt_check_file_code->execute([$file_code, $cbs]);

  $found = $stmt_check_file_code->fetch();

  if ($found) {

    if ($found->uic == $uic) return $valid_file_code;

    $stmt_get_last_file_code_form_temp = $conn->prepare("SELECT * FROM temp_file_code WHERE status = 'prepare' AND cbs != ? order by file_code DESC limit 1");
    $stmt_get_last_file_code_form_temp->execute([$cbs]);

    $last_temp_file_code = $stmt_get_last_file_code_form_temp->fetch();

    if ($last_temp_file_code) $last_temp_file_code_exist = $last_temp_file_code->file_code;

    $file_codes = explode('/', $last_temp_file_code_exist);
    $file_code_number = (int)substr($file_codes[0], 7, 5);
    $file_code_text = substr($file_codes[0], 0, 6);

    $year = date('Y') + 543 - 2500;

    if (userHasSiteName('rsat_bkk')) $file_code_year = $year - 59; // 64 is year 5
    if (userHasSiteName('rsat_ska')) $file_code_year = $year - 60; // 64 is year 4
    if (userHasSiteName('rsat_ubn')) $file_code_year = $year - 60; // 64 is year 4
    if (userHasSiteName('rsat_cbi')) $file_code_year = $year - 60; // 64 is year 4
    if (userHasSiteName('rsat_npt')) $file_code_year = $year - 66; // 66 is year 1
    if (userHasSiteName('rsat_nsn')) $file_code_year = $year - 63; // 64 is year 1

    $add_file_code = ($file_code_year * 100000) + ($file_code_number + 1);

    $valid_file_code = $file_code_text . $add_file_code . '/' . $year;
  }
  // check user use temp file
  $userUseTemp = $conn->prepare("SELECT * FROM temp_file_code WHERE cbs = ?");
  $userUseTemp->execute([$cbs]);

  $userUseTempCheck = $userUseTemp->fetch();

  ($userUseTempCheck) ? updateDatabase('temp_file_code', ['clinic_id' => $clinic_id, 'file_code' => $valid_file_code, 'uic' => $uic, 'status' => 'prepare'], ['cbs' => $cbs]) : insertDatabase('temp_file_code', ['clinic_id' => $clinic_id, 'file_code' => $valid_file_code, 'uic' => $uic, 'cbs' => $cbs, 'status' => 'prepare']);

  return $valid_file_code;
}

function fetchNewPreCounseling($preStatus, $clinic_id, $uic)
{
  global $conn;
  $stmt = $conn->prepare("SELECT 
  a.*,
  b.assist_last_entry,
  b.assist_period
  FROM clinicprecounseling a 
  LEFT JOIN module_drug_assist b ON b.uic = a.uic AND b.clinic_id < a.clinic_id AND b.id in (SELECT max(id) FROM module_drug_assist WHERE uic = a.uic)
  WHERE a.preStatus = ? and a.clinic_id = ? and a.uic = ?");
  $stmt->execute([$preStatus, $clinic_id, $uic]);

  return $stmt;
}

function fetchPreCounselingInfo($conn, $clinic_id, $uic)
{
  $sql = "SELECT 
    a.*,
    b.id as newcase_id,
    b.id_card,
    b.uid_code,

    b.clinicQueue,
    b.queueStatus,
    b.queueCbs,
    b.queueUpdate,
    b.allergyHistory,
    b.medical_condition,

    post.postStatus,
    phar.pharmacy_status,

    i.esig_code, 
    i.reference_code,

    -- crs.esig_code as consent_esig_code, 
    -- crs.reference_code as consent_reference_code,

    cr.request_date,  
    cr.request_list,  
    cr.client_signature as consent_reference_code,  
    cr.cbs as request_cbs,  
    cr.station as request_station,  

    hm.pid,
    hm.gender_reassignment,
    hm.hormones_used,
    hm.first_hormones_used_age,
    hm.first_hormones_role_model,
    hm.first_hormones_injection,
    hm.first_hormones_injection_staff,
    hm.other_interested_services,
    hm.other_interested_services_else,

    hr.harassment_screening,
    hr.harassment_recieved,
    hr.harassment_recieved_else,
    hr.harassment_recieved_date,
    hr.harassment_recieved_detail,
    hr.harassment_recieved_reported,
    hr.harassment_recieved_reported_location,
    hr.harassment_recieved_refer,
    hr.harassment_recieved_organization,
    hr.harassment_recieved_organization_else,

    m.mental_2q_1,
    m.mental_2q_2,
    m.mental_2q_3,
    m.mental_9q_1,
    m.mental_9q_2,
    m.mental_9q_3,
    m.mental_9q_4,
    m.mental_9q_5,
    m.mental_9q_6,
    m.mental_9q_7,
    m.mental_9q_8,
    m.mental_9q_9,
    m.mental_total_score,
    m.mental_suggestion,

    m.mental_8q_1,
    m.mental_8q_2,
    m.mental_8q_3,
    m.mental_8q_3_1,
    m.mental_8q_4,
    m.mental_8q_5,
    m.mental_8q_6,
    m.mental_8q_7,
    m.mental_8q_8,
    m.mental_8q_total_score,
    m.mental_8q_suggestion,

    emw.feeling_bad,
    emw.anxiety,
    emw.restless,
    emw.alcohol_frequency,
    emw.alcohol_amount,
    emw.drug_use,
    emw.loss_control,
    emw.plan_hurt,
    emw.strange_feeling,
    emw.hearing_voices,
    emw.death_wish,
    emw.suicidal_thoughts,
    emw.suicide_attempt_plan,
    emw.status_alcohol,
    emw.status_substance,
    emw.status_mental_health,
    emw.status_suicide,

    assist.assist_q1_a,
    assist.assist_q1_b,
    assist.assist_q1_c,
    assist.assist_q1_d,
    assist.assist_q1_e,
    assist.assist_q1_f,
    assist.assist_q1_g,
    assist.assist_q1_h,
    assist.assist_q1_i,
    assist.assist_q1_j,
    assist.assist_q2_a,
    assist.assist_q2_b,
    assist.assist_q2_c,
    assist.assist_q2_d,
    assist.assist_q2_e,
    assist.assist_q2_f,
    assist.assist_q2_g,
    assist.assist_q2_h,
    assist.assist_q2_i,
    assist.assist_q2_j,
    assist.assist_q2_use_when_sex_a,
    assist.assist_q2_use_when_sex_b,
    assist.assist_q2_use_when_sex_c,
    assist.assist_q2_use_when_sex_d,
    assist.assist_q2_use_when_sex_e,
    assist.assist_q2_use_when_sex_f,
    assist.assist_q2_use_when_sex_g,
    assist.assist_q2_use_when_sex_h,
    assist.assist_q2_use_when_sex_i,
    assist.assist_q2_use_when_sex_j,
    assist.assist_q2_use_method_a,
    assist.assist_q2_use_method_b,
    assist.assist_q2_use_method_c,
    assist.assist_q2_use_method_d,
    assist.assist_q2_use_method_e,
    assist.assist_q2_use_method_f,
    assist.assist_q2_use_method_g,
    assist.assist_q2_use_method_h,
    assist.assist_q2_use_method_i,
    assist.assist_q2_use_method_j,
    assist.assist_q2_use_inject_with_other_a,
    assist.assist_q2_use_inject_with_other_b,
    assist.assist_q2_use_inject_with_other_c,
    assist.assist_q2_use_inject_with_other_d,
    assist.assist_q2_use_inject_with_other_e,
    assist.assist_q2_use_inject_with_other_f,
    assist.assist_q2_use_inject_with_other_g,
    assist.assist_q2_use_inject_with_other_h,
    assist.assist_q2_use_inject_with_other_i,
    assist.assist_q2_use_inject_with_other_j,
    assist.assist_q2_1_use_a,
    assist.assist_q2_1_use_b,
    assist.assist_q2_1_use_c,
    assist.assist_q2_1_use_d,
    assist.assist_q2_1_use_e,
    assist.assist_q2_1_use_f,
    assist.assist_q2_1_use_g,
    assist.assist_q2_1_use_h,
    assist.assist_q2_1_use_i,
    assist.assist_q2_1_use_j,
    assist.assist_q2_1_method_d,
    assist.assist_q2_1_method_e,
    assist.assist_q2_1_method_g,
    assist.assist_q2_1_method_h,
    assist.assist_q2_1_method_i,
    assist.assist_q2_1_method_j,
    assist.assist_q2_1_join_d,
    assist.assist_q2_1_join_e,
    assist.assist_q2_1_join_g,
    assist.assist_q2_1_join_h,
    assist.assist_q2_1_join_i,
    assist.assist_q2_1_join_j,
    assist.assist_q2_2_use_a,
    assist.assist_q2_2_use_b,
    assist.assist_q2_2_use_c,
    assist.assist_q2_2_use_d,
    assist.assist_q2_2_use_e,
    assist.assist_q2_2_use_f,
    assist.assist_q2_2_use_g,
    assist.assist_q2_2_use_h,
    assist.assist_q2_2_use_i,
    assist.assist_q2_2_use_j,
    assist.assist_q2_2_method_d,
    assist.assist_q2_2_method_e,
    assist.assist_q2_2_method_g,
    assist.assist_q2_2_method_h,
    assist.assist_q2_2_method_i,
    assist.assist_q2_2_method_j,
    assist.assist_q2_2_join_d,
    assist.assist_q2_2_join_e,
    assist.assist_q2_2_join_g,
    assist.assist_q2_2_join_h,
    assist.assist_q2_2_join_i,
    assist.assist_q2_2_join_j,
    assist.assist_q3_a,
    assist.assist_q3_b,
    assist.assist_q3_c,
    assist.assist_q3_d,
    assist.assist_q3_e,
    assist.assist_q3_f,
    assist.assist_q3_g,
    assist.assist_q3_h,
    assist.assist_q3_i,
    assist.assist_q3_j,
    assist.assist_q4_a,
    assist.assist_q4_b,
    assist.assist_q4_c,
    assist.assist_q4_d,
    assist.assist_q4_e,
    assist.assist_q4_f,
    assist.assist_q4_g,
    assist.assist_q4_h,
    assist.assist_q4_i,
    assist.assist_q4_j,
    assist.assist_q5_a,
    assist.assist_q5_b,
    assist.assist_q5_c,
    assist.assist_q5_d,
    assist.assist_q5_e,
    assist.assist_q5_f,
    assist.assist_q5_g,
    assist.assist_q5_h,
    assist.assist_q5_i,
    assist.assist_q5_j,
    assist.assist_q6_a,
    assist.assist_q6_b,
    assist.assist_q6_c,
    assist.assist_q6_d,
    assist.assist_q6_e,
    assist.assist_q6_f,
    assist.assist_q6_g,
    assist.assist_q6_h,
    assist.assist_q6_i,
    assist.assist_q6_j,
    assist.assist_q7_a,
    assist.assist_q7_b,
    assist.assist_q7_c,
    assist.assist_q7_d,
    assist.assist_q7_e,
    assist.assist_q7_f,
    assist.assist_q7_g,
    assist.assist_q7_h,
    assist.assist_q7_i,
    assist.assist_q7_j,
    assist.assist_q8,
    assist.assist_q8_d,
    assist.assist_q8_e,
    assist.assist_q8_g,
    assist.assist_q8_h,
    assist.assist_q8_i,
    assist.assist_q8_j,
    assist.assist_total_score,
    assist.assist_period,
    assist.assist_suggestion,

    dass21.no1 as dass21_no1,
    dass21.no2 as dass21_no2,
    dass21.no3 as dass21_no3,
    dass21.no4 as dass21_no4,
    dass21.no5 as dass21_no5,
    dass21.no6 as dass21_no6,
    dass21.no7 as dass21_no7,
    dass21.no8 as dass21_no8,
    dass21.no9 as dass21_no9,
    dass21.no10 as dass21_no10,
    dass21.no11 as dass21_no11,
    dass21.no12 as dass21_no12,
    dass21.no13 as dass21_no13,
    dass21.no14 as dass21_no14,
    dass21.no15 as dass21_no15,
    dass21.no16 as dass21_no16,
    dass21.no17 as dass21_no17,
    dass21.no18 as dass21_no18,
    dass21.no19 as dass21_no19,
    dass21.no20 as dass21_no20,
    dass21.no21 as dass21_no21,
    dass21.depression_total as dass21_depression_total,
    dass21.depression_suggestion as dass21_depression_suggestion,
    dass21.anxiety_total as dass21_anxiety_total,
    dass21.anxiety_suggestion as dass21_anxiety_suggestion,
    dass21.stress_total as dass21_stress_total,
    dass21.stress_suggestion as dass21_stress_suggestion,

    last_assist.assist_period as last_assist_period,
    last_assist.created_at as last_assist_date

  FROM clinicprecounseling a
  LEFT JOIN clinicnewcase b 
    ON b.clinic_id = a.clinic_id AND b.queue = a.queue
  LEFT JOIN clinicpostcounseling post 
    ON post.clinic_id = a.clinic_id AND post.queue = a.queue
  LEFT JOIN clinic_pharmacy phar 
    ON phar.clinic_id = a.clinic_id AND phar.queue = a.queue
  LEFT JOIN esignature i 
    ON i.reference_code = a.client_signature
  LEFT JOIN (
    SELECT * FROM consent_request 
    WHERE uic = :uic_1
    ORDER BY signature_date DESC, id DESC 
    LIMIT 1
  ) cr ON cr.uic = b.uic
  LEFT JOIN module_mentalhealth m 
    ON a.clinic_id = m.clinic_id AND a.queue = m.queue
  LEFT JOIN module_emwtools emw 
    ON a.clinic_id = emw.clinic_id AND a.queue = emw.queue
  LEFT JOIN module_drug_assist assist 
    ON a.clinic_id = assist.clinic_id AND a.queue = assist.queue
  LEFT JOIN module_harassment hr 
    ON a.clinic_id = hr.clinic_id AND a.queue = hr.queue
  LEFT JOIN module_dass21 dass21 
    ON a.clinic_id = dass21.clinic_id AND a.queue = dass21.queue
  LEFT JOIN module_hormones_profile hm 
    ON a.uic = hm.uic
  LEFT JOIN (
    SELECT * FROM module_drug_assist 
    WHERE clinic_id < :clinic_id_1 AND uic = :uic_2
    ORDER BY id DESC 
    LIMIT 1
  ) last_assist ON last_assist.uic = a.uic
  WHERE a.clinic_id = :clinic_id_2 AND a.uic = :uic_3";

  $stmt = $conn->prepare($sql);

  // bind ทุกพารามิเตอร์ที่ไม่ซ้ำกัน
  $stmt->bindValue(':uic_1', $uic, PDO::PARAM_STR);
  $stmt->bindValue(':clinic_id_1', $clinic_id, PDO::PARAM_INT);
  $stmt->bindValue(':uic_2', $uic, PDO::PARAM_STR);
  $stmt->bindValue(':clinic_id_2', $clinic_id, PDO::PARAM_INT);
  $stmt->bindValue(':uic_3', $uic, PDO::PARAM_STR);

  $stmt->execute();

  return $stmt;
}

function fetchPostCounselingInfo($clinic_id, $uic)
{
  global $conn;
  $stmt = $conn->prepare("SELECT 
  a.*, 

  d.clinicQueue,
  d.queueStatus,
  d.queueCbs,
  d.queueUpdate,

  c.id as prescription_id
  
  FROM clinicpostcounseling a 
  LEFT JOIN clinicnewcase d ON d.clinic_id = a.clinic_id AND d.queue = a.queue
  LEFT JOIN clinicprecounseling b ON a.clinic_id = b.clinic_id AND a.queue = b.queue
  LEFT JOIN prescription c ON c.id = 
        (
           SELECT MAX(id) 
           FROM prescription z 
           WHERE z.uic = a.uic AND z.prescription_type = 'clinic' AND z.prep_visit = b.prep_service_visit
        )
  WHERE a.clinic_id = ? AND a.uic = ?");
  $stmt->execute([$clinic_id, $uic]);

  return $stmt;
}

function addNewPreCounseling($clinic_id, $uic, $queue, $counselor)
{

  $stmt = insertDatabase('clinicprecounseling', [
    'clinic_id' => $clinic_id,
    'uic' => $uic,
    'queue' => $queue,
    'cbs' => $counselor
  ]);

  updateDatabase('clinicnewcase', [
    'status' => 'Pre-Counseling',
    'counselor' => $counselor
  ], [
    'clinic_id' => $clinic_id,
    'uic' => $uic
  ]);

  return $stmt;
}

function addNewLab($clinic_id, $uic, $queue, $kp)
{

  $insertData = [
    'clinic_id' => $clinic_id,
    'uic' => $uic,
    'queue' => $queue,
    'kp' => $kp
  ];

  if (isset($_SESSION['user_code']) && $_SESSION['user_code'] == 16) {
    $insertData['lab_staff_collect'] = $_SESSION['user'];
  }

  $stmt = insertDatabase('cliniclabresult', $insertData);

  updateDatabase('clinicnewcase', [
    'status' => 'Testing'
  ], [
    'clinic_id' => $clinic_id,
    'uic' => $uic
  ]);

  return $stmt;
}

function addNewPostCounseling($clinic_id, $uic, $queue, $kp, $counselor)
{

  // fetch prep form pre-counseling
  $preData = selectDatabase('clinicprecounseling', [], ['clinic_id' => ['=', $clinic_id], 'queue' => ['=', $queue]])->fetch();

  $newPostData = [
    'clinic_id' => $clinic_id,
    'uic' => $uic,
    'queue' => $queue,
    'counselorKp' => $kp,
    'cbs' => $counselor
  ];

  $prep_med_data = [
    'prep_distribution' => $preData->prep_distribution,
    'prep_no_distribution_reason' => $preData->prep_no_distribution_reason,
    'prep_med_type' => $preData->prep_med_type,
    'prep_med_amount' => $preData->prep_med_amount,
    "client_signature" => $preData->client_signature,
    'prep_set_visit' => $preData->prep_set_visit,
    'prep_comment' => $preData->prep_comment,
    'nextFuTopic' => $preData->nextFuTopic,
    'nextFuDate' => $preData->nextFuDate
  ];

  if ($preData->prep_service_express == 'Express ไม่รอฟังผล') $newPostData = array_merge($newPostData, $prep_med_data);

  $stmt = insertDatabase('clinicpostcounseling', $newPostData);

  updateDatabase('clinicnewcase', [
    'status' => 'Post-Counseling',
  ], [
    'clinic_id' => $clinic_id,
    'uic' => $uic
  ]);

  return $stmt;
}

function fetchDoctorData($clinic_id, $uic, $queue)
{
  global $conn;
  $stmt = $conn->prepare("SELECT * FROM clinicdoctor WHERE clinic_id = ? and uic = ? and queue = ?");
  $stmt->execute([$clinic_id, $uic, $queue]);

  return $stmt;
}

function addNewClinicDoctor($clinic_id, $uic, $queue, $doctor)
{

  $newClinicDoctor = [
    'clinic_id' => $clinic_id,
    'uic' => $uic,
    'queue' => $queue,
    'doctor' => $doctor
  ];
  $stmt = insertDatabase('clinicdoctor', $newClinicDoctor);

  updateDatabase('clinicnewcase', [
    'status' => 'Doctor',
  ], [
    'clinic_id' => $clinic_id,
    'queue' => $queue
  ]);
  return $stmt;
}

function addNewClinicPharmacy($clinic_id, $uic, $queue, $counselor)
{

  $newClinicPharmacy = [
    'clinic_id' => $clinic_id,
    'uic' => $uic,
    'queue' => $queue,
    'pharmacy_staff' => $counselor
  ];
  $stmt = insertDatabase('clinic_pharmacy', $newClinicPharmacy);

  updateDatabase('clinicnewcase', [
    'status' => 'Pharmacy',
  ], [
    'clinic_id' => $clinic_id,
    'queue' => $queue
  ]);
  return $stmt;
}

function fetchLabWaitResultLists($clinic_id = '')
{
  global $conn;

  $data = [];

  $where = "WHERE (a.services LIKE '%ตรวจ NG%' OR a.services LIKE '%ตรวจ CT%' OR a.services LIKE '%ตรวจ VL%' OR (a.services LIKE '%ตรวจ CD4%' AND a.cd4_result = 'ผล CD4 ยังไม่ออก'))";

  if ($clinic_id) {
    $data = [$clinic_id];

    $where .= " AND a.clinic_id = ?";
  }

  $stmt = $conn->prepare("SELECT
  a.*,
  b.clinicQueue as clinic_queue
  FROM cliniclabresult a
  LEFT JOIN clinicnewcase b ON a.clinic_id = b.clinic_id AND a.queue = b.queue
  $where");
  $stmt->execute($data);

  return $stmt;
}

function fetchLabWaitResultHormones($clinic_id = '', $startDate, $endDate)
{
  global $conn;

  $data = [$startDate, $endDate];

  $where = "WHERE c.serviceProvide like '%Hormones%' AND b.created_at >= ? AND b.created_at < ?";

  if ($clinic_id) {
    $data = [$startDate, $endDate, $clinic_id];

    $where .= " AND a.clinic_id = ?";
  }

  $stmt = $conn->prepare("SELECT
    a.*,
    c.serviceProvide, c.preComment,
    b.clinicQueue as clinic_queue,
    b.uid_code as uid,
    b.phone1 as phone,
    b.nickname,
    d.hormones_lab_date,
    d.hormones_estradiol,	
    d.hormones_testosterone,	
    d.hormones_files,
    d.lab_status,
    d.hormones_remarks,
    d.cbs as lab_entry_staff
  FROM cliniclabresult a
  LEFT JOIN clinicnewcase b ON a.clinic_id = b.clinic_id AND a.queue = b.queue AND a.uic = b.uic
  LEFT JOIN clinicprecounseling c ON a.clinic_id = c.clinic_id AND a.queue = c.queue AND a.uic = c.uic
  LEFT JOIN module_hormones_lab_result d ON a.clinic_id = d.clinic_id AND a.queue = d.queue AND a.uic = d.uic
  $where");
  $stmt->execute($data);

  return $stmt;
}

function fetchLabWaitResultCT($clinic_id = '', $start_date)
{
  global $conn;

  $data = [$start_date];

  $where = "WHERE c.serviceProvide like '%ตรวจ CT%' AND b.created_at > ?";

  if ($clinic_id) {
    $data = [$start_date, $clinic_id];

    $where .= " AND a.clinic_id = ?";
  }

  $stmt = $conn->prepare("SELECT
    a.*,
    c.serviceProvide, c.preComment,
    b.clinicQueue as clinic_queue,
    b.uid_code as uid,
    b.phone1 as phone,
    b.nickname,
    d.lab_topic,
    d.lab_date as ct_lab_date,	
    d.lab_result as ct_lab_result,	
    d.lab_report_staff as ct_lab_report_staff,
    d.lab_report_time as ct_lab_report_time,
    d.lab_confirm_staff as ct_lab_confirm_staff,
    d.lab_confirm_time as ct_lab_confirm_time,
    d.lab_files as ct_lab_files,
    d.lab_remarks as ct_lab_remarks,
    d.lab_status,
    d.lab_report_staff as ct_lab_report_staff
  FROM cliniclabresult a
  LEFT JOIN clinicnewcase b ON a.clinic_id = b.clinic_id AND a.queue = b.queue AND a.uic = b.uic
  LEFT JOIN clinicprecounseling c ON a.clinic_id = c.clinic_id AND a.queue = c.queue AND a.uic = c.uic
  LEFT JOIN late_lab_result d ON a.clinic_id = d.clinic_id AND a.queue = d.queue AND a.uic = d.uic AND d.lab_topic = 'CT'
  $where LIMIT 10");
  $stmt->execute($data);

  return $stmt;
}

function fetchLabWaitResultNG($clinic_id = '', $start_date)
{
  global $conn;

  $data = [$start_date];

  $where = "WHERE c.serviceProvide like '%ตรวจ CT%' AND b.created_at > ?";

  if ($clinic_id) {
    $data = [$start_date, $clinic_id];

    $where .= " AND a.clinic_id = ?";
  }

  $stmt = $conn->prepare("SELECT
    a.*,
    c.serviceProvide, c.preComment,
    b.clinicQueue as clinic_queue,
    b.uid_code as uid,
    b.phone1 as phone,
    b.nickname,
    d.lab_topic,
    d.lab_date as ng_lab_date,	
    d.lab_result as ng_lab_result,	
    d.lab_report_staff as ng_lab_report_staff,
    d.lab_report_time as ng_lab_report_time,
    d.lab_confirm_staff as ng_lab_confirm_staff,
    d.lab_confirm_time as ng_lab_confirm_time,
    d.lab_files as ng_lab_files,
    d.lab_remarks as ng_lab_remarks,
    d.lab_status,
    d.lab_report_staff as ng_lab_report_staff
  FROM cliniclabresult a
  LEFT JOIN clinicnewcase b ON a.clinic_id = b.clinic_id AND a.queue = b.queue AND a.uic = b.uic
  LEFT JOIN clinicprecounseling c ON a.clinic_id = c.clinic_id AND a.queue = c.queue AND a.uic = c.uic
  LEFT JOIN late_lab_result d ON a.clinic_id = d.clinic_id AND a.queue = d.queue AND a.uic = d.uic AND d.lab_topic = 'NG'
  $where LIMIT 10");
  $stmt->execute($data);

  return $stmt;
}

function fetchLabWaitResultCTNG($clinic_id = '', $startData, $endDate)
{
  global $conn;

  $data = [$startData, $endDate];

  $where = "WHERE (c.serviceProvide like '%ตรวจ CT%' OR c.serviceProvide like '%ตรวจ NG%') AND b.created_at >= ? AND b.created_at < ?";

  if ($clinic_id) {
    $data = [$startData, $endDate, $clinic_id];

    $where .= " AND a.clinic_id = ?";
  }

  $stmt = $conn->prepare("SELECT
    a.*,
    c.serviceProvide, 
    c.preComment,
    c.sample_collect_person,
    c.sample_collect_methods,
    b.clinicQueue as clinic_queue,
    b.uid_code as uid,
    b.phone1 as phone,
    b.nickname,
    d.lab_topic,
    d.lab_date,	
    d.lab_result_ct,	
    d.lab_result_ng,	
    d.lab_report_staff,
    d.lab_report_time,
    d.lab_confirm_staff,
    d.lab_confirm_time,
    d.lab_files,
    d.lab_remarks,
    d.lab_status
  FROM cliniclabresult a
  LEFT JOIN clinicnewcase b ON a.clinic_id = b.clinic_id AND a.queue = b.queue AND a.uic = b.uic
  LEFT JOIN clinicprecounseling c ON a.clinic_id = c.clinic_id AND a.queue = c.queue AND a.uic = c.uic
  LEFT JOIN late_lab_result d ON a.clinic_id = d.clinic_id AND a.queue = d.queue AND a.uic = d.uic AND lab_topic = 'NG/CT'
  $where");
  $stmt->execute($data);

  return $stmt;
}

function fetchLastestNhsoClinicByUic($uic, $service_date)
{
  global $conn;
  $stmt = $conn->prepare("SELECT service_date,hiv_result,sti_result FROM clinic where uic = ? and date(service_date) > ? and hiv_result != ? order by sn desc limit 1");
  $stmt->execute([$uic, $service_date, '-']);

  return $stmt;
}

function fetchKpFromClinicBySn($sn)
{
  global $conn;
  $stmt = $conn->prepare("SELECT sex, phone FROM clinic where sn = ? limit 1");
  $stmt->execute([$sn]);

  return $stmt;
}

function fetchPhoneForNhso($sn)
{
  global $conn;
  $stmt = $conn->prepare("SELECT uic,phone FROM clinic where sn = ? limit 1");
  $stmt->execute([$sn]);

  return $stmt;
}

function fetchClinicByUIC($uic)
{
  global $conn;
  $stmt = $conn->prepare("SELECT 
  a.sn,
  a.service_date,
  a.uic,
  a.firstname,
  a.lastname,
  a.nickname,
  a.phone,
  a.mobile,
  a.comment1,
  a.comment2,
  a.created_at,
  a.file_code,
  a.prep,
  a.prep_med_amount,
  a.kpid,
  a.pid,
  a.old_uic,
  a.sex,
  a.education,
  a.occupation,
  a.referor,
  a.refer_number,
  a.cbs,
  a.id_card,

  a.hiv_result as clinic_hiv_result,
  a.sti_result as clinic_tpha_result,

  a.counselor,
  a.allergyHistory,
  a.medical_condition,
  a.age,
  a.line_id,
  a.email,
  a.full_address,
  a.nationality,
  a.sexwork,
  a.route,
  a.counselor,
  a.risk,
  a.next_retain_topic,
  a.next_retain_date,

  b.prep_service_visit,
  b.serviceProvide,
  b.sub_kp,
  p.prep_set_visit,
  p.nextFuTopic,
  p.nextFuDate,

  newcase.healthcare,
  newcase.hospital,

  c.services,
  c.hiv_result,
  c.tpha_result,
  c.vdrl_result,
  c.cd4_result,
  c.cd4_value as cd4,
  c.cd4_percent as cd4percent,
  c.hcv_result,
  c.hcv_rna_result,
  c.hcv_rna_value,
  c.hcv_doc_result,
  c.hbsag_result,
  c.creatinine_result,
  c.egfr_result,
  c.alt_result,
  c.ct_result,
  c.ng_result,
  c.wet_smear_result,
  c.gram_stain_result,
  c.vl_result,
  c.vl_result_value,

  d.lab_topic,
  d.lab_date,
  d.lab_result_ct,
  d.lab_result_ng,
  d.lab_report_staff,
  d.lab_report_time,
  d.lab_confirm_staff,
  d.lab_confirm_time,
  d.lab_files,
  d.lab_remarks,
  d.lab_status,

  dctng.lab_result_ng as ng_result_late,
  dctng.lab_result_ct as ct_result_late,
  dvl.lab_result_vl_value as vl_result_late,
  dcd4.lab_result_cd4_value as cd4_result_late,
  dcd4.lab_result_cd4_percent as cd4_percent_late

  FROM clinic a
  LEFT JOIN clinicprecounseling b ON b.id = a.pre_id
  LEFT JOIN clinicprecounseling p ON p.id = a.post_id
  LEFT JOIN cliniclabresult c ON c.id = a.lab_id
  LEFT JOIN clinicnewcase newcase ON newcase.id = a.newcase_id
  LEFT JOIN late_lab_result d ON a.clinic_id = d.clinic_id AND a.q = d.queue AND a.uic = d.uic
  LEFT JOIN late_lab_result dctng ON dctng.id = (SELECT max(id) FROM late_lab_result WHERE lab_topic in ('CT', 'NG', 'NG/CT') AND clinic_id = a.clinic_id AND uic = a.uic AND queue = a.q)
  LEFT JOIN late_lab_result dvl ON dvl.id = (SELECT max(id) FROM late_lab_result WHERE lab_topic = 'HIV-VL' AND clinic_id = a.clinic_id AND uic = a.uic AND queue = a.q)
  LEFT JOIN late_lab_result dcd4 ON dcd4.id = (SELECT max(id) FROM late_lab_result WHERE lab_topic = 'CD4' AND clinic_id = a.clinic_id AND uic = a.uic AND queue = a.q)
  WHERE a.uic = ?");
  $stmt->execute([$uic]);

  return $stmt;
}

function checkPhoneMatch($phone1, $phone2)
{
  $a = str_replace('-', '', $phone1);
  $b = str_replace('-', '', $phone2);
  if ($a == $b) {
    $result = $a;
  } else {
    $result = $a . ',' . $b;
  }
  return $result;
}

// fetch care support data by uic
function fetchCareSupportByUic($uic)
{
  global $conn;
  $stmt = $conn->prepare("SELECT * FROM caresupportdatabase where uic = ?");
  $stmt->execute([$uic]);

  return $stmt;
}

//  fetch retain for print document data
function fetchRetainData($uic = '', $topic, $start = '2014-01-01', $end = '2030-01-01', $cbs = '', $retainIDStart, $retainIDEnd)
{
  $retainIDStart = $retainIDStart == '' ? 1 : $retainIDStart;
  $retainIDEnd = $retainIDEnd == '' ? 9999 : $retainIDEnd;
  $end = $end == '' ? '2030-01-01' : "$end 23:59:59";
  global $conn;
  $stmt = $conn->prepare("SELECT * from retaincare where topic = ? and uic like ? and retainDate > ? and retainDate < ? and cbs like ? and retain_id >= ? and retain_id <= ? order by retain_id");
  $stmt->execute([$topic, "%" . $uic . "%", $start, $end, "%" . $cbs . "%", $retainIDStart, $retainIDEnd]);

  return $stmt;
}

function fetchRetainUicList()
{
  global $conn;
  $data = [checkQuarter()['q1_start']];
  $stmt = $conn->prepare("SELECT uic from retaincare where retainDate >= ? group by uic order by uic");
  $stmt->execute($data);

  $fetch = $stmt->fetchall();

  return $fetch;
}

//  fetch clinic for print document data
function fetchClinicData(
  $cbs = '',
  $uic = '',
  $clinic_id = '',
  $start = '2014-01-01',
  $end = '2030-01-01',
  $type = '',
  $IDStart = 1,
  $IDEnd = 999999
) {
  // ปรับค่าเริ่มต้น
  $IDStart   = ($IDStart === '' ? 1 : (int)$IDStart);
  $IDEnd     = ($IDEnd   === '' ? 999999 : (int)$IDEnd);

  // ต่อเวลาสิ้นวันให้ end (ถ้าไม่มีเวลา)
  // สมมติ service_date เป็น DATETIME; ถ้าเป็น DATE ก็ใช้แค่ $end ก็พอ
  if (strpos($end, ' ') === false) {
    $end = $end . ' 23:59:59';
  }

  // แปลง clinic_id ว่างให้เป็น NULL เพื่อใช้ ( ? IS NULL OR clinic_id = ? )
  $clinicIdParam = ($clinic_id === '' ? null : (int)$clinic_id);

  // เตรียมพารามิเตอร์ LIKE
  $cbsLike  = '%' . $cbs . '%';
  $uicLike  = '%' . $uic . '%';
  $typeLike = '%' . $type . '%';

  global $conn;

  $sql = "
    SELECT
      service_date,
      pid,
      prep,
      full_address,
      comment1,
      uic,
      clinic_id,
      mobile,
      hn,
      q,
      firstname,
      lastname,
      age,
      nickname,
      phone,
      sex,
      id_card,
      nationality,
      line_id,
      allergyHistory,
      sn,
      counselor
    FROM clinic
    WHERE
      cbs LIKE ?                     -- ใช้คอลัมน์ cbs ให้สอดคล้องกับตัวแปร $cbs
      AND uic LIKE ?
      AND ( ? IS NULL OR clinic_id = ? )
      AND service_date BETWEEN ? AND ?  -- รวมวันที่ปลายด้วย
      AND type LIKE ?
      AND sn BETWEEN ? AND ?
    ORDER BY sn ASC
  ";

  $stmt = $conn->prepare($sql);
  $stmt->execute([
    $cbsLike,
    $uicLike,
    $clinicIdParam,
    $clinicIdParam,
    $start,
    $end,
    $typeLike,
    $IDStart,
    $IDEnd,
  ]);

  return $stmt;
}

function fetchLastestCareSupport()
{
  global $conn;
  $stmt = $conn->prepare("SELECT * FROM caresupportdatabase order by id DESC ");
  $stmt->execute();

  return $stmt;
}

// care lastest hiv
function fetchLastestCareSupportHiv()
{
  global $conn;
  $stmt = $conn->prepare("SELECT * FROM caresupportdatabase where hiv_code > 0 order by hiv_code DESC limit 1");
  $stmt->execute();

  return $stmt;
}

// for hiv dashboard
function fetchHIVdb()
{
  global $conn;
  $data = ['positive'];
  $stmt = $conn->prepare("SELECT * FROM caresupportdatabase where hiv_status = ?");
  $stmt->execute($data);

  return $stmt;
}

// for hiv dashboard
function fetchHivNotYetRetain()
{
  global $conn;
  $data = ['positive'];
  $stmt = $conn->prepare("SELECT * FROM caresupportdatabase where hiv_status = ?");
  $stmt->execute($data);

  $cases = $stmt->fetchall();

  $return['hivAll'] = $stmt->rowCount();
  $return['notFound'] = 0;
  $return['notSetRetain'] = 0;
  $return['netYetRetainCount'] = 0;
  $return['onTimeRetainCount'] = 0;
  $return['passRetainCount'] = 0;


  foreach ($cases as $case) {
    //ค้น retaincare หาเคส row ล่าสุด ตรวจสอบเวลานัด เทียบเวลาปัจจุบัน แล้วนับออกมาใส่ใน []
    $data_retain = [$case->uic];
    $stmt_retain = $conn->prepare("SELECT uic, setRetainDate FROM retaincare where uic = ? order by id DESC limit 1");
    $stmt_retain->execute($data_retain);

    $retain_data = $stmt_retain->fetch();

    $return['notFound'] = !$retain_data ? $return['notFound'] + 1 : $return['notFound'];
    $return['notSetRetain'] = !($retain_data->setRetainDate ?? 0) ? $return['notSetRetain'] + 1 : $return['notSetRetain'];
    $return['netYetRetainCount'] = ($retain_data->setRetainDate ?? 0) > date("Y-m-d", strtotime("+3 day"))  ? $return['netYetRetainCount'] + 1 : $return['netYetRetainCount'];
    $return['onTimeRetainCount'] = ($retain_data->setRetainDate ?? 0) <= date("Y-m-d", strtotime("+3 day")) && ($retain_data->setRetainDate  ?? 0) >= date("Y-m-d", strtotime("-3 day"))  ? $return['onTimeRetainCount'] + 1 : $return['onTimeRetainCount'];
    $return['passRetainCount'] = ($retain_data->setRetainDate ?? 0) < date("Y-m-d", strtotime("-3 day")) && ($retain_data->setRetainDate ?? 0)  ? $return['passRetainCount'] + 1 : $return['passRetainCount'];
  }

  return $return;
}

// care lastest tpha
function fetchLastestCareSupportTpha()
{
  global $conn;
  $stmt = $conn->prepare("SELECT * FROM caresupportdatabase where tpha_code > 0 order by tpha_code DESC limit 1");
  $stmt->execute();

  return $stmt;
}

// care lastest hcv
function fetchLastestCareSupportHcv()
{
  global $conn;
  $stmt = $conn->prepare("SELECT * FROM caresupportdatabase where hcv_code > 0 order by hcv_code DESC limit 1");
  $stmt->execute();

  return $stmt;
}

// care lastest ng
function fetchLastestCareSupportNG()
{
  global $conn;
  $stmt = $conn->prepare("SELECT * FROM caresupportdatabase where ng_code > 0 order by ng_code DESC limit 1");
  $stmt->execute();

  return $stmt;
}

// care lastest ct
function fetchLastestCareSupportCT()
{
  global $conn;
  $stmt = $conn->prepare("SELECT * FROM caresupportdatabase where ct_code > 0 order by ct_code DESC limit 1");
  $stmt->execute();

  return $stmt;
}

// care lastest stis
function fetchLastestCareSupportStis()
{
  global $conn;
  $stmt = $conn->prepare("SELECT * FROM caresupportdatabase where stis_code > 0 order by stis_code DESC limit 1");
  $stmt->execute();

  return $stmt;
}

//case new uic
function fetchCaseNewUic($uic, $clinic_id)
{
  global $conn;
  $stmt = $conn->prepare("SELECT * FROM clinicnewcase where uic = ? and clinic_id = ?");
  $stmt->execute([$uic, $clinic_id]);

  return $stmt;
}

//retain care support
function fetchCaseRetainCareUic($uic)
{
  global $conn;
  $stmt = $conn->prepare("SELECT * FROM retaincare where uic = ?");
  $stmt->execute([$uic]);

  return $stmt;
}

//retain latest hivretain
function fetchLatestRetainID()
{
  global $conn;

  $data = [];

  $stmt = $conn->prepare("SELECT * FROM retaincare where retain_id != '' order by retain_id desc limit 1");
  $stmt->execute($data);

  return $stmt;
}

//lastest reach
function fetchLastestReachUic($uic)
{
  global $conn;
  $stmt = $conn->prepare("SELECT * FROM reach where uic = ? order by id DESC limit 1");
  $stmt->execute([$uic]);

  return $stmt;
}

//fetch pm
function fetchPMLists($cbs, $seeAll = [])
{
  global $conn;

  $cbs = in_array($cbs, $seeAll) ? "%%" : $cbs;

  $data = [$cbs];

  $stmt = $conn->prepare("SELECT * FROM pm where cbs like ?");
  $stmt->execute($data);

  return $stmt;
}

//getRetainHivRecords
function getRetainHivRecords($startDate = null)
{
  global $conn;
  if ($startDate == null) {
    $data = [];
    $stmt = $conn->prepare("SELECT id, cbs, service_date from caresupport_activity where care_topic like '%HIV%' AND method not in ('ปรับสถานะการส่งต่อ', 'เพิ่มเข้าระบบ')");
    $stmt->execute($data);
  }

  $data = [$startDate];
  $stmt = $conn->prepare("SELECT id, cbs, service_date from caresupport_activity where care_topic like '%HIV%' AND method not in ('ปรับสถานะการส่งต่อ', 'เพิ่มเข้าระบบ') AND service_date >= ?");
  $stmt->execute($data);

  return $stmt;
}

//lastest temperature Data
function getTempStationData()
{
  global $conn;
  $stmt = $conn->prepare("SELECT station, temperature, humidity, Date, Time FROM templogs where id = 1 OR MOD(id + 1, 360) = 1 order by id DESC limit 48");
  $stmt->execute();

  return $stmt;
}

//get Questionnaire Data
function getQuestionnaireData($month = '', $year = '')
{
  global $conn;

  $year = $year == '' ? date('Y') : $year;

  if ($month) {

    $data = [$year, $month];

    $stmt = $conn->prepare("SELECT
  AVG(ontime) as avgontime, AVG(speed) as avgspeed, AVG(infosign) as avginfosign, 
  AVG(expertstaff) as avgexpertstaff, AVG(nonstigma) as avgnonstigma, AVG(cleanliness) as avgcleanliness, 
  AVG(convenient) as avgconvenient, AVG(condomspot) as avgcondomspot, AVG(labequipment) as avglabequipment, 
  AVG(registrationrate) as avgregistrationrate, AVG(counselingrate) as avgcounselingrate, AVG(labrate) as avglabrate  
  FROM clinic_questionnaire where year(created_at) = ? and month(created_at) = ?");
    $stmt->execute($data);
  } else {

    $data = [$year];

    $stmt = $conn->prepare("SELECT
    AVG(ontime) as avgontime, AVG(speed) as avgspeed, AVG(infosign) as avginfosign, 
    AVG(expertstaff) as avgexpertstaff, AVG(nonstigma) as avgnonstigma, AVG(cleanliness) as avgcleanliness, 
    AVG(convenient) as avgconvenient, AVG(condomspot) as avgcondomspot, AVG(labequipment) as avglabequipment, 
    AVG(registrationrate) as avgregistrationrate, AVG(counselingrate) as avgcounselingrate, AVG(labrate) as avglabrate  
    FROM clinic_questionnaire where year(created_at) = ?");
    $stmt->execute($data);
  }

  return $stmt;
}

//Negative Priority Retain
function negRetainsPriority()
{
  global $conn;
  $stmt =  $conn->prepare("SELECT sn,wp,hiv_result,sti_result,nickname,firstname,lastname,service_date,uic,sex FROM clinic 
    WHERE sn IN (
                 SELECT max(sn) 
                   FROM clinic 
                  GROUP BY uic
               )  AND 
    hiv_result != 'R' AND 
    hiv_result != '-' AND
    service_date >= '2016-10-01' AND 
    DATEDIFF(CURDATE(), service_date) > 0
    ");
  $stmt->execute();

  return $stmt;
}

//Negative All Retain
function negRetainNormal()
{
  global $conn;
  $stmt =  $conn->prepare("SELECT sn,wp,hiv_result,sti_result,nickname,firstname,lastname,service_date,uic,sex,id_card,phone,mobile FROM clinic 
    WHERE sn IN (
                 SELECT max(sn) 
                   FROM clinic 
                  GROUP BY uic
               ) AND 
    hiv_result != 'R' AND 
    hiv_result != '-' AND 
    service_date >= '2016-10-01' AND 
    LENGTH(REPLACE(id_card,'-','')) = 13 AND
    LENGTH(REPLACE(phone,'-','')) = 10 AND
    SUBSTRING(id_card, 1, 1) in (1, 2, 3, 5) AND
    sex in ('MSM','MSW','TG','TGSW') AND
    DATEDIFF(CURDATE(), service_date) > 180
    -- AND count = 1
    -- LIMIT 1000, 500
    ");
  $stmt->execute();

  return $stmt;
}

//Negative All Retain
function negativeRetain2020($conn, $startDate, $endDate)
{

  $stmt =  $conn->prepare("SELECT
  a.sn,
  a.wp,
  a.hiv_result,
  a.sti_result,
  a.nickname,
  a.firstname,
  a.lastname,
  a.service_date,
  a.uic,
  a.sex,
  a.id_card,
  a.prep,
  a.referor,
  a.counselor,
  a.phone,
  a.mobile, 
  a.next_retain_topic,
  a.next_retain_date,
  a.comment1,
  a.file_code, 
  c.postComment, 
  c.prep_set_visit,
  d.cbs as refill_cbs,
  d.next_topic as refill_topic,
  d.next_date as refill_next,
  d.created_at as refill_date,
  d.comment as refill_comment,
  d.client_signature as refill_signature,
  g.cbs as retain_cbs, 
  g.retainDate as retain_date, 
  g.setRetainDate as retain_setDate, 
  g.inviteToTest as retain_invite, 
  g.nextRetain as retain_nextRetain,
  g.method as retain_method, 
  g.phoneOption as retain_phoneOption, 
  g.onlineOption as retain_onlineOption, 
  g.phoneReason as retain_phoneReason, 
  g.onlineReason as retain_onlineReason
FROM 
  clinic a
LEFT JOIN 
  retainnegative g ON a.uic = g.uic 
  AND g.id = (SELECT max(h.id) FROM retainnegative h WHERE h.uic = a.uic)
LEFT JOIN 
  clinicpostcounseling c ON c.clinic_id = a.clinic_id 
  AND c.queue = a.q 
  AND c.uic = a.uic
LEFT JOIN 
  prep_spending d ON d.uic = a.uic
  AND d.id = (SELECT MAX(id) FROM prep_spending z WHERE z.uic = a.uic)
WHERE 
  a.sn IN (SELECT max(c.sn) FROM clinic c GROUP BY uic)
  AND (a.phoneStatus NOT IN ('เบอร์ยกเลิก', 'ไม่มีเบอร์นี้') OR a.phoneStatus IS NULL)
  AND a.hiv_result NOT IN ('R','-')
  AND LENGTH(REPLACE(a.phone,'-','')) = 10
  AND (
        (a.next_retain_date IS NOT NULL AND a.next_retain_date >= ? AND a.next_retain_date < ?)
        OR 
        (g.setRetainDate >= ? AND g.setRetainDate < ?)
        OR
        (a.next_retain_date IS NULL AND DATE_ADD(a.service_date, INTERVAL 6 MONTH) >= ? AND DATE_ADD(a.service_date, INTERVAL 6 MONTH) < ?)
      )
  ");

  $stmt->execute([$startDate, $endDate, $startDate, $endDate, $startDate, $endDate]);

  return $stmt;
}

//Negative Retain Check
function checkSMSAlreadySend($uic)
{
  global $conn;
  $stmt =  $conn->prepare('SELECT * FROM negretainsms where uic = ?');
  $stmt->execute([$uic]);

  return $stmt;
}

//Negative Retain Check
function negRetainCheckUic($uic, $last = false)
{
  global $conn;
  $stmt =  $conn->prepare('SELECT * FROM retainnegative where uic = ?');
  if ($last) $stmt =  $conn->prepare('SELECT * FROM retainnegative where uic = ? order by retainDate desc limit 1');
  $stmt->execute([$uic]);

  return $stmt;
}

//Negative Retain Check
function checkValidPhone($uic)
{
  global $conn;
  $data = [$uic, 'Successfully sent to', 'Memory capacity exce'];
  $stmt =  $conn->prepare('SELECT * FROM negretainsms where uic = ? and smsResult in (?,?)');
  $stmt->execute($data);

  return $stmt;
}

//lastest cliniclog
function lastClinic()
{
  global $conn;
  $stmt =  $conn->prepare('SELECT * FROM cliniclog order by id DESC limit 1');
  $stmt->execute();

  return $stmt;
}

//NHSO Retain
function nhsoRetainData()
{
  global $conn;
  $stmt =  $conn->prepare("SELECT sn,wp,hiv_result,sti_result,nickname,firstname,lastname,service_date,uic,sex FROM clinic 
    WHERE sn IN (
                 SELECT max(sn) 
                   FROM clinic 
                  GROUP BY uic
               )
    AND hiv_result != 'R' AND hiv_result != '-'");
  $stmt->execute();

  return $stmt;
}

//fetch clinic data form clinic_id
function fetchClinicFromClinicId($clinic_id)
{
  global $conn;
  $stmt = $conn->prepare("SELECT 
  a.*,
  b.serviceProvide
  FROM clinic a 
  LEFT JOIN clinicprecounseling b ON a.pre_id = b.id
  WHERE a.clinic_id = ?
  ");
  $stmt->execute([$clinic_id]);

  return $stmt;
}

//fetch retain negative list
function fetchNegativeRetainForChart($startDate = '', $endDate = '')
{

  if ($startDate) {
    global $conn;

    $endDate = $endDate == '' ? date('Y-m-d H:i:s') : $endDate;

    $data = [$startDate, $endDate];

    $stmt = $conn->prepare("SELECT 

    a.cbs,
    a.inviteToTest,
    a.retainDate,
    b.service_date, b.hiv_result, b.sti_result, c.nickname

FROM `retainnegative` a 
left join clinic b on a.uic = b.uic and b.sn = (SELECT min(sn) FROM clinic WHERE uic = a.uic and service_date >= a.retainDate)
left join clinic c on a.sn = c.sn
WHERE a.retainDate >= ? AND a.retainDate < ? order by a.id");
    $stmt->execute($data);

    return $stmt;
  }

  global $conn;
  $stmt = $conn->prepare("SELECT * FROM retainnegative order by id DESC");
  $stmt->execute();

  return $stmt;
}

//fetch retain negative list
function fetchNegativeRetain($startDate, $endDate, $eCascadeCheck = false)
{

  global $conn;

  if ($startDate) {

    $data = [$startDate, $endDate];

    $where = "WHERE a.retainDate >= ? AND a.retainDate <= ?";

    if ($eCascadeCheck) $where .= " AND (a.eCascadeCheck = 'false' OR a.eCascadeCheck is null) AND a.phoneOption = 'ติดต่อได้ เคสยินดีให้ข้อมูล'";

    $stmt = $conn->prepare("SELECT a.*, b.service_date, b.hiv_result, b.sti_result, c.nickname, c.sex, c.phone, c.id_card, c.file_code 
    FROM `retainnegative` a 
    left join clinic b on b.sn = (SELECT min(sn) FROM clinic WHERE uic = a.uic and service_date >= a.retainDate)
    left join clinic c on a.sn = c.sn 
    $where");

    $stmt->execute($data);

    return $stmt;
  }

  $stmt = $conn->prepare("SELECT * FROM retainnegative order by id DESC");
  $stmt->execute();

  return $stmt;
}

//fetch retain negative list
function fetchOnlineQuestionnaire()
{
  global $conn;
  $stmt = $conn->prepare("SELECT a.*, c.sex, c.age, c.firstname, c.lastname, c.nickname, 
  b.type as q_type,
  b.uic as q_uic,
  b.phone as q_phone,
  b.name as q_name,
  b.bank_name as q_bank_name,
  b.bank_account as q_bank_account,
  b.tgSameInterest1 as q_tgSameInterest1,
  b.tgSameInterest2 as q_tgSameInterest2,
  b.tgSameInterest3 as q_tgSameInterest3,
  b.tgSameInterest4 as q_tgSameInterest4,
  b.tgMostUseSocialMedia1 as q_tgMostUseSocialMedia1,
  b.tgMostUseSocialMedia2 as q_tgMostUseSocialMedia2,
  b.tgMostUseSocialMedia3 as q_tgMostUseSocialMedia3,
  b.tgMostUseSocialMedia4 as q_tgMostUseSocialMedia4,
  b.tgNormalPageFollow1 as q_tgNormalPageFollow1,
  b.tgNormalPageFollow2 as q_tgNormalPageFollow2,
  b.tgNormalPageFollow3 as q_tgNormalPageFollow3,
  b.tgNormalPageFollow4 as q_tgNormalPageFollow4,
  b.tgSpecialPageFollow1 as q_tgSpecialPageFollow1,
  b.tgSpecialPageFollow2 as q_tgSpecialPageFollow2,
  b.tgSpecialPageFollow3 as q_tgSpecialPageFollow3,
  b.tgSpecialPageFollow4 as q_tgSpecialPageFollow4,
  b.tgAdviseToTestHiv1 as q_tgAdviseToTestHiv1,
  b.tgAdviseToTestHiv2 as q_tgAdviseToTestHiv2,
  b.tgAdviseToTestHiv3 as q_tgAdviseToTestHiv3,
  b.tgAdviseToTestHiv4 as q_tgAdviseToTestHiv4,
  b.tgIncentiveRequest1 as q_tgIncentiveRequest1,
  b.tgIncentiveRequest2 as q_tgIncentiveRequest2,
  b.tgIncentiveRequest3 as q_tgIncentiveRequest3,
  b.tgIncentiveRequest4 as q_tgIncentiveRequest4,
  b.tgActivitiesSuggestion1 as q_tgActivitiesSuggestion1,
  b.tgActivitiesSuggestion2 as q_tgActivitiesSuggestion2,
  b.tgActivitiesSuggestion3 as q_tgActivitiesSuggestion3,
  b.tgActivitiesSuggestion4 as q_tgActivitiesSuggestion4,
  b.tgComment as q_tgComment,
  b.created_at as q_created_at
  FROM retainnegative a LEFT join questionnaire b on a.uic = b.uic LEFT join clinic c on a.sn = c.sn where a.smsStatus != '' order by a.created_at");
  $stmt->execute();

  return $stmt;
}

//check lasted test from clinic
function clinicCheck($uic)
{
  global $conn;
  $stmt = $conn->prepare("SELECT pid, prep, qrcode, address from clinic where uic = ? order by service_date DESC limit 1");
  $stmt->execute([$uic]);

  return $stmt;
}

//check lasted test from clinic
function clinicCheckLastTested($uic, $date)
{
  global $conn;

  $data = [$uic, $date];

  $stmt = $conn->prepare("SELECT service_date from clinic where uic = ? AND date(service_date) < ? order by service_date DESC limit 1");
  $stmt->execute($data);

  return $stmt;
}

//check on caresupport database
function checkCareSupport($uic)
{
  global $conn;
  $stmt = $conn->prepare("SELECT * from caresupportdatabase where uic = ?");
  $stmt->execute([$uic]);

  return $stmt;
}

//select database
function selectDatabase($table, $fields = [], $conditions = [], $orders = [], $limit = '0,18446744073709551615')
{
  global $conn;

  $selectFields = '*';
  $conditionArr = [];
  $orderArr = [];
  $data = [];

  if (count($fields) > 0) $selectFields = implode(',', $fields);

  foreach ($conditions as $key => $value) {

    $conditionArr[] = "{$key} {$value[0]} ?";
    $data[] = $value[1];
  }

  foreach ($orders as $key => $value) {

    $orderArr[] = "{$key} {$value}";
  }

  $order = 'id ASC';
  if (count($orderArr) > 0) $order = implode(', ', $orderArr);

  $condition = 1;
  if (count($conditions) > 0) $condition = implode(' AND ', $conditionArr);


  $query = "SELECT $selectFields FROM $table WHERE $condition ORDER BY $order limit $limit";

  $stmt = $conn->prepare($query);
  $stmt->execute($data);

  return $stmt;
}

//update database
function updateDatabase($table, $update = [], $conditions = [])
{
  global $conn;

  $dataSet = [];
  $data = [];
  $conditionArr = [];

  foreach ($update as $key => $value) {

    $dataSet[] = $key . ' = ? ';
    $data[] = $value;
  }

  foreach ($conditions as $key => $value) {

    $conditionArr[] = $key . ' = ? ';
    $data[] = $value;
  }

  $prepare = implode(',', $dataSet);

  $condition = 1;
  if (count($conditions) > 0) $condition = implode(' AND ', $conditionArr);

  $query = "UPDATE $table SET $prepare WHERE $condition";

  $stmt = $conn->prepare($query);
  $stmt->execute($data);

  return $stmt->rowCount();
}

//delete database
function deleteDatabase($table, $conditions = [])
{
  global $conn;

  $data = [];

  foreach ($conditions as $key => $value) {

    $conditionArr[] = $key . ' = ? ';
    $data[] = $value;
  }

  $condition = 1;
  if (count($conditions) > 0) $condition = implode(' AND ', $conditionArr);

  $query = "DELETE FROM $table WHERE $condition";

  $stmt = $conn->prepare($query);
  $stmt->execute($data);

  return $stmt->rowCount();
}

//insert database
function insertDatabase($table, $insert = [])
{
  global $conn;

  $dataSet = [];
  $valuePlaceholders = [];
  $values = [];

  foreach ($insert as $key => $value) {

    $dataSet[] = $key;
    $values[] = $value;
    $valuePlaceholders[] = '?';
  }

  $data = $values;

  $prepareField = implode(',', $dataSet);
  $prepareValue = implode(',', $valuePlaceholders);


  $query = "INSERT INTO $table ($prepareField) VALUE ($prepareValue)";

  $stmt = $conn->prepare($query);
  $stmt->execute($data);

  return $conn->lastInsertId();
}

//fetch hiv last record on caresupport
function hivLastRecord()
{
  global $conn;
  $stmt = $conn->prepare("SELECT hiv_code, n_code, know_hiv_location from caresupportdatabase where hiv_status = ? ORDER BY hiv_code DESC LIMIT 1");
  $stmt->execute(['positive']);

  return $stmt;
}

//fetch counselor from clinic
function fetchClinicCounselor()
{
  global $conn;

  $data = [];

  $stmt = $conn->prepare("SELECT counselor from clinic where counselor != '' AND counselor is not null group by counselor");
  $stmt->execute($data);

  $data = $stmt->fetchall();

  $response = [];

  foreach ($data as $item) {
    $response[] = $item->counselor;
  }

  return $response;
}

//fetch retain staff from retaincare
function fetchRetaincareCBS()
{
  global $conn;

  $data = [];

  $stmt = $conn->prepare("SELECT cbs from retaincare where cbs != '' AND cbs is not null group by cbs");
  $stmt->execute($data);

  $data = $stmt->fetchall();

  $response = [];

  foreach ($data as $item) {
    $response[] = $item->cbs;
  }

  return $response;
}

//fetch tpha last record on caresupport
function tphaLastRecord()
{
  global $conn;
  $stmt = $conn->prepare("SELECT tpha_code from caresupportdatabase where tpha_status = ? ORDER BY tpha_code DESC LIMIT 1");
  $stmt->execute(['positive']);

  return $stmt;
}

//fetch stis last record on caresupport
function stisLastRecord()
{
  global $conn;
  $stmt = $conn->prepare("SELECT stis_code from caresupportdatabase where stis_status = ? ORDER BY stis_code DESC LIMIT 1");
  $stmt->execute(['positive']);

  return $stmt;
}

//fetch hcv last record on caresupport
function hcvLastRecord()
{
  global $conn;
  $stmt = $conn->prepare("SELECT hcv_code from caresupportdatabase where hcv_status = ? ORDER BY hcv_code DESC LIMIT 1");
  $stmt->execute(['positive']);

  return $stmt;
}

//fetch ct last record on caresupport
function ctLastRecord()
{
  global $conn;
  $stmt = $conn->prepare("SELECT ct_code from caresupportdatabase where ct_status = ? ORDER BY ct_code DESC LIMIT 1");
  $stmt->execute(['positive']);

  return $stmt;
}

//fetch ng last record on caresupport
function ngLastRecord()
{
  global $conn;
  $stmt = $conn->prepare("SELECT ng_code from caresupportdatabase where ng_status = ? ORDER BY ng_code DESC LIMIT 1");
  $stmt->execute(['positive']);

  return $stmt;
}

//fetch current TEMPERATURE
function currentTemp()
{
  global $conn;
  $stmt = $conn->prepare("SELECT * from templogs ORDER BY id DESC LIMIT 1");
  $stmt->execute();

  return $stmt;
}

//fetch last TEMPERATURE
function lastTemp()
{
  global $conn;
  $stmt = $conn->prepare("SELECT * FROM templogs ORDER BY id DESC LIMIT 1,1");
  $stmt->execute();

  return $stmt;
}

// update critical timestamp
function updateCriticalTime($id, $criticalTime)
{
  global $conn;

  $data = [$criticalTime, $id];

  $stmt = $conn->prepare("UPDATE templogs set criticalDateTime = ? WHERE id = ?");
  $stmt->execute($data);

  return $stmt;
}

//fetch clinic logs
function clinicLog($month = null, $year = null, $testType = null)
{
  global $conn;
  $data = [$month, $year, $testType];
  if ($month == null) {
    $stmt = $conn->prepare("SELECT * FROM cliniclog ORDER BY id DESC");
    $stmt->execute();
    return $stmt;
  }

  if ($testType == 'All') {
    $data = [$month, $year];
    $stmt = $conn->prepare("SELECT id, location, created_at FROM cliniclog where month(created_at) = ? and year(created_at) = ?");
    $stmt->execute($data);
    return $stmt;
  }

  if ($testType == 'DIC') {
    $stmt = $conn->prepare("SELECT id, location, created_at FROM cliniclog where month(created_at) = ? and year(created_at) = ? and location = ?");
    $stmt->execute($data);
    return $stmt;
  }

  if ($testType == 'Mobile') {
    $data = [$month, $year, "DIC"];
    $stmt = $conn->prepare("SELECT id, location, created_at FROM cliniclog where month(created_at) = ? and year(created_at) = ? and location != ?");
    $stmt->execute($data);
    return $stmt;
  }
}

//fetch positive yield for chart
function clinicPositiveYield()
{
  global $conn;

  $data = [];

  $stmt = $conn->prepare("SELECT service_date, sex, age, hiv_result, sti_result, type FROM clinic where hiv_result = 'R' OR sti_result = 'R'");
  $stmt->execute($data);

  return $stmt;
}

//fetch clinicGraph logs
function clinicGraph($month = null, $year = null, $testType = null, $pickKp = null, $pickRoute = null)
{
  global $conn;

  $testType = $testType == 'All' ? '%' : '%' . $testType;

  $pickRoute = $pickRoute == 'All' ? "(.*)" : ($pickRoute == 'walk in' ? "^[w]" : "^[^w]");

  $pickKp = $pickKp == null || $pickKp == 'All' ? "('MSM', 'TG', 'TGM', 'MSW', 'TGSW', 'FSW', 'PWID-Male', 'PWID-Female', 'Male', 'Female')" : "('$pickKp')";

  // $testType = '%';
  // $pickKp = '%';

  $data = [$month, $year, $testType];

  $stmt = $conn->prepare("SELECT sn, type, clinic_id, service_date, count(*) FROM clinic where month(service_date) = ? and year(service_date) = ? and type like ? and sex in $pickKp and referor REGEXP '$pickRoute' group by clinic_id");
  $stmt->execute($data);

  return $stmt;
}

//fetch questionnaire
function questionnaireSummary($month = null, $year = null)
{
  global $conn;
  $data = [$month, $year];
  if ($month == null) {
    $stmt = $conn->prepare("SELECT * FROM clinic_questionnaire ORDER BY id DESC");
    $stmt->execute();
    return $stmt;
  }

  $stmt = $conn->prepare("SELECT * FROM clinic_questionnaire where month(created_at) = ? and year(created_at) = ?");
  $stmt->execute($data);
  return $stmt;
}

//fetch questionnaire
function questionnaireSummary2($month = null, $year = null)
{
  global $conn;

  $data = [$month, $year];

  if ($month == null) {

    $data = [date('Y')];

    $stmt = $conn->prepare("SELECT * FROM clinic_questionnaire2 WHERE year(created_at) = ? ORDER BY id DESC");
    $stmt->execute($data);
    return $stmt;
  }

  $stmt = $conn->prepare("SELECT * FROM clinic_questionnaire2 where month(created_at) = ? and year(created_at) = ?");
  $stmt->execute($data);
  return $stmt;
}

//fetch hiv result
function fetchHivResult($clinic_id)
{
  global $conn;
  $data = [$clinic_id, 'Anti-HIV Positive'];
  $stmt = $conn->prepare("SELECT * FROM cliniclabresult where clinic_id = ? and hiv_result = ? ORDER BY id DESC");
  $stmt->execute($data);

  return $stmt;
}

//fetch tpha result
function fetchTphaResult($clinic_id)
{
  global $conn;
  $data = [$clinic_id, 'Anti-TP Positive'];
  $stmt = $conn->prepare("SELECT * FROM cliniclabresult where clinic_id = ? and tpha_result = ? ORDER BY id DESC");
  $stmt->execute($data);

  return $stmt;
}

//fetch hcv result
function fetchHcvResult($clinic_id)
{
  global $conn;
  $data = [$clinic_id, 'Anti-HCV Reactive', 'Anti-HCV Positive'];
  $stmt = $conn->prepare("SELECT * FROM cliniclabresult where clinic_id = ? and (hcv_result = ? OR hcv_result = ?) ORDER BY id DESC");
  $stmt->execute($data);

  return $stmt;
}

//fetch clinic data for clinic log
function clinicDataForLog($clinic_id)
{
  global $conn;
  $data = [$clinic_id];
  $stmt = $conn->prepare("SELECT sn FROM clinic WHERE clinic_id = ?");
  $stmt->execute($data);

  return $stmt;
}

//fetch clinic data for peer graph
function clinicDataForPeerGraph($start = '', $end = '')
{
  global $conn;

  if ($start == '') {
    $start = checkQuarter()['q1_start'];
    $end = checkQuarter()['q4_end'];
  }
  $data = [$start, $end];

  $stmt = $conn->prepare("SELECT service_date, referor, cbs, hiv_result, sti_result FROM clinic WHERE clinic_id > 0 and service_date >= ? and service_date < ?");
  $stmt->execute($data);

  return $stmt;
}

//fetch pm data
function fetchPmData($id)
{
  global $conn;
  $data = [$id];
  $stmt = $conn->prepare("SELECT * FROM pm WHERE id = ?");
  $stmt->execute($data);
  return $stmt;
}

//fetch nhso data for graph
function nhsoDataForGraph($year = null)
{
  global $conn;
  if ($year == null) {
    $data = [];
    $stmt = $conn->prepare("SELECT * FROM nhso");
    $stmt->execute($data);
    return $stmt;
  }

  $data = [$year];
  $stmt = $conn->prepare("SELECT * FROM nhso where year = ?");
  $stmt->execute($data);

  return $stmt;
}

//fetch nhso retain data for graph
function nhsoRetainDataForGraph($year = null)
{
  global $conn;
  if ($year == null) {
    $data = [];
    $stmt = $conn->prepare("
    SELECT n.*
    FROM `nhso` n
    where
    n.uic in (select c.uic from clinic c where n.uic = c.uic and c.sn > n.sn order by sn desc)
    ");
    $stmt->execute($data);
    return $stmt;
  }

  $data = [$year];
  $stmt = $conn->prepare("
  SELECT n.*
  FROM `nhso` n
  where n.year = ? and
  n.uic in (select c.uic from clinic c where n.uic = c.uic and c.sn > n.sn order by sn desc)
  ");
  $stmt->execute($data);

  return $stmt;
}

//fetch nhso summary
function nhsoSummary($year = null)
{
  global $conn;
  if ($year == null) {
    $data = [];
    $stmt = $conn->prepare("SELECT * FROM nhsosummary");
    $stmt->execute($data);
    return $stmt;
  }

  $data = [$year];
  $stmt = $conn->prepare("SELECT * FROM nhsosummary where year = ?");
  $stmt->execute($data);

  return $stmt;
}

//fetch clinic data for clinic graph
function clinicDataForGraph($clinic_id, $testType = null, $pickKp = null, $pickRoute = null)
{
  global $conn;

  $pickKp = $pickKp == null || $pickKp == 'All' ? "('MSM', 'TG', 'TGM', 'MSW', 'TGSW', 'FSW', 'PWID-Male', 'PWID-Female', 'Male', 'Female')" : "('$pickKp')";

  $pickRoute = $pickRoute != 'All' ? ($pickRoute == 'walk in' ? "^[w]" : "^[^w]") : "(.*)";


  if ($testType == null) {
    $data = [$clinic_id];
    $stmt = $conn->prepare("SELECT hiv_result, sti_result FROM clinic WHERE clinic_id = ? and sex in $pickKp and referor REGEXP '$pickRoute'");
    $stmt->execute($data);

    return $stmt;
  }

  if ($testType == 'HIV') {
    $data = [$clinic_id, 'R'];
    $stmt = $conn->prepare("SELECT hiv_result, sti_result FROM clinic WHERE clinic_id = ? and hiv_result = ? and sex in $pickKp and referor REGEXP '$pickRoute'");
    $stmt->execute($data);

    return $stmt;
  }

  if ($testType == 'TPHA') {
    $data = [$clinic_id, 'R'];
    $stmt = $conn->prepare("SELECT hiv_result, sti_result FROM clinic WHERE clinic_id = ? and sti_result = ? and sex in $pickKp and referor REGEXP '$pickRoute'");
    $stmt->execute($data);

    return $stmt;
  }
}

//clinicNewCaseCount
function clinicNewCaseCount($clinic_id)
{
  global $conn;
  $data = [$clinic_id];
  $stmt = $conn->prepare("SELECT uic FROM clinicnewcase WHERE clinic_id = ?");
  $stmt->execute($data);

  return $stmt;
}

//retain positive eachday that have clinic
function countRetainPos($clinicDate)
{
  global $conn;
  $data = [$clinicDate, $clinicDate . " 23:59:59"];
  $stmt = $conn->prepare("SELECT id FROM retaincare WHERE retainDate > ? and retainDate < ?");
  $stmt->execute($data);

  return $stmt;
}

//retain negative eachday that have clinic
function countRetainNeg($clinicDate)
{
  global $conn;
  $data = [$clinicDate, $clinicDate . " 23:59:59"];
  $stmt = $conn->prepare("SELECT id FROM retainnegative WHERE retainDate > ? and retainDate < ?");
  $stmt->execute($data);

  return $stmt;
}

function ageByBirthDate($birthDate = '', $dateCompare = '')
{

  if ($birthDate == '') $birthDate = date('Y-m-d');
  if ($dateCompare == '') $dateCompare = date('Y-m-d H:i:s');
  //explode the date to get month, day and year
  $birthDateSplit = explode("-", $birthDate);
  //get age from date or birthdate
  $age = (date(
    "md",
    date("U", mktime(0, 0, 0, (int)$birthDateSplit[1], (int)$birthDateSplit[2], (int)$birthDateSplit[0]))
  ) > date("md", strtotime($dateCompare)) ?
    ((date("Y", strtotime($dateCompare)) - $birthDateSplit[0]) - 1) : (date("Y", strtotime($dateCompare)) - $birthDateSplit[0]));

  return $age;
}

//fetch clinic negative case for birth day sms
function birthdaysmsfetch()
{
  global $conn;
  $birthDate = date('d');
  $month = date('m');
  $data = [$birthDate, $month, 'N', 'I'];
  $sql = "SELECT sn, service_date, uic, nickname, phone FROM clinic 
       WHERE SUBSTRING(uic,3,2) = ? AND SUBSTRING(uic,5,2) = ? AND ( hiv_result = ? OR hiv_result = ? ) AND sn IN (SELECT MAX(sn) FROM clinic GROUP BY uic) AND sex not in ('Female', 'FSW')";
  $stmt = $conn->prepare($sql);
  $stmt->execute($data);

  return $stmt;
}

//fetch clinic for valentine sms 2020
function valentine2020smsfetch()
{
  global $conn;

  $data = [checkQuarter()['q1_start']];

  $sql = "SELECT *
  from clinic
  where 1
  and sn in (select max(sn) from clinic group by uic)
  and service_date < ?
  and hiv_result <> 'R'
  and hiv_result <> '-'
  and char_length(phone) = 10
  and char_length(id_card) = 13
  and substring(id_card,1,1) in (1,2,3,4,5,8)
  and sex not in ('Male','Female')
  and type <> 'Mobile'
  and (phoneStatus NOT IN ('เบอร์ยกเลิก', 'ไม่มีเบอร์นี้') OR phoneStatus is NULL)
  and uic = 'ศด120228'
  group by uic";

  $stmt = $conn->prepare($sql);
  $stmt->execute($data);

  return $stmt;
}

//fetch clinic for prep to send sms transfer
function prepTransferSMSFetch()
{
  global $conn;
  $data = [];
  $sql = "SELECT sn, service_date, uic, prep, nickname, phone FROM clinic 
       WHERE prep != '' AND sn IN (SELECT MAX(sn) FROM clinic GROUP BY prep)";
  $stmt = $conn->prepare($sql);
  $stmt->execute($data);

  return $stmt;
}

//fetch prep for resend sms not shutdown
function prepNotShutdownInform()
{
  global $conn;
  $data = [];
  $sql = "SELECT * FROM prepsms";
  $stmt = $conn->prepare($sql);
  $stmt->execute($data);

  return $stmt;
}

//fetch clinic negative case for birth day sms
function testMeNowTokenFetch()
{
  global $conn;
  $data = [];

  $stmt = $conn->prepare("SELECT * FROM testmenowtoken");
  $stmt->execute($data);

  return $stmt;
}

//insert clinic negative case for birth day sms
function insertSMSRecord($sn, $uic, $nickname, $phone, $smsStatus)
{
  global $conn;
  $data = [$sn, $uic, $nickname, $phone, $smsStatus];
  $sql = "INSERT into birthdaysms (sn, uic, nickname, phone, smsStatus) 
      VALUES (?, ? ,? ,?, ?)";
  $stmt = $conn->prepare($sql);
  $stmt->execute($data);

  return $stmt;
}

//insert valentine's day 2020 sms record
function addValentineSMSRecord($sn, $uic, $nickname, $phone, $smsStatus)
{
  global $conn;
  $data = [$sn, $uic, $nickname, $phone, $smsStatus];
  $sql = "INSERT into valentine2020sms (sn, uic, nickname, phone, smsStatus) 
      VALUES (?, ? ,? ,?, ?)";
  $stmt = $conn->prepare($sql);
  $stmt->execute($data);

  return $stmt;
}

//insert prep sms notify for transfer
function insertPrEPTransfer($sn, $service_date, $uic, $prepID, $nickname, $phone, $sms = '')
{
  global $conn;
  $data = [$sn, $service_date, $uic, $nickname, $prepID, $phone, $sms];
  $sql = "INSERT into prepsms (sn, last_service_date, uic, name, prep_id, phone, sms) 
      VALUES (?, ?, ? ,? ,?, ?, ?)";
  $stmt = $conn->prepare($sql);
  $stmt->execute($data);

  return $stmt;
}

//insert clinic negative case for retain negative
function insertRetainSMSRecord($sn, $uic, $nickname, $phone, $smsStatus)
{
  global $conn;
  $data = [$sn, $uic, $nickname, $phone, $smsStatus];
  $sql = "INSERT into negretainsms (sn, uic, nickname, phone, smsStatus) 
      VALUES (?, ? ,? ,?, ?)";
  $stmt = $conn->prepare($sql);
  $stmt->execute($data);

  return $stmt;
}

//insert smsresponse
function insertSmsResponse($topic, $comment = '')
{
  global $conn;
  $data = [$topic, $comment];
  $sql = "INSERT into smsresponse (sms_title, comment) 
      VALUES (?, ?)";
  $stmt = $conn->prepare($sql);
  $stmt->execute($data);

  return $stmt;
}

//update birthdaysms table when user click sms link
function updateSMSClick($uic)
{
  global $conn;

  $data = [date('Y-m-d H:i:s'), $uic];
  $sql = "UPDATE birthdaysms SET responseDate = ? WHERE uic = ?";
  $stmt = $conn->prepare($sql);
  $stmt->execute($data);

  return $stmt;
}

// convert the $_FILES array to the cleaner array
function reArrayFiles(&$file_post)
{

  $file_ary = array();
  $file_count = count($file_post['name']);
  $file_keys = array_keys($file_post);

  for ($i = 0; $i < $file_count; $i++) {
    foreach ($file_keys as $key) {
      $file_ary[$i][$key] = $file_post[$key][$i];
    }
  }

  return $file_ary;
}

function pre_r($array)
{
  echo '<pre>';
  print_r($array);
  echo '</pre>';
}

// check if serialize or not 
function checkSerialize($source, $separate = ', ')
{
  $data = @unserialize($source);
  $textData = '';
  if ($data !== false) {

    $textData = implode($separate, array_filter(unserialize($source)));
  } else {
    $textData = $source;
  }

  return $textData;
}

// fetch birthDaySMS data
function fetchBirthDaySMS()
{
  global $conn;

  $data = [];
  $sql = "SELECT b.*,c.service_date,c.hiv_result,c.sti_result FROM birthdaysms AS b LEFT JOIN clinic AS c ON c.uic = b.uic and c.sn = (select min(a.sn) from clinic a where a.uic = b.uic and a.service_date > b.created_at )";
  $stmt = $conn->prepare($sql);
  $stmt->execute($data);

  return $stmt;
}

// fetch Negative Retention SMS data
function fetchNegativeRetentionSMS()
{
  global $conn;

  $data = [];
  $sql = "SELECT * FROM negretainsms";
  $stmt = $conn->prepare($sql);
  $stmt->execute($data);

  return $stmt;
}

// fetch last clinic HN
function fetchLastHnRecord()
{
  global $conn;

  $data = [];
  $stmt = $conn->prepare("SELECT hn FROM clinic order by sn DESC limit 1");
  $stmt->execute($data);

  return $stmt;
}

// fetch file_code by uic
function fetchFileCodeByUic($uic)
{
  global $conn;

  $data = [$uic];
  $stmt = $conn->prepare("SELECT sn, hn, file_code FROM clinic where (file_code != '' AND file_code is not null) AND uic = ? order by file_code DESC limit 1");
  $stmt->execute($data);

  return $stmt;
}

// fetch last file_code
function fetchLastFileCode()
{
  global $conn;

  $data = [];
  $stmt = $conn->prepare("
        SELECT sn, hn, file_code 
        FROM clinic 
        WHERE CHAR_LENGTH(file_code) = 15 
          AND file_code REGEXP '^[A-Za-z]{6}[0-9]{6}/[0-9]{2}$'
        ORDER BY file_code DESC 
        LIMIT 1
    ");
  $stmt->execute($data);
  return $stmt;
}

// fetch clinic for medical clinic log
function fetchMedicalClinicLog()
{
  global $conn;

  $data = [checkQuarter()['q1_start']];
  $stmt = $conn->prepare("SELECT sn,hn,service_date,uic,firstname,lastname,sex,age,mobile FROM clinic where service_date >= ?");
  $stmt->execute($data);

  return $stmt;
}

// hiv refer List
function hivReferList()
{
  global $conn;

  $data = ['ต้องการส่งต่อ', '%สิ้นสุดการส่งต่อ%', '%ยกเลิกการส่งต่อ%', '%สิ้นสุดการส่งต่อ%'];
  $stmt = $conn->prepare("SELECT * FROM caresupportdatabase WHERE hiv_want_refer=? AND hivReferNowStatus not Like ? AND (hivReferDoneStatus not Like ? AND hivReferDoneStatus not Like ? OR hivReferDoneStatus is null)");
  $stmt->execute($data);

  return $stmt;
}

// tpha refer List
function tphaReferList()
{
  global $conn;

  $data = ['เห็นควรส่งรักษา', '%สิ้นสุดการส่งต่อ%', '%ยกเลิกการส่งต่อ%', '%สิ้นสุดการส่งต่อ%'];
  $stmt = $conn->prepare("SELECT * FROM caresupportdatabase WHERE tpha_refer_to_treat = ? AND tphaReferNowStatus not Like ? AND (tphaReferDoneStatus not Like ? AND tphaReferDoneStatus not Like ? OR tphaReferDoneStatus is null)");
  $stmt->execute($data);

  return $stmt;
}

// stis refer List
function stisReferList()
{
  global $conn;

  $data = ['เห็นควรส่งรักษา', '%สิ้นสุดการส่งต่อ%', '%ยกเลิกการส่งต่อ%', '%สิ้นสุดการส่งต่อ%'];
  $stmt = $conn->prepare("SELECT * FROM caresupportdatabase WHERE stis_refer_to_treat =? AND stisReferNowStatus not Like ? AND (stisReferDoneStatus not Like ? AND stisReferDoneStatus not Like ? OR stisReferDoneStatus is null)");
  $stmt->execute($data);

  return $stmt;
}

// refer by uic
function referDataByUic($uic)
{
  global $conn;

  $data = [$uic];
  $stmt = $conn->prepare("SELECT * FROM refertotreat where uic=?");
  $stmt->execute($data);

  return $stmt;
}

// last refer Data
function lastReferData($uic, $topic)
{
  global $conn;

  $data = [$uic, $topic];
  $stmt = $conn->prepare("SELECT * FROM refertotreat where uic=? AND topic=? ORDER BY referDate DESC LIMIT 1");
  $stmt->execute($data);

  return $stmt;
}

// hiv retain List
function hivRetainList($topic)
{
  global $conn;

  $data = [$topic];
  $stmt = $conn->prepare("SELECT * FROM retaincare WHERE topic=? ORDER BY retainDate DESC");
  $stmt->execute($data);

  return $stmt;
}

// last retain Data
function lastRetainData($uic, $topic)
{
  global $conn;

  $data = [$uic, $topic];
  $stmt = $conn->prepare("SELECT * FROM retaincare WHERE uic = ? AND topic=? ORDER BY retainDate DESC LIMIT 1");
  $stmt->execute($data);

  return $stmt;
}

// Refer Next Date Color
function referDateColor($date)
{
  $data = [];
  // nextReferDate Color
  if ($date != '') {
    if (strtotime($date) >= strtotime(Date("Y-m-d")) - 86400) {
      if (strtotime($date) > strtotime(Date("Y-m-d")) + 86400) {
        $next = labelbig(dateOnly($date), 'success');
        $status = 'less';
      } else {
        $next = labelbig(dateOnly($date), 'warning');
        $status = 'on';
      }
    } else {
      if (strtotime($date) < strtotime(Date("Y-m-d")) - 86400 && $date != 'เบอร์ปิด/ เบอร์โทรยกเลิก' && $date != 'เบอร์ผิด/ ผู้รับระบุไม่ใช่เคส' && $date != 'ติดต่อได้ เคสปฎิเสธการให้ข้อมูล' && $date != 'ติดต่อได้ ได้รับข่าวการเสียชีวิต') {
        $next = labelbig(dateOnly($date), 'danger');
        $status = 'over';
      }
    }
  } else {
    $next = '';
    $status = 'no';
  }
  $data['next'] = $next;
  $data['status'] = $status;
  return $data;
  // nextReferDate Color
}

// Retain Next Date Color
function retainDateColor($date)
{
  $data = [];
  // nextRetainDate Color
  if ($date != '') {
    if (strtotime($date) >= strtotime(Date("Y-m-d")) - 259200) {
      if (strtotime($date) > strtotime(Date("Y-m-d")) + 259200) {
        $next = labelbig(dateOnly($date), 'success');
        $status = 'less';
      } else {
        $next = labelbig(dateOnly($date), 'warning');
        $status = 'on';
      }
    } else {
      if (strtotime($date) < strtotime(Date("Y-m-d")) - 259200 && $date != 'เบอร์ปิด/ เบอร์โทรยกเลิก' && $date != 'เบอร์ผิด/ ผู้รับระบุไม่ใช่เคส' && $date != 'ติดต่อได้ เคสปฎิเสธการให้ข้อมูล' && $date != 'ติดต่อได้ ได้รับข่าวการเสียชีวิต' && $date != 'อยู่ระหว่างการส่งต่อ') {
        $next = labelbig(dateOnly($date), 'danger');
        $status = 'over';
      }
    }
    if ($date == 'เบอร์ปิด/ เบอร์โทรยกเลิก') {
      $next = labelbig($date, 'black');
      $status = 'close';
    }
    if ($date == 'เบอร์ผิด/ ผู้รับระบุไม่ใช่เคส') {
      $next = labelbig($date, 'navy');
      $status = 'wrong';
    }
    if ($date == 'ติดต่อได้ เคสปฎิเสธการให้ข้อมูล') {
      $next = labelbig($date, 'danger');
      $status = 'refuse';
    }
    if ($date == 'ติดต่อได้ ได้รับข่าวการเสียชีวิต') {
      $next = labelbig($date, 'purple');
      $status = 'died';
    }
    if ($date == 'อยู่ระหว่างการส่งต่อ') {
      $next = labelbig($date, 'teal');
      $status = 'refer';
    }
  } else {
    $next = '';
    $status = 'no';
  }
  $data['next'] = $next;
  $data['status'] = $status;
  return $data;
  // nextRetainDate Color
}

//sum dashboard
function sumcbs($month, $year, $cbs = "")
{
  $sum = 0;
  if ($month == 1 or $month == 3 or $month == 5 or $month == 7 or $month == 8 or $month == 10 or $month == 12) {
    $days = 31;
  } else if ($month == 4 or $month == 6 or $month == 9 or $month == 11) {
    $days = 30;
  } else {
    $days = 28;
  }
  if ($cbs == "") {
    $cbsinput = " AND cbs<>'" . $cbs . "'";
  } else {
    $cbsinput = " AND cbs='" . $cbs . "'";
  }
  for ($i = 1; $i <= $days; $i++) {
    $sum += total($i, $month, $year, $cbsinput);
  }
  return $sum;
}

function cbsprogressbar($cbs, $color = "green", $month = 1, $year = 2018)
{
  echo '
		<div class="row">
		<div class="col-xs-3">
			<div class="row">
				<span class="pull-right">' . $cbs . '</span>
			</div>
		</div>
		<div class="col-xs-9">
			<div class="progress margin-left-auto">
				<div class="progress-bar progress-bar-' . $color . '" role="progressbar" aria-valuenow="' . sumcbs($month, $year, $cbs) . '" aria-valuemin="0" aria-valuemax="150" style="width: ' . (100 / 150) * sumcbs($month, $year, $cbs) . '%">' .
    sumcbs($month, $year, $cbs) . '
				</div>
			</div>
		</div>
	</div>';
}

function total($dayinput = "NOT null", $monthinput, $yearinput, $cbs = " AND cbs<>''")
{
  global $conn;
  $fetchdate = $conn->query('SELECT COUNT(id) AS countReach FROM reach WHERE DAY(reachdate) =' . $dayinput . ' AND MONTH(reachdate) = ' . $monthinput . ' AND YEAR(reachdate) = ' . $yearinput . $cbs . 'ORDER BY reachdate DESC');
  $total = $fetchdate->fetch(PDO::FETCH_ASSOC);
  return $total['countReach'];
}

//retain count function month year and cbs
function retainCount($month = 1, $year = 2018, $cbs = "%")
{
  global $conn;
  $retainMonthQuery = $conn->prepare('SELECT count(id) AS retainCount FROM retaincare where cbs LIKE :cbs AND MONTH(retainDate) = :month AND YEAR(retainDate) = :year');
  $retainMonthQuery->bindParam(':month', $month);
  $retainMonthQuery->bindParam(':year', $year);
  $retainMonthQuery->bindParam(':cbs', $cbs);
  $retainMonthQuery->execute();
  $retainMonth = $retainMonthQuery->fetch(PDO::FETCH_ASSOC);
  $result = $retainMonth['retainCount'];
  return $result;
}

function testColor($data, $size = '')
{
  if (in_array($data, ['R', 'P', 'CT Detected', 'NG Detected'])) return labelbig($data, 'danger', $size);
  if (in_array($data, ['N', 'CT Not Detected', 'NG Not Detected'])) return labelbig($data, 'success', $size);
  if (in_array($data, ['I', 'ผล CT ยังไม่ออก', 'ผล NG ยังไม่ออก'])) return labelbig($data, 'warning', $size);
  if (in_array($data, ['-', '', NULL])) return labelbig('-', 'black', $size);
  return labelbig($data, 'secondary', $size);
}

function retainProgressBar($cbs, $color = "green", $valuenow = 0, $valuemax = 100, $valueAll = 0)
{
  $percent = (100 / $valuemax) * $valuenow;

  return "
		<div class='row'>
		<div class='col-xs-3'>
			<div class='row'>
				<span class='pull-right'>$cbs</span>
			</div>
		</div>
		<div class='col-xs-9'>
			<div class='progress margin-left-auto'>
				<div class='progress-bar progress-bar-$color' role='progressbar' aria-valuenow='$valuenow' aria-valuemin='0' aria-valuemax='$valuemax' style='width: $percent%'>$valuenow | $valueAll</div>
			</div>
		</div>
	</div>";
}

//get retain Count
function getRetainCountAll($cbs, $date = null)
{
  global $conn;

  $date = $date ?? '2020-07-01';

  $data = [$cbs, $date];
  $stmt = $conn->prepare("SELECT id FROM caresupport_activity where cbs = ? and service_date >= ?");
  $stmt->execute($data);

  return $stmt;
}

//get retain Count
function getRetainCountSuccess($cbs, $date = null)
{
  global $conn;

  $date = $date ?? '2020-07-01';

  $data = [$cbs, $date, ''];
  $stmt = $conn->prepare("SELECT id FROM caresupport_activity where cbs = ? and service_date >= ? and hiv_remark != ? group by uic");
  $stmt->execute($data);

  return $stmt;
}

//get open clinic Date
function getOpenClinicDate($clinicID)
{
  global $conn;
  $data = [$clinicID];
  $stmt = $conn->prepare("SELECT * FROM cliniclog where id = ?");
  $stmt->execute($data);

  $fetch = $stmt->fetch();

  return $fetch;
}

// fetch working users
function fetchWorkingUsers()
{
  global $conn;

  $data = [];

  $stmt = $conn->prepare("SELECT id,username,nickname,role FROM users where status = 'working' order by username");
  $stmt->execute($data);

  $users = $stmt->fetchall();

  return $users;
}

//check index seeder exists record for clinic
function checkIndexSeederRecord($source_id, $uic, $type = '', $date = '', $seedID = '')
{
  global $conn;

  if ($seedID > 0) {
    $data = [$seedID];
    $stmt = $conn->prepare("SELECT * FROM index_seeder where id = ? limit 1");
    $stmt->execute($data);

    return $stmt;
  }

  $data = [$source_id, $uic, $type, $date];
  $stmt = $conn->prepare("SELECT * FROM index_seeder where source_id = ? and uic = ? and source_type = ? and date(created_at) = ? limit 1");
  $stmt->execute($data);

  return $stmt;
}

//check index seeder exists record for edit
function checkIndexSeederRecordForEdit($id)
{
  global $conn;
  $data = [$id];
  $stmt = $conn->prepare("SELECT 
  a.*, b.nickname as clinic_name, c.nickname as care_name
  FROM index_seeder a 
  LEFT JOIN clinic b ON a.source_id = b.clinic_id AND a.uic = b.uic
  LEFT JOIN caresupportdatabase c ON a.source_id = c.id AND a.uic = c.uic
  where a.id = ?");
  $stmt->execute($data);

  return $stmt;
}

//check index seeder Count record
function checkIndexSeederCount($uic)
{
  global $conn;
  $data = [$uic];
  $stmt = $conn->prepare("SELECT * FROM index_seeder where uic = ?");
  $stmt->execute($data);

  return $stmt;
}

// update index seeder record
function updateIndexSeederRecord($source)
{
  global $conn;
  $data = [
    $source['seed_type'],
    $source['staff'],
    $source['offer'],
    $source['notOfferReason'],
    $source['accept'],
    $source['notAcceptReason'],
    $source['linkRoute'],
    $source['partnerCount'],
    $source['comment'],
    $source['id']
  ];
  $stmt = $conn->prepare("UPDATE index_seeder SET 
  seed_type = ?,
  staff = ?,
  offer = ?,
  notOfferReason = ?,
  accept = ?,
  notAcceptReason = ?,
  linkRoute = ?,
  partnerCount = ?,
  comment = ?
  where id = ? limit 1");
  $stmt->execute($data);

  return $stmt;
}

// update index seeder ottLink
function updateIndexSeederOttLink($source)
{
  global $conn;
  $data = [
    $source['ottLink'],
    $source['id'],
    $source['uic'],
    $source['source_type']
  ];
  $stmt = $conn->prepare("UPDATE index_seeder SET 
  ottLink = ?

   where id = ? and uic = ? and source_type = ?");
  $stmt->execute($data);

  return $stmt;
}

// update index seeder snsCoupon
function updateIndexSeederSnsCoupon($source)
{
  global $conn;
  $data = [
    $source['snsCoupon'],
    $source['id'],
    $source['uic'],
    $source['source_type']
  ];
  $stmt = $conn->prepare("UPDATE index_seeder SET 
  snsCoupon = ?

   where id = ? and uic = ? and source_type = ?");
  $stmt->execute($data);

  return $stmt;
}

// update index_seeder partner count
function updatePartnerCount($source)
{

  $check = fetchIndexPartner($source['seed_uic'], $source['seed_id'])->rowCount();

  $partnerCount = 1;

  if ($check > 0) {
    $partnerCount = $check + 1;
  }

  global $conn;
  $data = [
    $partnerCount,
    $source['seed_id']
  ];
  $stmt = $conn->prepare("UPDATE index_seeder SET partnerCount = ?
   where id = ?");
  $stmt->execute($data);

  return $stmt;
}

// insert new index seeder record
function insertIndexSeederRecord($source)
{

  $offerCount = 1;

  $check = checkIndexSeederCount($source['uic'])->rowCount();

  if ($check > 0) {
    $offerCount = $check + 1;
  }

  global $conn;

  $data = [
    $source['source_id'],
    $source['uic'],
    $source['source_type'],
    $source['seed_type'],
    $source['staff'],
    $offerCount,
    $source['offer'],
    $source['notOfferReason'],
    $source['accept'],
    $source['notAcceptReason'],
    $source['partnerCount'],
    $source['linkRoute'],
    $source['ottLink'],
    $source['snsCoupon'],
    $source['comment']
  ];

  $stmt = $conn->prepare("INSERT INTO index_seeder (
    source_id,
    uic,
    source_type,
    seed_type,
    staff,
    offerCount,
    offer,
    notOfferReason,
    accept,
    notAcceptReason,
    partnerCount,
    linkRoute,
    ottLink,
    snsCoupon,
    comment
    ) VALUE (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
  $stmt->execute($data);

  return $stmt;
}

// INDEX TESTING
// save new index partner 
function saveNewIndexPartner($formData)
{
  global $conn;

  extract($formData);

  $data = [
    $age,
    $sex,
    $birthday,
    $email,
    $emotion_type,
    $facebook,
    $firstname,
    $hornet_id,
    $ipv_mental,
    $ipv_none,
    $ipv_physical,
    $ipv_sex,
    $lastname,
    $line_id,
    $nickname,
    $partner_comment,
    $people_id,
    $phone,
    $relation,
    $seed_id,
    $seed_uic,
    $seed_name,
    $service_type,
    'initialize',
    $contract_date,
    $twitter_id,
  ];
  $stmt = $conn->prepare("INSERT into index_partner (age,sex,birthday,email,emotion_type,facebook,firstname,hornet_id,ipv_mental,ipv_none,ipv_physical,ipv_sex,lastname,line_id,nickname,partner_comment,people_id,phone,relation,seed_id,seed_uic,seed_name,service_type,status,contract_date,twitter_id) 
  VALUE 
  (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
  $stmt->execute($data);

  return $stmt->rowCount();
}

// save edit index partner 
function saveEditIndexPartner($formData)
{
  global $conn;

  extract($formData);

  $data = [
    $age,
    $sex,
    $birthday,
    $email,
    $emotion_type,
    $facebook,
    $firstname,
    $hornet_id,
    $ipv_mental,
    $ipv_none,
    $ipv_physical,
    $ipv_sex,
    $lastname,
    $line_id,
    $nickname,
    $partner_comment,
    $people_id,
    $phone,
    $relation,
    $seed_id,
    $seed_uic,
    $seed_name,
    $service_type,
    $contract_date,
    $twitter_id,
    $id
  ];
  $stmt = $conn->prepare("UPDATE index_partner 
        SET 
          age = ?, sex = ?, birthday = ?, email = ?, emotion_type = ?, facebook = ?, firstname = ?, hornet_id = ?, ipv_mental = ?, ipv_none = ?, ipv_physical = ?, ipv_sex = ?, lastname = ?, line_id = ?, nickname = ?, partner_comment = ?, people_id = ?, phone = ?, relation = ?, seed_id = ?, seed_uic = ?, seed_name = ?, service_type = ?, contract_date = ?, twitter_id = ?
        where 
          id = ?");

  $stmt->execute($data);

  return $stmt->rowCount();
}

// fetch index partner 
function fetchIndexPartner($seed_uic = '', $seed_id)
{
  global $conn;

  $data = [$seed_id];
  $stmt = $conn->prepare("SELECT * FROM index_partner WHERE seed_id = ?");
  $stmt->execute($data);

  return $stmt;
}

// fetch seeder 
function fetchSeeder($startDate, $endDate)
{
  global $conn;

  $data = [$startDate, $endDate];

  $stmt = $conn->prepare("SELECT 
  a.*, 
  b.sex as sex_clinic, 
  b.service_date as clinic_date, 
  d.kp as kp_activity,
  d.service_date as activity_date,
  e.kp as kp_retain,
  e.retainDate as retain_date
  FROM index_seeder a 
  LEFT JOIN clinic b ON a.source_id = b.clinic_id AND a.uic = b.uic AND a.source_type = 'clinic'
  LEFT JOIN caresupport_activity d ON d.id = (SELECT MAX(id) FROM caresupport_activity WHERE uic = a.uic AND date(created_at) = date(a.created_at))
  LEFT JOIN retaincare e ON e.id = (SELECT MAX(id) FROM retaincare WHERE uic = a.uic AND date(created_at) = date(a.created_at) )
  WHERE a.created_at >= ? AND a.created_at < ?");
  $stmt->execute($data);

  return $stmt;
}

// fetch partner 
function fetchPartner()
{
  global $conn;

  $data = [];
  $stmt = $conn->prepare("SELECT 
  a.*, 
  b.seed_type, 
  d.sex as clinic_sex, 
  c.sex as care_sex
  FROM index_partner a
  LEFT JOIN index_seeder b ON a.seed_id = b.id
  LEFT JOIN clinic d ON b.source_id = d.clinic_id AND b.uic = d.uic AND b.source_type = 'clinic'
  LEFT JOIN caresupportdatabase c ON b.uic = c.uic
  ");
  $stmt->execute($data);

  return $stmt;
}

// fetch clinic risk data 
function fetchClinicByUicAndClinic_id($clinic_id, $uic)
{
  global $conn;

  $data = [$clinic_id, $uic];
  $stmt = $conn->prepare("SELECT risk FROM clinic where clinic_id = ? and uic = ? limit 1");
  $stmt->execute($data);

  return $stmt;
}

// fetch referList All
function fetchReferAllList()
{
  global $conn;

  $data = ['ต้องการส่งต่อ', '%สิ้นสุดการส่งต่อ%', '%ยกเลิกการส่งต่อ%', '%สิ้นสุดการส่งต่อ%', 'เห็นควรส่งรักษา', '%สิ้นสุดการส่งต่อ%', '%ยกเลิกการส่งต่อ%', '%สิ้นสุดการส่งต่อ%', 'เห็นควรส่งรักษา', '%สิ้นสุดการส่งต่อ%', '%ยกเลิกการส่งต่อ%', '%สิ้นสุดการส่งต่อ%'];
  $stmt = $conn->prepare("SELECT * FROM caresupportdatabase WHERE 
  hiv_want_refer=? AND 
  hivReferNowStatus not Like ? AND 
  (hivReferDoneStatus not Like ? AND hivReferDoneStatus not Like ? OR hivReferDoneStatus is null) OR
  tpha_refer_to_treat = ? AND 
  tphaReferNowStatus not Like ? AND 
  (tphaReferDoneStatus not Like ? AND tphaReferDoneStatus not Like ? OR tphaReferDoneStatus is null) OR
  tpha_refer_to_treat = ? AND 
  tphaReferNowStatus not Like ? AND 
  (tphaReferDoneStatus not Like ? AND tphaReferDoneStatus not Like ? OR tphaReferDoneStatus is null)
  ");
  $stmt->execute($data);

  return $stmt;
}

function hiv_refer_lists()
{
  global $conn;

  $data = ['ส่งต่อ'];
  $stmt = $conn->prepare("SELECT 
  a.*,
  a.hiv_refer_status as care_hiv_refer_status,
  a.syphilis_refer_status as care_syphilis_refer_status,
  a.hcv_refer_status as care_hcv_refer_status,
  b.method,
  b.care_route,
  b.method_remark,
  b.common_next_retain_date,
  b.hiv_refer_status as activity_hiv_refer_status,
  b.syphilis_refer_status as activity_syphilis_refer_status,
  b.hcv_refer_status as activity_hcv_refer_status
  FROM caresupportdatabase a
  LEFT JOIN caresupport_activity b ON a.uic = b.uic AND b.id = (select max(c.id) from caresupport_activity c where c.uic = a.uic)
  WHERE a.hiv_refer_status = ?");
  $stmt->execute($data);

  $response = $stmt->fetchall(PDO::FETCH_ASSOC);

  foreach ($response as $key => $value) {
    $response[$key]['topic'] = 'HIV';
  }

  return $response;
}

function syphilis_refer_lists()
{
  global $conn;

  $data = ['ส่งต่อ'];
  $stmt = $conn->prepare("SELECT 
  a.*,
  a.hiv_refer_status as care_hiv_refer_status,
  a.syphilis_refer_status as care_syphilis_refer_status,
  a.hcv_refer_status as care_hcv_refer_status,
  b.method,
  b.care_route,
  b.method_remark,
  b.common_next_retain_date,
  b.hiv_refer_status as activity_hiv_refer_status,
  b.syphilis_refer_status as activity_syphilis_refer_status,
  b.hcv_refer_status as activity_hcv_refer_status
  FROM caresupportdatabase a
  LEFT JOIN caresupport_activity b ON a.uic = b.uic AND b.id = (select max(c.id) from caresupport_activity c where c.uic = a.uic)
  WHERE a.syphilis_refer_status = ?");
  $stmt->execute($data);

  $response = $stmt->fetchall(PDO::FETCH_ASSOC);

  foreach ($response as $key => $value) {
    $response[$key]['topic'] = 'Syphilis';
  }

  return $response;
}

function hcv_refer_lists()
{
  global $conn;

  $data = ['ส่งต่อ'];
  $stmt = $conn->prepare("SELECT 
  a.*,
  a.hiv_refer_status as care_hiv_refer_status,
  a.syphilis_refer_status as care_syphilis_refer_status,
  a.hcv_refer_status as care_hcv_refer_status,
  b.method,
  b.care_route,
  b.method_remark,
  b.common_next_retain_date,
  b.hiv_refer_status as activity_hiv_refer_status,
  b.syphilis_refer_status as activity_syphilis_refer_status,
  b.hcv_refer_status as activity_hcv_refer_status
  FROM caresupportdatabase a
  LEFT JOIN caresupport_activity b ON a.uic = b.uic AND b.id = (select max(c.id) from caresupport_activity c where c.uic = a.uic)
  WHERE a.hcv_refer_status = ?");
  $stmt->execute($data);

  $response = $stmt->fetchall(PDO::FETCH_ASSOC);

  foreach ($response as $key => $value) {
    $response[$key]['topic'] = 'HCV';
  }

  return $response;
}

// fetch referList HIV
function fetchReferHIVList()
{
  global $conn;

  $data = ['ต้องการส่งต่อ', '%สิ้นสุดการส่งต่อ%', '%ยกเลิก%', '%สิ้นสุด%'];
  $stmt = $conn->prepare("SELECT * FROM caresupportdatabase WHERE 
  hiv_want_refer=? AND 
  hivReferNowStatus not Like ? AND 
  (hivReferDoneStatus not Like ? AND hivReferDoneStatus not Like ? OR hivReferDoneStatus is null)");
  $stmt->execute($data);

  return $stmt;
}

// fetch referList TPHA
function fetchReferTPHAList()
{
  global $conn;

  $data = ['เห็นควรส่งรักษา', '%สิ้นสุดการส่งต่อ%', '%ยกเลิก%', '%สิ้นสุด%'];
  $stmt = $conn->prepare("SELECT * FROM caresupportdatabase WHERE 
  tpha_refer_to_treat = ? AND 
  tphaReferNowStatus not Like ? AND 
  (tphaReferDoneStatus not Like ? AND tphaReferDoneStatus not Like ? OR tphaReferDoneStatus is null)");
  $stmt->execute($data);

  return $stmt;
}

// fetch referList STIs
function fetchReferSTIsList()
{
  global $conn;

  $data = ['เห็นควรส่งรักษา', '%สิ้นสุดการส่งต่อ%', '%ยกเลิก%', '%สิ้นสุด%'];
  $stmt = $conn->prepare("SELECT * FROM caresupportdatabase WHERE stis_refer_to_treat =? AND 
  stisReferNowStatus not Like ? AND 
  (stisReferDoneStatus not Like ? AND stisReferDoneStatus not Like ? OR stisReferDoneStatus is null)");
  $stmt->execute($data);

  return $stmt;
}

// fetch referList
function fetchLastRefer($uic, $topic)
{
  global $conn;

  $data = [$uic, $topic];

  $stmt = $conn->prepare("SELECT * FROM refertotreat WHERE uic = ? AND topic=? ORDER BY referDate DESC LIMIT 1");
  $stmt->execute($data);

  return $stmt;
}

// fetch all refer
function fetchAllReferByTopic($uic, $topic)
{
  global $conn;

  $data = [$uic, $topic];

  $stmt = $conn->prepare("SELECT * FROM refertotreat WHERE uic = ? AND topic=? ORDER BY referDate");
  $stmt->execute($data);

  return $stmt;
}

// insert new export file record
function saveExportRecord($tablename, $filePath, $staff)
{
  global $conn;

  $data = [$tablename, $filePath, $staff];

  $stmt = $conn->prepare("INSERT into databaseexportlog (database_name, path, create_staff) VALUE (?, ?, ?)");
  $stmt->execute($data);

  return $stmt;
}

// fetch export database data
function fetchDatabaseData()
{
  global $conn;

  $data = [];

  $stmt = $conn->prepare("SELECT * FROM databaseexportlog order by id desc");
  $stmt->execute($data);

  return $stmt;
}

// update export database data
function updateExport($id, $downloadStaff)
{
  global $conn;

  $data = [$downloadStaff, $id];

  $stmt = $conn->prepare("UPDATE databaseexportlog set downloaded = 1, downloaded_at = now(), downloaded_staff = ? where id = ?");
  $stmt->execute($data);

  return $stmt;
}

// แปลงตัวเลขเงิน เป็นอักษร
function bahtText($amount_number)
{
  $amount_number = number_format($amount_number, 2, ".", "");
  $pt = strpos($amount_number, ".");
  $number = $fraction = "";
  if ($pt === false)
    $number = $amount_number;
  else {
    $number = substr($amount_number, 0, $pt);
    $fraction = substr($amount_number, $pt + 1);
  }

  $ret = "";
  $baht = ReadNumber($number);
  if ($baht != "")
    $ret .= $baht . "บาท";

  $satang = ReadNumber($fraction);
  if ($satang != "")
    $ret .=  $satang . "สตางค์";
  else
    $ret .= "ถ้วน";
  return $ret;
}

function ReadNumber($number)
{
  $position_call = array("แสน", "หมื่น", "พัน", "ร้อย", "สิบ", "");
  $number_call = array("", "หนึ่ง", "สอง", "สาม", "สี่", "ห้า", "หก", "เจ็ด", "แปด", "เก้า");
  $number = $number + 0;
  $ret = "";
  if ($number == 0) return $ret;
  if ($number > 1000000) {
    $ret .= ReadNumber(intval($number / 1000000)) . "ล้าน";
    $number = intval(fmod($number, 1000000));
  }

  $divider = 100000;
  $pos = 0;
  while ($number > 0) {
    $d = intval($number / $divider);
    $ret .= (($divider == 10) && ($d == 2)) ? "ยี่" : ((($divider == 10) && ($d == 1)) ? "" : ((($divider == 1) && ($d == 1) && ($ret != "")) ? "เอ็ด" : $number_call[$d]));
    $ret .= ($d ? $position_call[$pos] : "");
    $number = $number % $divider;
    $divider = $divider / 10;
    $pos++;
  }
  return $ret;
}

function Export_Database($host, $user, $pass, $name,  $tables = false, $backup_name = false)

//ENTER THE RELEVANT INFO BELOW
// $mysqlUserName      = "Your Username";
// $mysqlPassword      = "Your Password";
// $mysqlHostName      = "Your Host";
// $DbName             = "Your Database Name here";
// $backup_name        = "mybackup.sql";
// $tables             = "Your tables";

{
  $mysqli = new mysqli($host, $user, $pass, $name);
  $mysqli->select_db($name);
  $mysqli->query("SET NAMES 'utf8'");

  $queryTables    = $mysqli->query('SHOW TABLES');
  while ($row = $queryTables->fetch_row()) {
    $target_tables[] = $row[0];
  }
  if ($tables !== false) {
    $target_tables = array_intersect($target_tables, $tables);
  }
  foreach ($target_tables as $table) {
    $result         =   $mysqli->query('SELECT * FROM ' . $table);
    $fields_amount  =   $result->field_count;
    $rows_num = $mysqli->affected_rows;
    $res            =   $mysqli->query('SHOW CREATE TABLE ' . $table);
    $TableMLine     =   $res->fetch_row();
    $content        = (!isset($content) ?  '' : $content) . "\n\n" . $TableMLine[1] . ";\n\n";

    for ($i = 0, $st_counter = 0; $i < $fields_amount; $i++, $st_counter = 0) {
      while ($row = $result->fetch_row()) { //when started (and every after 100 command cycle):
        if ($st_counter % 100 == 0 || $st_counter == 0) {
          $content .= "\nINSERT INTO " . $table . " VALUES";
        }
        $content .= "\n(";
        for ($j = 0; $j < $fields_amount; $j++) {
          $row[$j] = str_replace("\n", "\\n", addslashes($row[$j]));
          if (isset($row[$j])) {
            $content .= '"' . $row[$j] . '"';
          } else {
            $content .= '""';
          }
          if ($j < ($fields_amount - 1)) {
            $content .= ',';
          }
        }
        $content .= ")";
        //every after 100 command cycle [or at last line] ....p.s. but should be inserted 1 cycle eariler
        if ((($st_counter + 1) % 100 == 0 && $st_counter != 0) || $st_counter + 1 == $rows_num) {
          $content .= ";";
        } else {
          $content .= ",";
        }
        $st_counter = $st_counter + 1;
      }
    }
    $content .= "\n\n\n";
  }
  //$backup_name = $backup_name ? $backup_name : $name."___(".date('H-i-s')."_".date('d-m-Y').")__rand".rand(1,11111111).".sql";
  $backup_name = $backup_name ? $backup_name : $name . ".sql";
  header('Content-Type: application/octet-stream');
  header("Content-Transfer-Encoding: Binary");
  header("Content-disposition: attachment; filename=\"" . $backup_name . "\"");
  echo $content;
  exit;
}

function Export_csv($host, $user, $pass, $database, $table = false)
{
  /* vars for export */
  if ($table == false) {
    $table = 'backup';
  }
  // optional where query
  $where = 'WHERE 1 ORDER BY 1';
  // filename for export
  $csv_filename = 'db_export_' . $table . '_' . date('Y-m-d') . '.csv';
  // database variables
  $port = 3306;

  $conn = mysqli_connect($host, $user, $pass, $database, $port);
  if (mysqli_connect_errno()) {
    die("Failed to connect to MySQL: " . mysqli_connect_error());
  }

  // create empty variable to be filled with export data
  $csv_export = '';

  // query to get data from database
  $query = mysqli_query($conn, "SELECT * FROM " . $table . " " . $where);
  $field = mysqli_field_count($conn);

  // create line with field names
  for ($i = 0; $i < $field; $i++) {
    $csv_export .= mysqli_fetch_field_direct($query, $i)->name . ';';
  }

  // newline (seems to work both on Linux & Windows servers)
  $csv_export .= '
      ';

  // loop through database query and fill export variable
  while ($row = mysqli_fetch_array($query)) {
    // create line with field values
    for ($i = 0; $i < $field; $i++) {
      $csv_export .= '"' . $row[mysqli_fetch_field_direct($query, $i)->name] . '";';
    }
    $csv_export .= '
      ';
  }

  // Export the data and prompt a csv file for download
  header('Content-Description: File Transfer');
  header('Content-Type: application/octet-stream');
  header("Content-Disposition: attachment; filename=" . $csv_filename . "");
  header('Content-Transfer-Encoding: binary');
  header('Expires: 0');
  header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
  header('Pragma: public');
  echo "\xEF\xBB\xBF"; // UTF-8 BOM
  echo $csv_export;
}

function csvExport($tablename)
{

  // $hostname   = 'localhost';
  // $username   = 'cl50-whiskey_p';
  // $password   = 'rmBRsm!kK';
  // $myDatabase = 'cl50-whiskey_p';

  try {
    // $conn = new PDO('mysql:host=localhost;dbname='.$myDatabase, $username, $password);
    global $conn;

    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    $q = $conn->prepare("DESCRIBE $tablename");
    $q->execute();
    $table_fields = $q->fetchAll(PDO::FETCH_COLUMN);

    $x = $conn->query("select * from $tablename");




    $filelocation = '../assets/exports/';
    $filename     = 'export-' . date('Y-m-d H.i.s') . '.csv';
    $file_export  =  $filelocation . $filename;

    $data = fopen($file_export, 'w');

    $csv_fields = [];

    for ($i = 0; $i < count($table_fields); $i++) {
      $csv_fields[] = $table_fields[$i];
    }

    fputcsv($data, $csv_fields);

    while ($row = $x->fetch(PDO::FETCH_ASSOC)) {
      fputcsv($data, $row);
    }
  } catch (PDOException $e) {
    echo 'ERROR: ' . $e->getMessage();
  }
}

function fetchRetainGraph($month = 1, $year = 2018)
{

  global $conn;

  if ($month < 4) {
    $month = (int) $month + 9;
    $year = (int) $year - 1;
  } else {
    $month = (int) $month - 3;
  }

  $startDate = "{$year}-{$month}-01";

  $data = [$startDate];

  $stmt = $conn->prepare('SELECT id, cbs, retainDate FROM retaincare where retainDate >= ?');
  $stmt->execute($data);

  return $stmt;
}

function fetchRetainGraph2($month = 1, $year = 2018)
{

  global $conn;

  if ($month < 4) {
    $month = (int) $month + 9;
    $year = (int) $year - 1;
  } else {
    $month = (int) $month - 3;
  }

  $startDate = "{$year}-{$month}-01";

  $data = [$startDate];

  $stmt = $conn->prepare('SELECT 

  id, 
  cbs, 
  service_date as retainDate

  FROM caresupport_activity where service_date >= ?');
  $stmt->execute($data);

  return $stmt;
}

function caseReachForNHSOReach($conn, $eCascadeCheck = false, $period, $year = '2021', $filter = '', $startDate = NULL, $endDate = NULL)
{

  if ($period == '') {
    $start = checkQuarter()['q1_start'];
    $end = date('Y-m-d');
  }

  if ($period == 'month') {
    $start = checkQuarter()['this_month_start'];
    $end = checkQuarter()['this_month_end'];
  }

  if ($period == 'quarter') {
    $start = checkQuarter()['this_quarter_start'];
    $end = checkQuarter()['this_quarter_end'];
  }

  if ($period == 'fy23') {
    $start = checkQuarter("2023-01-01")['q1_start'];
    if ($filter) $start = $filter;
    $end = checkQuarter("2023-01-01")['q4_end'];
  }

  if ($period == 'fy22') {
    $start = checkQuarter("2022-01-01")['q1_start'];
    $end = checkQuarter("2022-01-01")['q4_end'];
  }

  if ($period == 'fy21') {
    $start = checkQuarter("2021-01-01")['q1_start'];
    $end = checkQuarter("2021-01-01")['q4_end'];
  }

  if ($period == 'all') {
    $start = '2020-12-01';
    $end = date('Y-m-d');
  }

  if ($startDate && $endDate) {
    $start = $startDate;
    $end = $endDate;
  }

  $data = [$year, $start, $end];

  $stmt = $conn->prepare("SELECT
    a.id,
    a.cbs,
    a.location,
    a.reachdate,
    a.uic,
    a.sex,
    a.firstname,
    a.lastname,
    a.phone,
    a.occupation,
    a.condom49,
    a.condom52,
    a.condom53,
    a.condom54,
    a.condom56,
    a.lubricant,
    a.peopleid,
    NULL AS sub_kp,
    b.nap_code,
    b.nap_status,
    b.nap_staff,
    b.nap_date,
    b.nap_comment,
    c.service_date as tested_date
FROM
  reach a
  LEFT JOIN (
    SELECT
      MAX(sn) as sn,
      refer_number,
      uic
    FROM clinic
    GROUP BY refer_number, uic
  ) max_clinic ON max_clinic.refer_number = a.id AND max_clinic.uic = a.uic
  LEFT JOIN clinic c ON c.sn = max_clinic.sn
LEFT JOIN (
    SELECT
        MAX(id) AS max_id,
        source_id
    FROM nhso_record_nap
    WHERE source = 'reach'
        AND type = 'Reach'
        AND year = ?
    GROUP BY source_id
) AS max_nap
ON max_nap.source_id = a.id
LEFT JOIN nhso_record_nap b
ON max_nap.max_id = b.id
WHERE 
  a.created_at >= ? AND a.created_at < ?");

  $stmt->execute($data);

  $dataFetchs = $stmt->fetchall();

  $response = [];

  foreach ($dataFetchs as $key => $item) {
    if (checkPID($item->peopleid)) {
      $response[$key]['id'] = $item->id;
      $response[$key]['type'] = 'Reach';
      $response[$key]['source'] = 'reach';
      $response[$key]['isreach'] = $item->cbs;
      $response[$key]['location'] = $item->location;
      $response[$key]['service_date'] = $item->reachdate;
      $response[$key]['uic'] = $item->uic;
      $response[$key]['kp'] = $item->sex;
      $response[$key]['sub_kp'] = $item->sub_kp;
      $response[$key]['prep_code'] = '';
      $response[$key]['birth_day'] = (substr($item->uic, -6, 2)) . '/' . (substr($item->uic, -4, 2)) . '/' . (substr($item->uic, -2) > 70 ? substr($item->uic, -2) + 2400 : substr($item->uic, -2) + 2500);
      $response[$key]['firstname'] = $item->firstname;
      $response[$key]['lastname'] = $item->lastname;
      $response[$key]['phone'] = $item->phone;
      $response[$key]['id_card'] = $item->peopleid;
      $response[$key]['occupation'] = $item->occupation;
      $response[$key]['nap_code'] = $item->nap_code;
      $response[$key]['nap_status'] = $item->nap_status;
      $response[$key]['nap_staff'] = $item->nap_staff;
      $response[$key]['nap_date'] = $item->nap_date;
      $response[$key]['nap_comment'] = $item->nap_comment;
      $response[$key]['tested_date'] = $item->tested_date;
      $response[$key]['condom49'] = $item->condom49;
      $response[$key]['condom52'] = $item->condom52;
      $response[$key]['condom53'] = $item->condom53;
      $response[$key]['condom54'] = $item->condom54;
      $response[$key]['condom56'] = $item->condom56;
      $response[$key]['lubricant'] = $item->lubricant;
    }
  }

  return $response;
}

function caseRetainForNHSOReach($conn, $eCascadeCheck = false, $period, $year = '2021', $filter = '', $startDate = NULL, $endDate = NULL)
{

  if ($period == '') {
    $start = checkQuarter()['q1_start'];
    $end = date('Y-m-d');
  }

  if ($period == 'month') {
    $start = checkQuarter()['this_month_start'];
    $end = checkQuarter()['this_month_end'];
  }

  if ($period == 'quarter') {
    $start = checkQuarter()['this_quarter_start'];
    $end = checkQuarter()['this_quarter_end'];
  }

  if ($period == 'fy23') {
    $start = checkQuarter("2023-01-01")['q1_start'];
    $end = checkQuarter("2023-01-01")['q4_end'];
  }

  if ($period == 'fy22') {
    $start = checkQuarter("2022-01-01")['q1_start'];
    $end = checkQuarter("2022-01-01")['q4_end'];
  }

  if ($period == 'fy21') {
    $start = checkQuarter("2021-01-01")['q1_start'];
    $end = checkQuarter("2021-01-01")['q4_end'];
  }

  if ($period == 'all') {
    $start = '2020-12-01';
    $end = date('Y-m-d');
  }

  if ($startDate && $endDate) {
    $start = $startDate;
    $end = $endDate;
  }

  $data = [$year, $start, $end];

  $stmt = $conn->prepare("SELECT 
  a.*, 
  a.cbs as retain_cbs, 
  b.*, 
  c.nap_code, 
  c.nap_status, 
  c.nap_staff, 
  c.nap_date, 
  c.nap_comment,
  d.sub_kp,
  e.service_date as tested_date
FROM 
  retainnegative a 
  LEFT JOIN clinic b ON a.sn = b.sn 
  LEFT JOIN (
    SELECT 
      MAX(sn) as sn, 
      uic 
    FROM clinic 
    GROUP BY uic
  ) max_clinic ON max_clinic.uic = a.uic AND max_clinic.sn > a.sn
  LEFT JOIN clinic e ON e.sn = max_clinic.sn
  LEFT JOIN nhso_record_nap c ON c.type = 'Reach' AND c.year = ? AND b.id_card = c.id_card 
  LEFT JOIN clinicprecounseling d ON b.pre_id = d.id
WHERE 
  a.retainDetail != '' AND 
  a.created_at >= ? AND 
  a.created_at < ?
");

  $stmt->execute($data);

  $dataFetchs = $stmt->fetchall();

  $response = [];

  foreach ($dataFetchs as $key => $item) {
    if (checkPID($item->id_card)) {
      $response[$key]['id'] = $item->sn;
      $response[$key]['type'] = 'Reach';
      $response[$key]['source'] = 'retain';
      $response[$key]['isreach'] = $item->retain_cbs;
      $response[$key]['location'] = 'DIC';
      $response[$key]['service_date'] = $item->retainDate;
      $response[$key]['uic'] = $item->uic;
      $response[$key]['kp'] = $item->sex;
      $response[$key]['sub_kp'] = $item->sub_kp;
      $response[$key]['prep_code'] = '';
      $response[$key]['birth_day'] = (substr($item->uic, -6, 2)) . '/' . (substr($item->uic, -4, 2)) . '/' . (substr($item->uic, -2) > 70 ? substr($item->uic, -2) + 2400 : substr($item->uic, -2) + 2500);
      $response[$key]['firstname'] = $item->firstname;
      $response[$key]['lastname'] = $item->lastname;
      $response[$key]['phone'] = $item->phone;
      $response[$key]['id_card'] = $item->id_card;
      $response[$key]['occupation'] = $item->occupation;
      $response[$key]['nap_code'] = $item->nap_code;
      $response[$key]['nap_staff'] = $item->nap_staff;
      $response[$key]['nap_status'] = $item->nap_status;
      $response[$key]['nap_date'] = $item->nap_date;
      $response[$key]['nap_comment'] = $item->nap_comment;
      $response[$key]['tested_date'] = $item->tested_date;
    }
  }

  return $response;
}

function caseClinicForNHSOReach($conn, $eCascadeCheck = false, $period, $year = '2021', $filter = '', $startDate = NULL, $endDate = NULL)
{

  if ($period == '') {
    $start = checkQuarter()['q1_start'];
    $end = date('Y-m-d');
  }

  if ($period == 'month') {
    $start = checkQuarter()['this_month_start'];
    $end = checkQuarter()['this_month_end'];
  }

  if ($period == 'quarter') {
    $start = checkQuarter()['this_quarter_start'];
    $end = checkQuarter()['this_quarter_end'];
  }

  if ($period == 'fy23') {
    $start = checkQuarter("2023-01-01")['q1_start'];
    $end = checkQuarter("2023-01-01")['q4_end'];
  }

  if ($period == 'fy22') {
    $start = checkQuarter("2022-01-01")['q1_start'];
    $end = checkQuarter("2022-01-01")['q4_end'];
  }

  if ($period == 'fy21') {
    $start = checkQuarter("2021-01-01")['q1_start'];
    $end = checkQuarter("2021-01-01")['q4_end'];
  }

  if ($period == 'all') {
    $start = '2020-12-01';
    $end = date('Y-m-d');
  }

  if ($startDate && $endDate) {
    $start = $startDate;
    $end = $endDate;
  }

  $data = [$year, $start, $end];

  $stmt = $conn->prepare("SELECT 
  a.*, 
  b.nap_code, 
  b.nap_status, 
  b.nap_staff, 
  b.nap_date, 
  b.nap_comment,
  c.sub_kp,
  d.condomDistribute49 as condom49,
  d.condomDistribute52 as condom52,
  d.condomDistribute53 as condom53,
  d.condomDistribute54 as condom54,
  d.condomDistribute56 as condom56,
  d.lubricantDistribute as lubricant,
  a.service_date as tested_date
FROM 
  clinic a 
  LEFT JOIN nhso_record_nap b ON b.type = 'Reach' AND b.year = ? AND a.id_card = b.id_card 
  LEFT JOIN clinicprecounseling c ON a.pre_id = c.id 
  LEFT JOIN clinicpostcounseling d ON a.post_id = d.id
WHERE 
  a.clinic_id > 0 AND 
  a.service_date >= ? AND 
  a.service_date < ?
");

  $stmt->execute($data);

  $dataFetchs = $stmt->fetchall();

  $response = [];

  foreach ($dataFetchs as $key => $item) {
    if (checkPID($item->id_card)) {
      $response[$key]['id'] = $item->sn;
      $response[$key]['type'] = 'Reach';
      $response[$key]['source'] = 'clinic';
      $response[$key]['isreach'] = $item->cbs;
      $response[$key]['location'] = $item->mobile;
      $response[$key]['service_date'] = $item->service_date;
      $response[$key]['uic'] = $item->uic;
      $response[$key]['kp'] = $item->sex;
      $response[$key]['sub_kp'] = $item->sub_kp;
      $response[$key]['prep_code'] = $item->prep;
      $response[$key]['birth_day'] = (substr($item->uic, -6, 2)) . '/' . (substr($item->uic, -4, 2)) . '/' . ((int)substr($item->uic, -2) > 70 ? (int)substr($item->uic, -2) + 2400 : (int)substr($item->uic, -2) + 2500);
      $response[$key]['firstname'] = $item->firstname;
      $response[$key]['lastname'] = $item->lastname;
      $response[$key]['phone'] = $item->phone;
      $response[$key]['id_card'] = $item->id_card;
      $response[$key]['occupation'] = $item->occupation;
      $response[$key]['nap_code'] = $item->nap_code;
      $response[$key]['nap_status'] = $item->nap_status;
      $response[$key]['nap_staff'] = $item->nap_staff;
      $response[$key]['nap_date'] = $item->nap_date;
      $response[$key]['nap_comment'] = $item->nap_comment;
      $response[$key]['tested_date'] = $item->tested_date;
      $response[$key]['condom49'] = $item->condom49;
      $response[$key]['condom52'] = $item->condom52;
      $response[$key]['condom53'] = $item->condom53;
      $response[$key]['condom54'] = $item->condom54;
      $response[$key]['condom56'] = $item->condom56;
      $response[$key]['lubricant'] = $item->lubricant;
    }
  }

  return $response;
}

function fetchNHSOForReach($conn, $eCascadeCheck = false, $period, $year = '2021', $filter = '', $startDate = NULL, $endDate = NULL)
{

  $start_query = checkQuarter("{$year}-01-01")['q1_start'];
  $end_query = checkQuarter("{$year}-01-01")['q4_end'];

  $reachDatas = caseReachForNHSOReach($conn, $eCascadeCheck, $period, $year, $filter, $start_query, $end_query);
  $retainDatas = caseRetainForNHSOReach($conn, $eCascadeCheck, $period, $year, $filter, $start_query, $end_query);
  $clinicWalkinDatas = caseClinicForNHSOReach($conn, $eCascadeCheck, $period, $year, $filter, $start_query, $end_query);

  $data = array_merge($reachDatas, $retainDatas, $clinicWalkinDatas);
  // $data = array_merge($clinicWalkinDatas);

  $arr_sort = array_column($data, 'service_date');

  array_multisort($arr_sort, SORT_ASC, $data);

  $uicLists = [];
  $response = [];

  $data_filter = array_filter($data, function ($item) {

    $kpList = ['MSM', 'MSW', 'TG', 'TGSW', 'FSW', 'PWID-Male', 'PWID-Female', 'TGM', 'Female', 'Male'];

    return in_array($item['kp'], $kpList) && !in_array(substr($item['id_card'], 0, 1), [0, 6, 7]);
  });

  foreach ($data_filter as $item) {

    if (!in_array($item['uic'], $uicLists)) {
      $uicLists[] = $item['uic'];
      $response[] = $item;
    }
  }
  return $response;
}

function caseClinicForNap($conn, $eCascadeCheck = false, $period, $year = '2021', $filter = '', $startDate = NULL, $endDate = NULL, $source = 'testing')
{

  if ($period == '') {
    $start = checkQuarter()['q1_start'];
    $end = date('Y-m-d');
  }

  if ($period == 'month') {
    $start = checkQuarter()['this_month_start'];
    $end = checkQuarter()['this_month_end'];
  }

  if ($period == 'quarter') {
    $start = checkQuarter()['this_quarter_start'];
    $end = checkQuarter()['this_quarter_end'];
  }

  if ($period == 'fy26') {
    $start = checkQuarter("2026-01-01")['q1_start'];
    if ($filter) $start = $filter;
    $end = checkQuarter("2026-01-01")['q4_end'];
  }

  if ($period == 'fy25') {
    $start = checkQuarter("2025-01-01")['q1_start'];
    if ($filter) $start = $filter;
    $end = checkQuarter("2025-01-01")['q4_end'];
  }

  if ($period == 'fy24') {
    $start = checkQuarter("2024-01-01")['q1_start'];
    if ($filter) $start = $filter;
    $end = checkQuarter("2024-01-01")['q4_end'];
  }

  if ($period == 'fy23') {
    $start = checkQuarter("2023-01-01")['q1_start'];
    if ($filter) $start = $filter;
    $end = checkQuarter("2023-01-01")['q4_end'];
  }

  if ($period == 'fy22') {
    $filter = checkQuarter("2022-01-01")['q1_start'];
    $start = checkQuarter("2022-01-01")['q1_start'];
    $end = checkQuarter("2022-01-01")['q4_end'];
  }

  if ($period == 'fy21') {
    $filter = checkQuarter("2021-01-01")['q1_start'];
    $start = checkQuarter("2021-01-01")['q1_start'];
    $end = checkQuarter("2021-01-01")['q4_end'];
  }

  if ($period == 'all') {
    $filter = '2020-12-01';
    $start = '2020-12-01';
    $end = date('Y-m-d');
  }

  if ($startDate && $endDate) {
    $start = $startDate;
    $end = $endDate;
  }

  $data = [$source, $year, $start, $end];

  // Build different WHERE conditions based on source
  $testCondition = "a.hiv_result != '-'"; // Default for HIV/testing
  if ($source === 'syphilis') {
    $testCondition = "a.sti_result != '-'"; // For Syphilis
  } elseif ($source === 'hcv') {
    $testCondition = "a.hcv_result != '-'"; // For HCV
  }

  $stmt = $conn->prepare("SELECT
  a.*,
  c.sub_kp,
  d.healthcare,
  d.hospital,
  b.id as nhso_id,
  b.nap_code,
  b.vct_nap_status,
  b.nap_status,
  b.nap_staff,
  b.nap_date,
  b.nap_comment,
  b.nap_api_status,
  b.nap_api_date,
  b.nap_api_result,
  b.nap_api_staff,
  late_lab.lab_result_ct,
  late_lab.lab_result_ng

  FROM clinic a
  LEFT JOIN clinicnewcase d ON a.newcase_id = d.id
  LEFT JOIN late_lab_result late_lab ON a.latelab_id = late_lab.id
  LEFT JOIN clinicprecounseling c ON a.pre_id = c.id
  LEFT JOIN nhso_record_nap b on b.type = 'Clinic' AND b.source = ? AND b.year = ? AND a.id_card = b.id_card AND a.sn = b.source_id

  WHERE SUBSTRING(a.id_card, 1, 1) not in ('0', '6', '7')
  AND a.sex in ('MSM', 'MSW', 'TG', 'TGSW', 'TGM', 'PWID-Male', 'PWID-Female', 'FSW', 'Male', 'Female')
  AND $testCondition
  AND a.service_date >= ?
  AND a.service_date < ?
  AND a.clinic_id > 0");

  $stmt->execute($data);

  $dataFetchs = $stmt->fetchall();

  $filterDate = [];

  foreach ($dataFetchs as $key => $item) {

    if ($item->service_date >= $filter) {
      $filterDate[] = $item;
    }
  }

  $uicCounts = [];
  $firstTestedDate = [];
  $response = [];

  foreach ($filterDate as $item) {
    if (checkPID($item->id_card)) {
      // Count UIC occurrences
      if (!isset($uicCounts[$item->uic])) {
        $uicCounts[$item->uic] = 0;
      }
      $uicCounts[$item->uic]++;

      // Track the first tested date
      if (!isset($firstTestedDate[$item->uic])) {
        $firstTestedDate[$item->uic] = Carbon::parse($item->service_date);
      }
    }
  }

  foreach ($filterDate as $key => $item) {
    if (checkPID($item->id_card)) {

      $date1 = $firstTestedDate[$item->uic];
      $date2 = Carbon::parse($item->service_date);
      $formLastTest = $date1->diffInDays($date2);

      $birth_year = (int)substr($item->uic, -2);
      $birth_day = (substr($item->uic, -6, 2)) . '/' . (substr($item->uic, -4, 2)) . '/' . (($birth_year > 70 ? $birth_year + 2400 : $birth_year + 2500));

      $response[$key]['id'] = $item->sn;
      $response[$key]['type'] = 'Clinic';
      $response[$key]['source'] = $source;
      $response[$key]['isreach'] = $item->cbs;
      $response[$key]['location'] = $item->mobile;
      $response[$key]['clinic_type'] = $item->type;
      $response[$key]['service_date'] = $item->service_date;
      $response[$key]['uic'] = $item->uic;
      $response[$key]['uic_count'] = $uicCounts[$item->uic];
      $response[$key]['kp'] = $item->sex;
      $response[$key]['sub_kp'] = $item->sub_kp;
      $response[$key]['formLastTest'] = $formLastTest;
      $response[$key]['birth_day'] = $birth_day;
      $response[$key]['firstname'] = $item->firstname;
      $response[$key]['lastname'] = $item->lastname;
      $response[$key]['phone'] = $item->phone;
      $response[$key]['id_card'] = $item->id_card;
      $response[$key]['occupation'] = $item->occupation;
      $response[$key]['healthcare'] = $item->healthcare;
      $response[$key]['hospital'] = $item->hospital;
      $response[$key]['cbs'] = $item->cbs;
      $response[$key]['hiv_result'] = $item->hiv_result;
      $response[$key]['sti_result'] = $item->sti_result;
      $response[$key]['hcv_result'] = $item->hcv_result;
      $response[$key]['ct_result'] = $item->lab_result_ct ?? $item->ct_result;
      $response[$key]['ng_result'] = $item->lab_result_ng ?? $item->ng_result;
      $response[$key]['counselor'] = $item->counselor;
      $response[$key]['mobile'] = $item->mobile;
      $response[$key]['nap_code'] = $item->nap_code;
      $response[$key]['vct_nap_status'] = $item->vct_nap_status;
      $response[$key]['nap_status'] = $item->nap_status;
      $response[$key]['nap_staff'] = $item->nap_staff;
      $response[$key]['nap_date'] = $item->nap_date;
      $response[$key]['nap_comment'] = $item->nap_comment;
      $response[$key]['nap_api_status'] = $item->nap_api_status;
      $response[$key]['nap_api_date'] = $item->nap_api_date;
      $response[$key]['nap_api_result'] = $item->nap_api_result;
      $response[$key]['nap_api_staff'] = $item->nap_api_staff;
      $response[$key]['nhso_id'] = $item->nhso_id;
    }
  }

  return $response;
}

function caseHCVForNap($conn, $eCascadeCheck = false, $period, $year = '2021', $filter = '', $startDate = NULL, $endDate = NULL)
{
  if ($period == '') {
    $start = checkQuarter()['q1_start'];
    $end = date('Y-m-d');
  }

  if ($period == 'month') {
    $start = checkQuarter()['this_month_start'];
    $end = checkQuarter()['this_month_end'];
  }

  if ($period == 'quarter') {
    $start = checkQuarter()['this_quarter_start'];
    $end = checkQuarter()['this_quarter_end'];
  }

  if ($period == 'fy26') {
    $start = checkQuarter("2026-01-01")['q1_start'];
    if ($filter) $start = $filter;
    $end = checkQuarter("2026-01-01")['q4_end'];
  }

  if ($period == 'fy25') {
    $start = checkQuarter("2025-01-01")['q1_start'];
    if ($filter) $start = $filter;
    $end = checkQuarter("2025-01-01")['q4_end'];
  }

  if ($period == 'fy24') {
    $start = checkQuarter("2024-01-01")['q1_start'];
    if ($filter) $start = $filter;
    $end = checkQuarter("2024-01-01")['q4_end'];
  }

  if ($period == 'fy23') {
    $start = checkQuarter("2023-01-01")['q1_start'];
    if ($filter) $start = $filter;
    $end = checkQuarter("2023-01-01")['q4_end'];
  }

  if ($period == 'fy22') {
    $filter = checkQuarter("2022-01-01")['q1_start'];
    $start = checkQuarter("2022-01-01")['q1_start'];
    $end = checkQuarter("2022-01-01")['q4_end'];
  }

  if ($period == 'fy21') {
    $filter = checkQuarter("2021-01-01")['q1_start'];
    $start = checkQuarter("2021-01-01")['q1_start'];
    $end = checkQuarter("2021-01-01")['q4_end'];
  }

  if ($period == 'all') {
    $filter = '2020-12-01';
    $start = '2020-12-01';
    $end = date('Y-m-d');
  }

  if ($startDate && $endDate) {
    $start = $startDate;
    $end = $endDate;
  }

  $data = [$year, $start, $end];

  $stmt = $conn->prepare("SELECT
  a.*,
  c.sub_kp,
  d.healthcare,
  d.hospital,
  b.nap_code,
  b.nap_status,
  b.nap_staff,
  b.nap_date,
  b.nap_comment,
  late_lab.lab_result_ct,
  late_lab.lab_result_ng

  FROM clinic a
  LEFT JOIN clinicnewcase d ON a.newcase_id = d.id
  LEFT JOIN late_lab_result late_lab ON a.latelab_id = late_lab.id
  LEFT JOIN clinicprecounseling c ON a.pre_id = c.id
  LEFT JOIN nhso_record_nap b on b.source = 'hcv' AND b.year = ? AND a.id_card = b.id_card AND a.sn = b.source_id

  WHERE SUBSTRING(a.id_card, 1, 1) not in ('0', '6', '7')
  AND a.sex in ('MSM', 'MSW', 'TG', 'TGSW', 'TGM', 'PWID-Male', 'PWID-Female', 'FSW', 'Male', 'Female')
  AND a.hcv_result != '-'
  AND a.service_date >= ?
  AND a.service_date < ?
  AND a.clinic_id > 0
  ORDER BY a.service_date ASC");

  $stmt->execute($data);

  $dataFetchs = $stmt->fetchall();

  $response = [];

  $uicLists = [];

  $firstTestedDate = [];

  $filterDate = [];

  foreach ($dataFetchs as $key => $item) {

    if ($item->service_date >= $filter) {
      $filterDate[] = $item;
    }
  }

  // $dataFetchs = $filterDate;

  foreach ($filterDate as $key => $item) {
    if (checkPID($item->id_card)) {

      // Only process if this UIC hasn't been seen before (first record only)
      if (!in_array($item->uic, $uicLists)) {
        $uicLists[] = $item->uic;

        if (!array_key_exists($item->uic, $firstTestedDate)) {

          $firstTestedDate[$item->uic] = Carbon::parse($item->service_date);
        }

        $date1 = $firstTestedDate[$item->uic];
        $date2 = Carbon::parse($item->service_date);

        $formLastTest = $date1->diffInDays($date2);

        $response[$key]['id'] = $item->sn;
        $response[$key]['type'] = 'Clinic';
        $response[$key]['source'] = 'hcv';
        $response[$key]['isreach'] = $item->cbs;
        $response[$key]['location'] = $item->mobile;
        $response[$key]['clinic_type'] = $item->type;
        $response[$key]['service_date'] = $item->service_date;
        $response[$key]['uic'] = $item->uic;
        $response[$key]['uic_count'] = array_count_values($uicLists)[$item->uic];
        $response[$key]['kp'] = $item->sex;
        $response[$key]['sub_kp'] = $item->sub_kp;
        $response[$key]['formLastTest'] = $formLastTest;
        $response[$key]['birth_day'] = (substr($item->uic, -6, 2)) . '/' . (substr($item->uic, -4, 2)) . '/' . ((int)substr($item->uic, -2) > 70 ? (int)substr($item->uic, -2) + 2400 : (int)substr($item->uic, -2) + 2500);
        $response[$key]['firstname'] = $item->firstname;
        $response[$key]['lastname'] = $item->lastname;
        $response[$key]['phone'] = $item->phone;
        $response[$key]['id_card'] = $item->id_card;
        $response[$key]['occupation'] = $item->occupation;
        $response[$key]['healthcare'] = $item->healthcare;
        $response[$key]['hospital'] = $item->hospital;
        $response[$key]['cbs'] = $item->cbs;
        $response[$key]['hiv_result'] = $item->hiv_result;
        $response[$key]['sti_result'] = $item->sti_result;
        $response[$key]['hcv_result'] = $item->hcv_result;
        $response[$key]['ct_result'] = $item->lab_result_ct ?? $item->ct_result;
        $response[$key]['ng_result'] = $item->lab_result_ng ?? $item->ng_result;
        $response[$key]['counselor'] = $item->counselor;
        $response[$key]['mobile'] = $item->mobile;
        $response[$key]['nap_code'] = $item->nap_code;
        $response[$key]['nap_status'] = $item->nap_status;
        $response[$key]['nap_staff'] = $item->nap_staff;
        $response[$key]['nap_date'] = $item->nap_date;
        $response[$key]['nap_comment'] = $item->nap_comment;
      }
    }
  }

  if ($eCascadeCheck) {

    $eCascadeCheck_response = [];

    foreach ($response as $key => $item) {
      if ($item['nap_status'] == 'false' || $item['nap_status'] == null) {
        $eCascadeCheck_response[] = $item;
      }
    }

    return $eCascadeCheck_response;
  }

  return $response;
}

function caseCD4ForNap($conn, $eCascadeCheck = false, $period, $year = '2021', $filter = '', $startDate = NULL, $endDate = NULL)
{
  if ($period == '') {
    $start = checkQuarter()['q1_start'];
    $end = date('Y-m-d');
  }

  if ($period == 'month') {
    $start = checkQuarter()['this_month_start'];
    $end = checkQuarter()['this_month_end'];
  }

  if ($period == 'quarter') {
    $start = checkQuarter()['this_quarter_start'];
    $end = checkQuarter()['this_quarter_end'];
  }

  if ($period == 'fy26') {
    $start = checkQuarter("2026-01-01")['q1_start'];
    if ($filter) $start = $filter;
    $end = checkQuarter("2026-01-01")['q4_end'];
  }

  if ($period == 'fy25') {
    $start = checkQuarter("2025-01-01")['q1_start'];
    if ($filter) $start = $filter;
    $end = checkQuarter("2025-01-01")['q4_end'];
  }

  if ($period == 'fy24') {
    $start = checkQuarter("2024-01-01")['q1_start'];
    if ($filter) $start = $filter;
    $end = checkQuarter("2024-01-01")['q4_end'];
  }

  if ($period == 'fy23') {
    $start = checkQuarter("2023-01-01")['q1_start'];
    if ($filter) $start = $filter;
    $end = checkQuarter("2023-01-01")['q4_end'];
  }

  if ($period == 'fy22') {
    $filter = checkQuarter("2022-01-01")['q1_start'];
    $start = checkQuarter("2022-01-01")['q1_start'];
    $end = checkQuarter("2022-01-01")['q4_end'];
  }

  if ($period == 'fy21') {
    $filter = checkQuarter("2021-01-01")['q1_start'];
    $start = checkQuarter("2021-01-01")['q1_start'];
    $end = checkQuarter("2021-01-01")['q4_end'];
  }

  if ($period == 'all') {
    $filter = '2020-12-01';
    $start = '2020-12-01';
    $end = date('Y-m-d');
  }

  if ($startDate && $endDate) {
    $start = $startDate;
    $end = $endDate;
  }

  $data = [$year, $start, $end];

  $stmt = $conn->prepare("SELECT 
  a.*, 
  c.sub_kp, 
  d.healthcare, 
  d.hospital, 
  lab.cd4_result, 
  lab.cd4_value, 
  lab.cd4_percent, 
  lab.vl_result, 
  lab.vl_result_value, 
  x.nap_code, 
  x.nap_status, 
  x.nap_staff, 
  x.nap_date, 
  x.nap_comment,
  b.nap_code as hiv_nap_code, 
  b.nap_comment as hiv_nap_comment,
  dctng.lab_result_ng as ng_result_late,
  dctng.lab_result_ct as ct_result_late,
  dvl.lab_result_vl_value as vl_result_late,
  dcd4.lab_result_cd4_value as cd4_result_late,
  dcd4.lab_result_cd4_percent as cd4_percent_late

  FROM clinic a
  LEFT JOIN clinicnewcase d ON a.newcase_id = d.id
  LEFT JOIN cliniclabresult lab ON a.lab_id = lab.id
  LEFT JOIN late_lab_result dctng ON dctng.id = (SELECT max(id) FROM late_lab_result WHERE lab_topic in ('CT', 'NG', 'NG/CT') AND clinic_id = a.clinic_id AND uic = a.uic AND queue = a.q)
  LEFT JOIN late_lab_result dvl ON dvl.id = (SELECT max(id) FROM late_lab_result WHERE lab_topic = 'HIV-VL' AND clinic_id = a.clinic_id AND uic = a.uic AND queue = a.q)
  LEFT JOIN late_lab_result dcd4 ON dcd4.id = (SELECT max(id) FROM late_lab_result WHERE lab_topic = 'CD4' AND clinic_id = a.clinic_id AND uic = a.uic AND queue = a.q)
  LEFT JOIN clinicprecounseling c ON a.pre_id = c.id
  LEFT JOIN nhso_record_nap b on b.source = 'testing' AND b.year = ? AND a.id_card = b.id_card AND a.sn = b.source_id 
  LEFT JOIN nhso_record_nap x on x.source = 'cd4' AND b.source_id = x.source_id
  WHERE SUBSTRING(a.id_card, 1, 1) not in ('0', '6', '7') 
  AND lab.cd4_result IS NOT NULL
  AND a.service_date >= ? 
  AND a.service_date < ? 
  AND a.clinic_id > 0");

  $stmt->execute($data);

  $dataFetchs = $stmt->fetchall();

  $response = [];

  $uicLists = [];

  $firstTestedDate = [];

  $filterDate = [];

  foreach ($dataFetchs as $key => $item) {

    if ($item->service_date >= $filter) {
      $filterDate[] = $item;
    }
  }

  // $dataFetchs = $filterDate;

  foreach ($filterDate as $key => $item) {
    if (checkPID($item->id_card)) {

      $uicLists[] = $item->uic;

      if (!array_key_exists($item->uic, $firstTestedDate)) {

        $firstTestedDate[$item->uic] = Carbon::parse($item->service_date);
      }

      $date1 = $firstTestedDate[$item->uic];
      $date2 = Carbon::parse($item->service_date);

      $formLastTest = $date1->diffInDays($date2);

      $response[$key]['id'] = $item->sn;
      $response[$key]['type'] = 'Clinic';
      $response[$key]['source'] = 'cd4';
      $response[$key]['isreach'] = $item->cbs;
      $response[$key]['location'] = $item->mobile;
      $response[$key]['clinic_type'] = $item->type;
      $response[$key]['service_date'] = $item->service_date;
      $response[$key]['uic'] = $item->uic;
      $response[$key]['uic_count'] = array_count_values($uicLists)[$item->uic];
      $response[$key]['kp'] = $item->sex;
      $response[$key]['sub_kp'] = $item->sub_kp;
      $response[$key]['formLastTest'] = $formLastTest;
      $response[$key]['birth_day'] = (substr($item->uic, -6, 2)) . '/' . (substr($item->uic, -4, 2)) . '/' . ((int)substr($item->uic, -2) > 70 ? (int)substr($item->uic, -2) + 2400 : (int)substr($item->uic, -2) + 2500);
      $response[$key]['firstname'] = $item->firstname;
      $response[$key]['lastname'] = $item->lastname;
      $response[$key]['phone'] = $item->phone;
      $response[$key]['id_card'] = $item->id_card;
      $response[$key]['occupation'] = $item->occupation;
      $response[$key]['healthcare'] = $item->healthcare;
      $response[$key]['hospital'] = $item->hospital;
      $response[$key]['cbs'] = $item->cbs;
      $response[$key]['hiv_result'] = $item->hiv_result;
      $response[$key]['sti_result'] = $item->sti_result;
      $response[$key]['hcv_result'] = $item->hcv_result;

      $response[$key]['ct_result'] = $item->ct_result_late ?? $item->ct_result;
      $response[$key]['ng_result'] = $item->ng_result_late ?? $item->ng_result;
      $response[$key]['cd4_value'] = $item->cd4_result_late ?? $item->cd4_value;
      $response[$key]['cd4_percent'] = $item->cd4_percent_late ?? $item->cd4_percent;
      $response[$key]['vl_value'] = $item->vl_result_late ?? $item->vl_result_value;

      $response[$key]['counselor'] = $item->counselor;
      $response[$key]['mobile'] = $item->mobile;
      $response[$key]['nap_code'] = $item->nap_code;
      $response[$key]['nap_status'] = $item->nap_status;
      $response[$key]['nap_staff'] = $item->nap_staff;
      $response[$key]['nap_date'] = $item->nap_date;
      $response[$key]['nap_comment'] = $item->nap_comment;
      $response[$key]['hiv_nap_code'] = $item->hiv_nap_code;
      $response[$key]['hiv_nap_comment'] = $item->hiv_nap_comment;
    }
  }

  if ($eCascadeCheck) {

    $eCascadeCheck_response = [];

    foreach ($response as $key => $item) {
      if ($item['nap_status'] == 'false' || $item['nap_status'] == null) {
        $eCascadeCheck_response[] = $item;
      }
    }

    return $eCascadeCheck_response;
  }

  return $response;
}

function caseVLForNap($conn, $eCascadeCheck = false, $period, $year = '2021', $filter = '', $startDate = NULL, $endDate = NULL)
{
  if ($period == '') {
    $start = checkQuarter()['q1_start'];
    $end = date('Y-m-d');
  }

  if ($period == 'month') {
    $start = checkQuarter()['this_month_start'];
    $end = checkQuarter()['this_month_end'];
  }

  if ($period == 'quarter') {
    $start = checkQuarter()['this_quarter_start'];
    $end = checkQuarter()['this_quarter_end'];
  }

  if ($period == 'fy26') {
    $start = checkQuarter("2026-01-01")['q1_start'];
    if ($filter) $start = $filter;
    $end = checkQuarter("2026-01-01")['q4_end'];
  }

  if ($period == 'fy25') {
    $start = checkQuarter("2025-01-01")['q1_start'];
    if ($filter) $start = $filter;
    $end = checkQuarter("2025-01-01")['q4_end'];
  }

  if ($period == 'fy24') {
    $start = checkQuarter("2024-01-01")['q1_start'];
    if ($filter) $start = $filter;
    $end = checkQuarter("2024-01-01")['q4_end'];
  }

  if ($period == 'fy23') {
    $start = checkQuarter("2023-01-01")['q1_start'];
    if ($filter) $start = $filter;
    $end = checkQuarter("2023-01-01")['q4_end'];
  }

  if ($period == 'fy22') {
    $filter = checkQuarter("2022-01-01")['q1_start'];
    $start = checkQuarter("2022-01-01")['q1_start'];
    $end = checkQuarter("2022-01-01")['q4_end'];
  }

  if ($period == 'fy21') {
    $filter = checkQuarter("2021-01-01")['q1_start'];
    $start = checkQuarter("2021-01-01")['q1_start'];
    $end = checkQuarter("2021-01-01")['q4_end'];
  }

  if ($period == 'all') {
    $filter = '2020-12-01';
    $start = '2020-12-01';
    $end = date('Y-m-d');
  }

  if ($startDate && $endDate) {
    $start = $startDate;
    $end = $endDate;
  }

  $data = [$year, $start, $end];

  $stmt = $conn->prepare("SELECT 
  a.*, 
  c.sub_kp, 
  d.healthcare, 
  d.hospital, 
  lab.cd4_result, 
  lab.cd4_value, 
  lab.cd4_percent, 
  lab.vl_result, 
  lab.vl_result_value, 
  x.nap_code, 
  x.nap_status, 
  x.nap_staff, 
  x.nap_date, 
  x.nap_comment,
  b.nap_code as hiv_nap_code, 
  b.nap_comment as hiv_nap_comment,
  dctng.lab_result_ng as ng_result_late,
  dctng.lab_result_ct as ct_result_late,
  dvl.lab_result_vl_value as vl_result_late,
  dcd4.lab_result_cd4_value as cd4_result_late,
  dcd4.lab_result_cd4_percent as cd4_percent_late

  FROM clinic a
  LEFT JOIN clinicnewcase d ON a.newcase_id = d.id
  LEFT JOIN cliniclabresult lab ON a.lab_id = lab.id
  LEFT JOIN late_lab_result dctng ON dctng.id = (SELECT max(id) FROM late_lab_result WHERE lab_topic in ('CT', 'NG', 'NG/CT') AND clinic_id = a.clinic_id AND uic = a.uic AND queue = a.q)
  LEFT JOIN late_lab_result dvl ON dvl.id = (SELECT max(id) FROM late_lab_result WHERE lab_topic = 'HIV-VL' AND clinic_id = a.clinic_id AND uic = a.uic AND queue = a.q)
  LEFT JOIN late_lab_result dcd4 ON dcd4.id = (SELECT max(id) FROM late_lab_result WHERE lab_topic = 'CD4' AND clinic_id = a.clinic_id AND uic = a.uic AND queue = a.q)
  LEFT JOIN clinicprecounseling c ON a.pre_id = c.id
  LEFT JOIN nhso_record_nap b on b.source = 'testing' AND b.year = ? AND a.id_card = b.id_card AND a.sn = b.source_id 
  LEFT JOIN nhso_record_nap x on x.source = 'vl' AND b.source_id = x.source_id
  WHERE SUBSTRING(a.id_card, 1, 1) not in ('0', '6', '7') 
  AND lab.vl_result IS NOT NULL
  AND a.service_date >= ? 
  AND a.service_date < ? 
  AND a.clinic_id > 0");

  $stmt->execute($data);

  $dataFetchs = $stmt->fetchall();

  $response = [];

  $uicLists = [];

  $firstTestedDate = [];

  $filterDate = [];

  foreach ($dataFetchs as $key => $item) {

    if ($item->service_date >= $filter) {
      $filterDate[] = $item;
    }
  }

  // $dataFetchs = $filterDate;

  foreach ($filterDate as $key => $item) {
    if (checkPID($item->id_card)) {

      $uicLists[] = $item->uic;

      if (!array_key_exists($item->uic, $firstTestedDate)) {

        $firstTestedDate[$item->uic] = Carbon::parse($item->service_date);
      }

      $date1 = $firstTestedDate[$item->uic];
      $date2 = Carbon::parse($item->service_date);

      $formLastTest = $date1->diffInDays($date2);

      $response[$key]['id'] = $item->sn;
      $response[$key]['type'] = 'Clinic';
      $response[$key]['source'] = 'vl';
      $response[$key]['isreach'] = $item->cbs;
      $response[$key]['location'] = $item->mobile;
      $response[$key]['clinic_type'] = $item->type;
      $response[$key]['service_date'] = $item->service_date;
      $response[$key]['uic'] = $item->uic;
      $response[$key]['uic_count'] = array_count_values($uicLists)[$item->uic];
      $response[$key]['kp'] = $item->sex;
      $response[$key]['sub_kp'] = $item->sub_kp;
      $response[$key]['formLastTest'] = $formLastTest;
      $response[$key]['birth_day'] = (substr($item->uic, -6, 2)) . '/' . (substr($item->uic, -4, 2)) . '/' . ((int)substr($item->uic, -2) > 70 ? (int)substr($item->uic, -2) + 2400 : (int)substr($item->uic, -2) + 2500);
      $response[$key]['firstname'] = $item->firstname;
      $response[$key]['lastname'] = $item->lastname;
      $response[$key]['phone'] = $item->phone;
      $response[$key]['id_card'] = $item->id_card;
      $response[$key]['occupation'] = $item->occupation;
      $response[$key]['healthcare'] = $item->healthcare;
      $response[$key]['hospital'] = $item->hospital;
      $response[$key]['cbs'] = $item->cbs;
      $response[$key]['hiv_result'] = $item->hiv_result;
      $response[$key]['sti_result'] = $item->sti_result;
      $response[$key]['hcv_result'] = $item->hcv_result;

      $response[$key]['ct_result'] = $item->ct_result_late ?? $item->ct_result;
      $response[$key]['ng_result'] = $item->ng_result_late ?? $item->ng_result;
      $response[$key]['cd4_value'] = $item->cd4_result_late ?? $item->cd4_value;
      $response[$key]['cd4_percent'] = $item->cd4_percent_late ?? $item->cd4_percent;
      $response[$key]['vl_value'] = $item->vl_result_late ?? $item->vl_result_value;

      $response[$key]['counselor'] = $item->counselor;
      $response[$key]['mobile'] = $item->mobile;
      $response[$key]['nap_code'] = $item->nap_code;
      $response[$key]['nap_status'] = $item->nap_status;
      $response[$key]['nap_staff'] = $item->nap_staff;
      $response[$key]['nap_date'] = $item->nap_date;
      $response[$key]['nap_comment'] = $item->nap_comment;
      $response[$key]['hiv_nap_code'] = $item->hiv_nap_code;
      $response[$key]['hiv_nap_comment'] = $item->hiv_nap_comment;
    }
  }

  if ($eCascadeCheck) {

    $eCascadeCheck_response = [];

    foreach ($response as $key => $item) {
      if ($item['nap_status'] == 'false' || $item['nap_status'] == null) {
        $eCascadeCheck_response[] = $item;
      }
    }

    return $eCascadeCheck_response;
  }

  return $response;
}

function casePrEPForNap($conn, $eCascadeCheck = false, $period, $filter = '2021-03-01', $uic = '', $hospital = '', $startDate = null, $endDate = null)
{
  $response = [];

  if (!$filter) $filter = '2021-03-01';

  if ($uic) {
    $prep_stock_spending = prep_stock_spending($conn, 'uic', $uic, $filter)->fetchall();
    $prep_refill_spending = prep_refill_spending($conn, $uic)->fetchall();
  } else {
    $prep_stock_spending = prep_stock_spending($conn, 'enter_nap', null, $filter)->fetchall();
    $prep_refill_spending = prep_refill_spending($conn)->fetchall();
  }


  foreach ($prep_stock_spending as $item) {

    $data = [];

    $stock_spending = json_decode($item->prep_med_type);
    $value_spending = json_decode($item->prep_med_amount);

    $med_stock = $stock_spending ? implode(", ", array_map(function ($each_stock) {
      return explode('_', $each_stock)[1] ?? $each_stock;
    }, $stock_spending)) : "";
    $med_title = $stock_spending ? implode(", ", array_map(function ($each_stock) {
      return explode('_', $each_stock)[0] ?? $each_stock;
    }, $stock_spending)) : "";
    $med_value = $value_spending ? implode(", ", $value_spending) : "";

    if ($item->clinic_hbsag_result) {
      $item->lab_hbsag_result = $item->clinic_hbsag_result;
    }
    if ($item->clinic_creatinine_result) {
      $item->lab_creatinine_result = $item->clinic_creatinine_result;
    }
    if ($item->clinic_egfr_result) {
      $item->lab_egfr_result = $item->clinic_egfr_result;
    }
    if ($item->clinic_alt_result) {
      $item->lab_alt_result = $item->clinic_alt_result;
    }


    $data['id'] = $item->sn;
    $data['status'] = 'จ่ายออก';
    $data['topic'] = 'Clinic';
    $data['service_date'] = $item->service_date;
    $data['hiv_result'] = $item->hiv_result;
    $data['sti_result'] = $item->sti_result;
    $data['hcv_result'] = $item->hcv_result;
    $data['condom'] = $item->condomDistribute;
    $data['lubricant'] = $item->lubricantDistribute;
    $data['med_stock'] = $med_stock;
    $data['prep_history'] = $item->prep_taken_check;
    $data['prep_visit'] = $item->prep_service_visit;
    $data['prep_project'] = $item->prep_today_route;
    $data['prep_method'] = $item->prep_service_method;
    $data['prep_code'] = $item->prep;
    $data['prep_nap_code'] = $item->prep_nap_code;
    $data['uic'] = $item->uic;
    $data['kp'] = $item->sex;
    $data['id_card'] = $item->id_card;
    $data['med_title'] = $med_title;
    $data['med_amount'] = $med_value;
    $data['comment'] = $item->prep_comment;
    $data['staff'] = $item->counselor;
    $data['next_visit'] = $item->prep_set_visit;
    $data['next_date'] = $item->nextFuDate;
    $data['input_id'] = $item->input_id;
    $data['nap_status'] = $item->nap_status;
    $data['nap_code'] = $item->nap_code;
    $data['nap_staff'] = $item->nap_staff;
    $data['nap_date'] = $item->nap_date;
    $data['nap_comment'] = $item->nap_comment;
    $data['lab_comment'] = $item->lab_comment;

    $data['lab_creatinine_result'] = $item->lab_creatinine_result;
    $data['lab_egfr_result'] = $item->lab_egfr_result;
    $data['lab_hbsag_result'] = $item->lab_hbsag_result;
    $data['lab_alt_result'] = $item->lab_alt_result;
    $data['lab_file_upload'] = json_decode($item->lab_file_upload);

    $data['lab_nap_status'] = $item->lab_nap_status;
    $data['lab_nap_creatinine_code'] = $item->lab_nap_creatinine_code;
    $data['lab_nap_hbsag_code'] = $item->lab_nap_hbsag_code;
    $data['lab_nap_staff'] = $item->lab_nap_staff;
    $data['lab_nap_date'] = $item->lab_nap_date;
    $data['lab_nap_comment'] = $item->lab_nap_comment;
    $data['prep_no_distribution_reason'] = $item->prep_no_distribution_reason;
    $data['prep_approve_date'] = $item->prep_approve_date;
    $data['update_status'] = $item->update_status;
    $data['prep_approve_doctor'] = $item->prep_approve_doctor;
    $data['prep_approve_signature'] = $item->prep_approve_signature;
    $data['cbo_request_approve_comment'] = $item->cbo_request_approve_comment;
    $data['cbo_request_bloodtest_comment'] = $item->cbo_request_bloodtest_comment;
    $data['hospital_addlab_comment'] = $item->hospital_addlab_comment;
    $data['hospital_request_approve_comment'] = $item->hospital_request_approve_comment;
    $data['hospital_approve_comment'] = $item->hospital_approve_comment;
    $data['hospital_not_approve_comment'] = $item->hospital_not_approve_comment;

    if ($hospital == 'hospital') {
      if ($item->update_status) $response[] = $data;
    } else {
      $response[] = $data;
    }
  }

  foreach ($prep_refill_spending as $item) {

    $data = [];

    $stock_spending = json_decode($item->stock_lot);
    $med_spending = json_decode($item->med);
    $value_spending = json_decode($item->value);

    $med_stock = $stock_spending ? implode(", ", $stock_spending) : "";
    $med_title = $med_spending ? implode(", ", $med_spending) : "";
    $med_value = $value_spending ? implode(", ", $value_spending) : "";

    $data['id'] = $item->id;
    $data['topic'] = 'Refill';
    $data['service_date'] = $item->service_date;
    $data['hiv_result'] = $item->hiv_result;
    $data['sti_result'] = $item->sti_result;
    $data['hcv_result'] = $item->hcv_result;
    $data['condom'] = $item->condomDistribute;
    $data['lubricant'] = $item->lubricantDistribute;
    $data['med_stock'] = $med_stock;
    $data['prep_history'] = 'เคยรับ PrEP มาก่อน';
    $data['prep_visit'] = $item->visit;
    $data['prep_project'] = $item->project;
    $data['prep_method'] = $item->last_prep_method;
    $data['prep_code'] = $item->prep_code;
    $data['prep_nap_code'] = $item->prep_nap_code;
    $data['uic'] = $item->uic;
    $data['kp'] = $item->kp;
    $data['id_card'] = $item->id_card;
    $data['med_title'] = $med_title;
    $data['med_amount'] = $med_value;
    $data['staff'] = $item->cbs;
    $data['next_visit'] = $item->next_topic;
    $data['next_date'] = $item->next_date;
    $data['comment'] = $item->comment;
    $data['input_id'] = $item->input_id;
    $data['nap_status'] = $item->nap_status;
    $data['nap_code'] = $item->nap_code;
    $data['nap_staff'] = $item->nap_staff;
    $data['nap_date'] = $item->nap_date;
    $data['nap_comment'] = $item->nap_comment;
    $data['lab_comment'] = $item->lab_comment;

    $data['lab_creatinine_result'] = $item->lab_creatinine_result ?? '';
    $data['lab_egfr_result'] = $item->lab_egfr_result ?? '';
    $data['lab_hbsag_result'] = $item->lab_hbsag_result ?? '';
    $data['lab_alt_result'] = $item->lab_alt_result ?? '';
    $data['lab_file_upload'] = '';

    $data['lab_nap_status'] = $item->lab_nap_status ?? '';
    $data['lab_nap_creatinine_code'] = $item->lab_nap_creatinine_code ?? '';
    $data['lab_nap_hbsag_code'] = $item->lab_nap_hbsag_code ?? '';
    $data['lab_nap_staff'] = $item->lab_nap_staff ?? '';
    $data['lab_nap_date'] = $item->lab_nap_date ?? '';
    $data['lab_nap_comment'] = $item->lab_nap_comment ?? '';
    $data['prep_no_distribution_reason'] = '';
    $data['prep_approve_signature'] = $item->prep_approve_signature;
    $data['prep_approve_date'] = $item->prep_approve_date;
    $data['update_status'] = $item->update_status;
    $data['prep_approve_doctor'] = $item->prep_approve_doctor;
    $data['cbo_request_approve_comment'] = $item->cbo_request_approve_comment;
    $data['cbo_request_bloodtest_comment'] = $item->cbo_request_bloodtest_comment;
    $data['hospital_addlab_comment'] = $item->hospital_addlab_comment;
    $data['hospital_request_approve_comment'] = $item->hospital_request_approve_comment;
    $data['hospital_approve_comment'] = $item->hospital_approve_comment;
    $data['hospital_not_approve_comment'] = $item->hospital_not_approve_comment;

    if ($hospital == 'hospital') {
      if ($item->update_status) $response[] = $data;
    } else {
      $response[] = $data;
    }
  }

  if ($startDate && $endDate) {
    $filterData = [];

    foreach ($response as $key => $item) {
      if ($item['service_date'] >= $startDate && $item['service_date'] < $endDate) {
        $filterData[] = $item;
      }
    }

    return $filterData;
  }

  if ($period == 'all') {
    $period_data = [];

    foreach ($response as $key => $item) {
      if ($item['service_date'] >= '2015-01-01' && $item['service_date'] < checkQuarter()['q4_end']) {
        $period_data[] = $item;
      }
    }

    $response = $period_data;
  }

  if ($period == 'month') {
    $period_data = [];

    foreach ($response as $key => $item) {
      if ($item['service_date'] >= checkQuarter()['this_month_start'] && $item['service_date'] < checkQuarter()['this_month_end']) {
        $period_data[] = $item;
      }
    }

    $response = $period_data;
  }

  if ($period == 'quarter') {
    $period_data = [];

    foreach ($response as $key => $item) {
      if ($item['service_date'] >= checkQuarter()['this_quarter_start'] && $item['service_date'] < checkQuarter()['this_quarter_end']) {
        $period_data[] = $item;
      }
    }

    $response = $period_data;
  }

  if ($period == 'fy21') {
    $period_data = [];

    foreach ($response as $key => $item) {
      if ($item['service_date'] >= checkQuarter('2021-01-01')['q1_start'] && $item['service_date'] < checkQuarter('2021-01-01')['q4_end']) {
        $period_data[] = $item;
      }
    }

    $response = $period_data;
  }

  if ($period == 'fy22') {
    $period_data = [];

    foreach ($response as $key => $item) {
      if ($item['service_date'] >= checkQuarter('2022-01-01')['q1_start'] && $item['service_date'] < checkQuarter('2022-01-01')['q4_end']) {
        $period_data[] = $item;
      }
    }

    $response = $period_data;
  }

  if ($period == 'fy23') {
    $period_data = [];

    foreach ($response as $key => $item) {
      if ($item['service_date'] >= checkQuarter('2023-01-01')['q1_start'] && $item['service_date'] < checkQuarter('2023-01-01')['q4_end']) {
        $period_data[] = $item;
      }
    }

    $response = $period_data;
  }

  if ($period == 'fy24') {
    $period_data = [];

    foreach ($response as $key => $item) {
      if ($item['service_date'] >= checkQuarter('2024-01-01')['q1_start'] && $item['service_date'] < checkQuarter('2024-01-01')['q4_end']) {
        $period_data[] = $item;
      }
    }

    $response = $period_data;
  }

  if ($eCascadeCheck) {

    $eCascadeCheck_response = [];

    foreach ($response as $key => $item) {
      if ($item['nap_status'] == 'false' || $item['nap_status'] == null) {
        $eCascadeCheck_response[] = $item;
      }
    }

    $response = $eCascadeCheck_response;
  }

  $filterDate = [];

  foreach ($response as $key => $item) {

    if ($item['service_date'] >= $filter) {
      $filterDate[] = $item;
    }
  }

  $response = $filterDate;

  return $response;
}

function casePrEPForLabEnter($conn, $eCascadeCheck = false, $period, $year = '2021', $filter = '2021-03-01', $hospital = '', $startDate = NULL, $endDate = NULL)
{

  if (!$filter) $filter = '2021-03-01';

  $prep_stock_spending = prep_stock_spending($conn, 'enter_lab', null, $filter, $startDate, $endDate)->fetchall();
  // $prep_stock_spending = prep_stock_spending($conn, 'enter_lab', null, $filter)->fetchall();

  $response = [];

  foreach ($prep_stock_spending as $item) {

    $data = [];

    $stock_spending = json_decode($item->prep_med_type);
    $value_spending = json_decode($item->prep_med_amount);

    $med_stock = $stock_spending ? implode(", ", array_map(function ($each_stock) {
      return explode('_', $each_stock)[1] ?? $each_stock;
    }, $stock_spending)) : "";
    $med_title = $stock_spending ? implode(", ", array_map(function ($each_stock) {
      return explode('_', $each_stock)[0] ?? $each_stock;
    }, $stock_spending)) : "";
    $med_value = $value_spending ? implode(", ", $value_spending) : "";

    $lab_file_upload = [];
    if ($item->lab_file_upload) $lab_file_upload = array_merge($lab_file_upload, json_decode($item->lab_file_upload));
    if ($item->hiv_file_upload) $lab_file_upload = array_merge($lab_file_upload, json_decode($item->hiv_file_upload));
    if ($item->creatinine_file_upload) $lab_file_upload = array_merge($lab_file_upload, json_decode($item->creatinine_file_upload));
    if ($item->hbsag_file_upload) $lab_file_upload = array_merge($lab_file_upload, json_decode($item->hbsag_file_upload));

    $serviceProvide = $item->serviceProvide ? unserialize($item->serviceProvide) : [];

    $data['topic_id'] = $item->sn; // $data['id']
    $data['lab_file_upload'] = $lab_file_upload; //
    $data['prep_nap_code'] = $item->nap_code; //
    $data['prep_nap_creatinine'] = $item->lab_nap_creatinine_code; //
    $data['prep_nap_hbsag'] = $item->lab_nap_hbsag_code; //
    $data['comment'] = $item->prep_comment; //
    $data['request_type'] = $item->type; //
    $data['request_date'] = $item->request_date; //
    $data['update_status'] = $item->update_status; //
    $data['update_staff'] = $item->update_staff; //
    $data['update_date'] = $item->update_date; //

    $data['id'] = $item->sn;
    $data['clinic_id'] = $item->clinic_id;
    $data['queue'] = $item->q;
    $data['status'] = 'จ่ายออก';
    $data['topic'] = 'Clinic';
    $data['service_date'] = $item->service_date;
    $data['hiv_result'] = $item->hiv_result;
    $data['sti_result'] = $item->sti_result;
    $data['hcv_result'] = $item->hcv_result;
    $data['condom'] = $item->condomDistribute;
    $data['lubricant'] = $item->lubricantDistribute;
    $data['med_stock'] = $med_stock;
    $data['prep_history'] = $item->prep_taken_check;
    $data['prep_visit'] = $item->prep_service_visit;
    $data['prep_project'] = $item->prep_today_route;
    $data['prep_method'] = $item->prep_service_method;
    $data['prep_code'] = $item->prep;
    $data['prep_nap_code'] = $item->prep_nap_code;
    $data['uic'] = $item->uic;
    $data['kp'] = $item->sex;
    $data['firstname'] = $item->firstname;
    $data['lastname'] = $item->lastname;
    $data['id_card'] = $item->id_card;
    $data['med_title'] = $med_title;
    $data['med_amount'] = $med_value;
    $data['staff'] = $item->counselor;
    $data['next_visit'] = $item->prep_set_visit;
    $data['next_date'] = $item->nextFuDate;
    $data['input_id'] = $item->input_id;
    $data['nap_status'] = $item->nap_status;
    $data['nap_code'] = $item->nap_code;
    $data['nap_staff'] = $item->nap_staff;
    $data['nap_date'] = $item->nap_date;
    $data['nap_comment'] = $item->nap_comment;
    $data['lab_creatinine_result'] = $item->lab_creatinine_result;
    $data['lab_egfr_result'] = $item->lab_egfr_result;
    $data['lab_hbsag_result'] = $item->lab_hbsag_result;
    $data['lab_status'] = $item->lab_status;
    $data['lab_staff'] = $item->lab_staff;
    $data['lab_date'] = $item->lab_date;
    $data['lab_comment'] = $item->lab_comment;
    $data['prep_no_distribution_reason'] = $item->prep_no_distribution_reason;
    $data['extra_blood'] = $item->extra_blood;
    $data['serviceProvide'] = $serviceProvide;
    $data['extra_blood_services'] = json_decode($item->extra_blood_services);

    if ($hospital == 'hospital') {

      if ($data['prep_code'] != '' && $item->update_status == 'แจ้งส่งเลือดตรวจ') $response[] = $data;
    } else {

      if ($data['prep_code'] != '') $response[] = $data;
    }
  }

  if ($startDate && $endDate) {
    $filterData = [];

    foreach ($response as $key => $item) {
      if ($item['service_date'] >= $startDate && $item['service_date'] < $endDate) {
        $filterData[] = $item;
      }
    }

    return $filterData;
  }

  if ($period == 'all') {
    $period_data = [];

    foreach ($response as $key => $item) {
      if ($item['service_date'] >= '2015-01-01' && $item['service_date'] < checkQuarter()['q4_end']) {
        $period_data[] = $item;
      }
    }

    $response = $period_data;
  }

  if ($period == 'month') {
    $period_data = [];

    foreach ($response as $key => $item) {
      if ($item['service_date'] >= checkQuarter()['this_month_start'] && $item['service_date'] < checkQuarter()['this_month_end']) {
        $period_data[] = $item;
      }
    }

    $response = $period_data;
  }

  if ($period == 'quarter') {
    $period_data = [];

    foreach ($response as $key => $item) {
      if ($item['service_date'] >= checkQuarter()['this_quarter_start'] && $item['service_date'] < checkQuarter()['this_quarter_end']) {
        $period_data[] = $item;
      }
    }

    $response = $period_data;
  }

  if ($period == 'fy') {
    $period_data = [];

    foreach ($response as $key => $item) {
      if ($item['service_date'] >= checkQuarter()['q1_start'] && $item['service_date'] < checkQuarter()['q4_end']) {
        $period_data[] = $item;
      }
    }

    $response = $period_data;
  }

  if ($eCascadeCheck) {

    $eCascadeCheck_response = [];

    foreach ($response as $key => $item) {
      if ($item['lab_status'] == 'false' || $item['lab_status'] == null) {
        $eCascadeCheck_response[] = $item;
      }
    }

    $response = $eCascadeCheck_response;
  }

  $filterDate = [];

  foreach ($response as $key => $item) {

    if ($item['service_date'] >= $filter) {
      $filterDate[] = $item;
    }
  }

  $response = $filterDate;

  return $response;
}

function casenPEPForLabEnter($eCascadeCheck = false, $period, $year = '2021', $filter = '2021-03-01', $hospital = '', $startDate = NULL, $endDate = NULL)
{

  if (!$filter) $filter = '2021-03-01';

  $npep_stock_spending = npep_stock_spending('enter_lab', null, $filter)->fetchall();

  $response = [];

  foreach ($npep_stock_spending as $item) {

    $data = [];

    $stock_spending = json_decode($item->npep_med_type);
    $value_spending = json_decode($item->npep_med_amount);

    $med_stock = $stock_spending ? implode(", ", array_map(function ($each_stock) {
      return explode('_', $each_stock)[1] ?? $each_stock;
    }, $stock_spending)) : "";
    $med_title = $stock_spending ? implode(", ", array_map(function ($each_stock) {
      return explode('_', $each_stock)[0] ?? $each_stock;
    }, $stock_spending)) : "";
    $med_value = $value_spending ? implode(", ", $value_spending) : "";

    $data['topic_id'] = $item->sn; // $data['id']
    $data['lab_file_upload'] = $item->lab_file_upload ? json_decode($item->lab_file_upload) : []; //
    $data['npep_nap_code'] = $item->nap_code; //
    $data['npep_nap_creatinine'] = $item->lab_nap_creatinine_code; //
    $data['npep_nap_hbsag'] = $item->lab_nap_hbsag_code; //
    $data['comment'] = $item->npep_comment; //
    $data['request_type'] = $item->type; //
    $data['request_date'] = $item->request_date; //
    $data['update_status'] = $item->update_status; //
    $data['update_staff'] = $item->update_staff; //
    $data['update_date'] = $item->update_date; //

    $data['id'] = $item->npep_post_id;
    $data['status'] = 'จ่ายออก';
    $data['topic'] = 'Clinic';
    $data['service_date'] = $item->service_date;
    $data['hiv_result'] = $item->hiv_result;
    $data['sti_result'] = $item->sti_result;
    $data['hcv_result'] = $item->hcv_result;
    $data['condom'] = $item->condomDistribute;
    $data['lubricant'] = $item->lubricantDistribute;
    $data['med_stock'] = $med_stock;
    $data['npep_history'] = $item->npep_taken_check;
    $data['npep_visit'] = $item->npep_service_visit;
    $data['npep_project'] = $item->npep_today_route;
    $data['npep_method'] = $item->npep_service_method;
    $data['npep_code'] = $item->npep_code;
    $data['npep_nap_code'] = $item->npep_nap_code;
    $data['uic'] = $item->uic;
    $data['kp'] = $item->kp;
    $data['firstname'] = $item->firstname;
    $data['lastname'] = $item->lastname;
    $data['id_card'] = $item->id_card;
    $data['med_title'] = $med_title;
    $data['med_amount'] = $med_value;
    $data['staff'] = $item->npep_cbs;
    $data['next_visit'] = $item->npep_set_visit;
    $data['next_date'] = $item->nextFuDate;
    $data['input_id'] = $item->input_id;
    $data['nap_status'] = $item->nap_status;
    $data['nap_code'] = $item->nap_code;
    $data['nap_staff'] = $item->nap_staff;
    $data['nap_date'] = $item->nap_date;
    $data['nap_comment'] = $item->nap_comment;
    $data['lab_creatinine_result'] = $item->lab_creatinine_result;
    $data['lab_egfr_result'] = $item->lab_egfr_result;
    $data['lab_hbsag_result'] = $item->lab_hbsag_result;
    $data['lab_alt_result'] = $item->lab_alt_result;
    $data['lab_status'] = $item->lab_status;
    $data['lab_staff'] = $item->lab_staff;
    $data['lab_date'] = $item->lab_date;
    $data['lab_comment'] = $item->lab_comment;
    $data['npep_no_distribution_reason'] = $item->npep_no_distribution_reason;
    $data['extra_blood'] = $item->extra_blood;
    $data['extra_blood_services'] = json_decode($item->extra_blood_services);

    if ($hospital == 'hospital') {

      if ($data['npep_code'] != '' && $item->update_status == 'แจ้งส่งเลือดตรวจ') $response[] = $data;
    } else {

      if ($data['npep_code'] != '') $response[] = $data;
    }
  }

  if ($startDate && $endDate) {
    $filterData = [];

    foreach ($response as $key => $item) {
      if ($item['service_date'] >= $startDate && $item['service_date'] < $endDate) {
        $filterData[] = $item;
      }
    }

    return $filterData;
  }

  if ($period == 'all') {
    $period_data = [];

    foreach ($response as $key => $item) {
      if ($item['service_date'] >= '2015-01-01' && $item['service_date'] < checkQuarter()['q4_end']) {
        $period_data[] = $item;
      }
    }

    $response = $period_data;
  }

  if ($period == 'month') {
    $period_data = [];

    foreach ($response as $key => $item) {
      if ($item['service_date'] >= checkQuarter()['this_month_start'] && $item['service_date'] < checkQuarter()['this_month_end']) {
        $period_data[] = $item;
      }
    }

    $response = $period_data;
  }

  if ($period == 'quarter') {
    $period_data = [];

    foreach ($response as $key => $item) {
      if ($item['service_date'] >= checkQuarter()['this_quarter_start'] && $item['service_date'] < checkQuarter()['this_quarter_end']) {
        $period_data[] = $item;
      }
    }

    $response = $period_data;
  }

  if ($period == 'fy') {
    $period_data = [];

    foreach ($response as $key => $item) {
      if ($item['service_date'] >= checkQuarter()['q1_start'] && $item['service_date'] < checkQuarter()['q4_end']) {
        $period_data[] = $item;
      }
    }

    $response = $period_data;
  }

  if ($eCascadeCheck) {

    $eCascadeCheck_response = [];

    foreach ($response as $key => $item) {
      if ($item['lab_status'] == 'false' || $item['lab_status'] == null) {
        $eCascadeCheck_response[] = $item;
      }
    }

    $response = $eCascadeCheck_response;
  }

  $filterDate = [];

  foreach ($response as $key => $item) {

    if ($item['service_date'] >= $filter) {
      $filterDate[] = $item;
    }
  }

  $response = $filterDate;

  return $response;
}

function tdCopy($dataCopy, $dataShow, $tdStyle = '', $tdClass = '', $tdProp = '')
{

  $site = '';
  // $site = 'chiangmai/';

  return "<td {$tdProp} onmouseenter='showClipBoard($(this))' onmouseleave='hideClipBoard($(this))' class='{$tdClass}' style='position:relative;{$tdStyle}'>{$dataShow}<button style='position:absolute;right:2px;top:2px;' class='clipboard' data-clipboard-snippet='' data-clipboard-text='{$dataCopy}'><img class='clippy' width='13' src='/$site" . "dist/img/clippy.svg' alt='Copy to clipboard'></button></td>";
}

function eCascadeDate($date = '')
{

  if ($date == '' || $date == null || $date == 0) {
    return '';
  }

  $date_arry = explode("-", dateOnly($date));

  return "{$date_arry[1]}/{$date_arry[2]}/{$date_arry[0]}";
}

function kpColor($data, $size = '')
{
  if ($data == 'MSM') {
    return labelbig($data, 'primary', $size);
  }
  if ($data == 'TG') {
    return labelbig($data, 'warning', $size);
  }
  if ($data == 'TGW') {
    return labelbig($data, 'warning', $size);
  }
  if ($data == 'TGM') {
    return labelbig($data, 'black', $size);
  }
  if ($data == 'MSW') {
    return labelbig($data, 'danger', $size);
  }
  if ($data == 'TGSW') {
    return labelbig($data, 'maroon', $size);
  }
  if ($data == 'FSW') {
    return labelbig($data, 'purple', $size);
  }
  if ($data == 'PWID-Male') {
    return labelbig($data, 'teal', $size);
  }
  if ($data == 'PWID-Female') {
    return labelbig($data, 'navy', $size);
  }
  if ($data == 'Male') {
    return labelbig($data, 'info', $size);
  }
  if ($data == 'Female') {
    return labelbig($data, 'default', $size);
  }
}

function hivstStatusColor($data, $size = '')
{

  if ($data == 'Registered') {
    return labelbig($data, 'primary', $size);
  }
  if ($data == 'Delivered') {
    return labelbig($data, 'warning', $size);
  }
  if ($data == 'Recieved') {
    return labelbig($data, 'purple', $size);
  }
  if ($data == 'Tested') {
    return labelbig($data, 'info', $size);
  }
  if ($data == 'Confirmed') {
    return labelbig($data, 'teal', $size);
  }
  if ($data == 'Finished') {
    return labelbig($data, 'maroon', $size);
  }
  if ($data == 'Canceled') {
    return labelbig($data, 'black', $size);
  }
  return labelbig($data, 'default', $size);
}

// fetch export database data
function registerForB1($startDate, $endDate, $eCascadeCheck = false)
{
  global $conn;

  $where = "WHERE status in ('Testing', 'Wait Post-Counseling', 'Services Finished') AND recruitment = 'walk in' AND created_at >= ? AND created_at < ?";

  if (in_array($_SESSION['site_specific']->sitename2, ['RSAT'])) $where = "WHERE status in ('Testing', 'Wait Post-Counseling', 'Services Finished') AND (recruitment = 'walk in' OR recruitment = 'retain') AND created_at >= ? AND created_at < ?";

  $data = [$startDate, $endDate];

  if ($eCascadeCheck) $where .= " AND (eCascadeCheck = 'false' OR eCascadeCheck is null)";

  $stmt = $conn->prepare("SELECT * FROM clinicnewcase $where ORDER BY id");

  $stmt->execute($data);

  return $stmt;
}

// fetch export database data
function clinicForC1($startDate, $endDate, $eCascadeCheck = false)
{
  global $conn;

  $where = "WHERE c.clinic_id > 0 AND c.service_date >= ? AND c.service_date < ? AND c.hiv_result != '-'";

  if ($eCascadeCheck) $where .= " AND (c.eCascadeCheck = 'false' OR c.eCascadeCheck is null)";

  $data = [$startDate, $endDate];

  $stmt = $conn->prepare("SELECT 
    c.sn, 
    c.prep, 
    c.service_date, 
    c.uic, 
    c.nickname, 
    c.phone, 
    c.sex, 
    c.cbs, 
    c.type, 
    c.id_card, 
    c.hiv_result, 
    c.sti_result, 
    c.hcv_result, 
    c.cd4, 
    c.cd4percent, 
    c.comment1, 
    c.comment2, 
    c.eCascadeCheck, 
    c.eCascadeStaff, 
    c.eCascadeDate, 
    n.recruiting, 
    n.wantTestReason, 
    p.condomDistribute, 
    p.lubricantDistribute,
    p.prep_distribution,
    p.prep_no_distribution_reason,
    p.prep_comment,
    pre.prep_provide,
    pre.prep_taken_check,
    pre.prep_service_visit
    FROM clinic c 
    LEFT JOIN clinicnewcase n ON c.newcase_id = n.id
    LEFT JOIN clinicprecounseling pre ON c.pre_id = pre.id
    LEFT JOIN clinicpostcounseling p ON c.post_id = p.id
    $where
    order by c.sn");

  $stmt->execute($data);

  return $stmt;
}

function reachA1Left($date = '')
{
  global $conn;
  if ($date == '') $date = checkQuarter()['q1_start'];
  $data = [$date];

  $stmt = $conn->prepare("SELECT * FROM reach where reachdate >= ? AND (eCascadeCheck = 'false' OR eCascadeCheck is null)");
  $stmt->execute($data);

  return $stmt;
}

function retainA1Left($date = '')
{
  global $conn;
  if ($date == '') $date = checkQuarter()['q1_start'];
  $data = [$date];

  $stmt = $conn->prepare("SELECT * FROM retainnegative where retainDate >= ? AND phoneOption = 'ติดต่อได้ เคสยินดีให้ข้อมูล' AND (eCascadeCheck = 'false' OR eCascadeCheck is null)");
  $stmt->execute($data);

  return $stmt;
}

function registerB1Left($date = '')
{
  global $conn;
  if ($date == '') $date = checkQuarter()['q1_start'];
  $data = [$date];

  $stmt = $conn->prepare("SELECT * FROM clinicnewcase where created_at >= ? AND status in ('Testing', 'Wait Post-Counseling', 'Services Finished') AND recruitment = 'walk in' AND (eCascadeCheck = 'false' OR eCascadeCheck is null)");
  $stmt->execute($data);

  return $stmt;
}

function clinicC1Left($date = '')
{
  global $conn;
  if ($date == '') $date = checkQuarter()['q1_start'];
  $data = [$date];

  $stmt = $conn->prepare("SELECT sn FROM clinic where clinic_id > 0 AND service_date >= ? AND hiv_result != '-' AND (eCascadeCheck = 'false' OR eCascadeCheck is null)");
  $stmt->execute($data);

  return $stmt;
}

function hivReferD2Left($date = '')
{
  global $conn;
  if ($date == '') $date = checkQuarter()['q1_start'];
  $data = [$date];

  $stmt = $conn->prepare("SELECT * FROM refertotreat where referDate >= ? AND topic = 'HIV' AND (eCascadeCheck = 'false' OR eCascadeCheck is null)");
  $stmt->execute($data);

  return $stmt;
}

function hivRetainD2Left($date = '')
{
  global $conn;
  if ($date == '') $date = checkQuarter()['q1_start'];
  $data = [$date];

  $stmt = $conn->prepare("SELECT * FROM caresupport_activity where service_date >= ? AND care_topic like '%HIV%' AND (eCascadeCheck = 'false' OR eCascadeCheck is null)");
  $stmt->execute($data);

  return $stmt;
}

function reachNapLeft($conn, $period, $datefilter = '2021-10-01', $year = '2021', $startDate, $endDate)
{

  $reachDatas = [];
  $retainDatas = [];
  $reachDatas = caseReachForNHSOReach($conn, false, $period, $year, $datefilter, $startDate, $endDate);
  $retainDatas = caseRetainForNHSOReach($conn, false, $period, $year, $datefilter, $startDate, $endDate);
  $clinicWalkinDatas = caseClinicForNHSOReach($conn, false, $period, $year, $datefilter, $startDate, $endDate);

  $nap = array_merge($clinicWalkinDatas, $reachDatas, $retainDatas);

  $result = [];

  $uicLists = [];
  $kpList = ['MSM', 'MSW', 'TG', 'TGSW','TGM', 'FSW', 'PWID-Male', 'PWID-Female', 'Male', 'Female'];
  foreach ($nap as $item) {

    if (in_array($item['kp'], $kpList) && !in_array(substr($item['id_card'], 0, 1), [0, 6, 7])) {
      if (!in_array($item['uic'], $uicLists) && $item['service_date'] >= $datefilter) {
        $uicLists[] = $item['uic'];
        if ($item['nap_status'] != 'true') $result[] = $item;
      }
    }
  }

  return $result;
}

function clinicCaseByTime($type = 'DIC', $date, $dayName, $timeStart, $timeEnd)
{
  global $conn;

  $data = [$type, $date, $dayName, $timeStart, $timeEnd];

  $stmt = $conn->prepare("SELECT count(time(service_date)) as time
  FROM `clinic`
  WHERE 
  type = ? AND
  `service_date` >= ? AND 
  DAYNAME(service_date) = ? AND
  time(service_date) >= ? AND
  time(service_date) < ?");
  $stmt->execute($data);

  return $stmt;;
}

function clinicCaseForGraph()
{
  $date = '2019-10-01';
  $days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

  $times = [
    ['09:00:00', '09:10:00'],
    ['09:10:00', '09:20:00'],
    ['09:20:00', '09:30:00'],
    ['09:30:00', '09:40:00'],
    ['09:40:00', '09:50:00'],
    ['09:50:00', '10:00:00'],
    ['10:00:00', '10:10:00'],
    ['10:10:00', '10:20:00'],
    ['10:20:00', '10:30:00'],
    ['10:30:00', '10:40:00'],
    ['10:40:00', '10:50:00'],
    ['10:50:00', '11:00:00'],
    ['11:00:00', '11:10:00'],
    ['11:10:00', '11:20:00'],
    ['11:20:00', '11:30:00'],
    ['11:30:00', '11:40:00'],
    ['11:40:00', '11:50:00'],
    ['11:50:00', '12:00:00'],
    ['12:00:00', '12:10:00'],
    ['12:10:00', '12:20:00'],
    ['12:20:00', '12:30:00'],
    ['12:30:00', '12:40:00'],
    ['12:40:00', '12:50:00'],
    ['12:50:00', '13:00:00'],
    ['13:00:00', '13:10:00'],
    ['13:10:00', '13:20:00'],
    ['13:20:00', '13:30:00'],
    ['13:30:00', '13:40:00'],
    ['13:40:00', '13:50:00'],
    ['13:50:00', '14:00:00'],
    ['14:00:00', '14:10:00'],
    ['14:10:00', '14:20:00'],
    ['14:20:00', '14:30:00'],
    ['14:30:00', '14:40:00'],
    ['14:40:00', '14:50:00'],
    ['14:50:00', '15:00:00'],
    ['15:00:00', '15:10:00'],
    ['15:10:00', '15:20:00'],
    ['15:20:00', '15:30:00'],
    ['15:30:00', '15:40:00'],
    ['15:40:00', '15:50:00'],
    ['15:50:00', '16:00:00'],
    ['16:00:00', '16:10:00'],
    ['16:10:00', '16:20:00'],
    ['16:20:00', '16:30:00'],
    ['16:30:00', '16:40:00'],
    ['16:40:00', '16:50:00'],
    ['16:50:00', '17:00:00'],
    ['17:00:00', '17:10:00'],
    ['17:10:00', '17:20:00'],
    ['17:20:00', '17:30:00'],
    ['17:30:00', '17:40:00'],
    ['17:40:00', '17:50:00'],
    ['17:50:00', '18:00:00'],
    ['18:00:00', '18:10:00'],
    ['18:10:00', '18:20:00'],
    ['18:20:00', '18:30:00'],
    ['18:30:00', '18:40:00'],
    ['18:40:00', '18:50:00'],
    ['18:50:00', '19:00:00']
  ];

  $timesx = [
    ['09:00:00', '09:15:00'],
    ['09:15:00', '09:30:00'],
    ['09:30:00', '09:45:00'],
    ['09:45:00', '10:00:00'],
    ['10:00:00', '10:15:00'],
    ['10:15:00', '10:30:00'],
    ['10:30:00', '10:45:00'],
    ['10:45:00', '11:00:00'],
    ['11:00:00', '11:15:00'],
    ['11:15:00', '11:30:00'],
    ['11:30:00', '11:45:00'],
    ['11:45:00', '12:00:00'],
    ['12:00:00', '12:15:00'],
    ['12:15:00', '12:30:00'],
    ['12:30:00', '12:45:00'],
    ['12:45:00', '13:00:00'],
    ['13:00:00', '13:15:00'],
    ['13:15:00', '13:30:00'],
    ['13:30:00', '13:45:00'],
    ['13:45:00', '14:00:00'],
    ['14:00:00', '14:15:00'],
    ['14:15:00', '14:30:00'],
    ['14:30:00', '14:45:00'],
    ['14:45:00', '15:00:00'],
    ['15:00:00', '15:15:00'],
    ['15:15:00', '15:30:00'],
    ['15:30:00', '15:45:00'],
    ['15:45:00', '16:00:00'],
    ['16:00:00', '16:15:00'],
    ['16:15:00', '16:30:00'],
    ['16:30:00', '16:45:00'],
    ['16:45:00', '17:00:00'],
    ['17:00:00', '17:15:00'],
    ['17:15:00', '17:30:00'],
    ['17:30:00', '17:45:00'],
    ['17:45:00', '18:00:00'],
    ['18:00:00', '18:15:00'],
    ['18:15:00', '18:30:00'],
    ['18:30:00', '18:45:00'],
    ['18:45:00', '19:00:00']
  ];

  $timeSlot30 = [
    ['09:00:00', '09:30:00'],
    ['09:30:00', '10:00:00'],
    ['10:00:00', '10:30:00'],
    ['10:30:00', '11:00:00'],
    ['11:00:00', '11:30:00'],
    ['11:30:00', '12:00:00'],
    ['12:00:00', '12:30:00'],
    ['12:30:00', '13:00:00'],
    ['13:00:00', '13:30:00'],
    ['13:30:00', '14:00:00'],
    ['14:00:00', '14:30:00'],
    ['14:30:00', '15:00:00'],
    ['15:00:00', '15:30:00'],
    ['15:30:00', '16:00:00'],
    ['16:00:00', '16:30:00'],
    ['16:30:00', '17:00:00'],
    ['17:00:00', '17:30:00'],
    ['17:30:00', '18:00:00'],
    ['18:00:00', '18:30:00'],
    ['18:30:00', '19:00:00'],
    ['19:00:00', '19:30:00'],
    ['19:30:00', '20:00:00'],
    ['20:00:00', '20:30:00'],
    ['20:30:00', '21:00:00'],
    ['21:00:00', '21:30:00'],
    ['21:30:00', '22:00:00'],
  ];

  $timesh = [
    ['09:00:00', '10:00:00'],
    ['10:00:00', '11:00:00'],
    ['11:00:00', '12:00:00'],
    ['12:00:00', '13:00:00'],
    ['13:00:00', '14:00:00'],
    ['14:00:00', '15:00:00'],
    ['15:00:00', '16:00:00'],
    ['16:00:00', '17:00:00'],
    ['17:00:00', '18:00:00'],
    ['18:00:00', '19:00:00'],
    ['19:00:00', '20:00:00'],
    ['20:00:00', '21:00:00'],
    ['21:00:00', '22:00:00'],
  ];

  $count = 0;
  $total = 0;

  $dayDatas = [];

  foreach ($days as $day) {

    foreach ($timesh as $time) {
      $count = clinicCaseByTime('DIC', $date, $day, $time[0], $time[1])->fetch()->time;

      $dayDatas['data'][$day][] = $count;

      $total = $total + $count;
      // echo $count.'<br>';
    }
  }

  $dayDatas['total'] = $total;
  $dayDatas['time'] = $timesh;

  return $dayDatas;
}

function startOfThisQuarter($currentDate = '')
{

  if ($currentDate == '') $currentDate = date("Y-m-d");

  if (date('m', strtotime($currentDate)) > 9) return date('Y', strtotime($currentDate)) . "-10-01";
  if (date('m', strtotime($currentDate)) > 6) return date('Y', strtotime($currentDate)) . "-07-01";
  if (date('m', strtotime($currentDate)) > 3) return date('Y', strtotime($currentDate)) . "-04-01";
  if (date('m', strtotime($currentDate)) > 0) return date('Y', strtotime($currentDate)) . "-01-01";
}

function fetchIncentiveStock()
{
  global $conn;

  $data = [];

  $stmt = $conn->prepare("SELECT * FROM incentive_stock");
  $stmt->execute($data);

  return $stmt;
}

function isProvidedPrEP($clinic_id, $uic)
{
  global $conn;

  $data = [$clinic_id, $uic, 'ให้บริการ PrEP'];

  $stmt = $conn->prepare("SELECT id, clinic_id, uic, prep_provide, serviceProvide FROM clinicprecounseling where clinic_id = ? and uic = ? and prep_provide = ?");
  $stmt->execute($data);

  $data = $stmt->fetch();

  return $data ? true : false;
}

function checkPrEPProfile($uic)
{
  global $conn;

  $data = [$uic];

  $stmt = $conn->prepare("SELECT * FROM prep_profile WHERE uic = ?");
  $stmt->execute($data);

  return $stmt->fetch();
}

function fetchClinicPrEPData($clinic_id, $uic)
{
  global $conn;

  $data = [$clinic_id, $uic];

  $stmt = $conn->prepare("SELECT 
  a.id as pre_id, a.clinic_id, a.uic,
  a.prep_taken_check,
  a.prep_taken_route,
  a.prep_location,
  a.prep_provide,
  a.prep_no_provide_reason,
  a.prep_not_pass_criteria,
  a.prep_today_route,
  a.prep_service_step,
  a.prep_service_visit,
  a.prep_service_method,
  a.prep_service_express,
  a.prep_check_consent,
  b.*,
  a.prep_code,
  c.id_card
  
  FROM clinicprecounseling a 
  LEFT JOIN prep_profile b ON a.uic = b.uic
  LEFT JOIN clinicnewcase c ON c.clinic_id = a.clinic_id AND c.queue = a.queue
  where a.clinic_id = ? and a.uic = ?");
  $stmt->execute($data);

  return $stmt;
}

function fetchIncentiveStockCheck($incentive)
{
  global $conn;

  $data = [$incentive];

  $stmt = $conn->prepare("SELECT * FROM incentive_stock where name = ?");
  $stmt->execute($data);

  return $stmt;
}

function insertNewIncentive($incentive)
{
  global $conn;

  $data = [$incentive];

  $stmt = $conn->prepare("INSERT into incentive_stock (name) VALUE (?)");
  $stmt->execute($data);

  return $conn->lastInsertId();
}

function deleteIncentive($id)
{
  global $conn;

  $data = [$id];

  $stmt = $conn->prepare("DELETE FROM incentive_stock WHERE id = ?");
  $stmt->execute($data);

  return $stmt->rowCount();
}

function clean_input($data)
{
  $data = trim($data);
  $data = stripslashes($data);
  $data = htmlspecialchars($data);
  return $data;
}

function ajaxFetchPrEPLists($period = 'all')
{
  global $conn;

  $start = '2010-10-01';
  $end = date('Y-m-d');

  if ($period == 'month') {
    $start = checkQuarter()['this_month_start'];
    $end = checkQuarter()['this_month_end'];
  }

  if ($period == 'quarter') {
    $start = checkQuarter()['this_quarter_start'];
    $end = checkQuarter()['this_quarter_end'];
  }

  if ($period == 'fy2022') {
    $start = checkQuarter('2022-01-01')['q1_start'];
    $end = checkQuarter('2022-01-01')['q4_end'];
  }

  if ($period == 'fy2021') {
    $start = checkQuarter('2021-01-01')['q1_start'];
    $end = checkQuarter('2021-01-01')['q4_end'];
  }

  $data = [$start, $end];

  $where = "WHERE a.last_contact_date >= ? AND a.last_contact_date < ?";

  if ($period == 'all') $where = "";

  $stmt = $conn->prepare("SELECT 
  a.* ,
  a.phone as prep_phone,
  b.phone, b.file_code, b.counselor,
  c.prep_left, c.prep_adherence, c.prep_adherence_ondemand,
  d.prep_approve_doctor, d.prep_approve_signature, d.update_status, d.hospital_approve_comment
  from prep_profile a
  LEFT JOIN clinic b ON a.uic = b.uic AND b.sn = (
            SELECT MAX(sn) 
            FROM clinic c 
            WHERE c.uic = a.uic)
  LEFT JOIN clinicprecounseling c ON b.pre_id = c.id
  LEFT JOIN prep_hospital_request d ON a.uic = b.uic and a.prep_code = d.prep_code
  AND d.id = 
        (
           SELECT MAX(id) 
           FROM prep_hospital_request e 
           WHERE e.uic = d.uic
        )
  $where
  ");

  $stmt->execute($data);

  return $stmt;
}

function fetch_clinic_caseinfo($period = 'all', $get_start = '', $get_end = '')
{
  global $conn;

  $start = '2010-10-01';
  $end = date('Y-m-d');

  if ($period == 'month') {
    $start = checkQuarter()['this_month_start'];
    $end = checkQuarter()['this_month_end'];
  }

  if ($period == 'quarter') {
    $start = checkQuarter()['this_quarter_start'];
    $end = checkQuarter()['this_quarter_end'];
  }

  if ($period == 'fy2021') {
    $start = checkQuarter('2021-01-01')['q1_start'];
    $end = checkQuarter('2021-01-01')['q4_end'];
  }

  if ($period == 'fy2022') {
    $start = checkQuarter('2022-01-01')['q1_start'];
    $end = checkQuarter('2022-01-01')['q4_end'];
  }

  if ($period == 'all') {
    $start = checkQuarter('2014-01-01')['q1_start'];
    $end = checkQuarter()['q4_end'];
  }

  if ($get_start && $get_end) {
    $start = $get_start;
    $end = $get_end;
  }

  $data = [$start, $end];

  $stmt = $conn->prepare("SELECT * FROM clinicnewcase WHERE created_at >= ? and created_at < ? order by id DESC");

  $stmt->execute($data);

  return $stmt;
}

function fetchPrEPInfo($uic)
{
  global $conn;

  $data = [$uic];

  $stmt = $conn->prepare("SELECT a.*, b.*, c.prep_left, c.recieveForSex
  FROM prep_profile a 
  LEFT JOIN clinic b ON a.uic = b.uic AND b.sn = (
            SELECT MAX(sn) 
            FROM clinic c 
            WHERE c.uic = a.uic)
  LEFT JOIN clinicprecounseling c ON b.pre_id = c.id
  LEFT JOIN clinicpostcounseling d ON b.post_id = d.id
  WHERE a.uic = ?");
  $stmt->execute($data);

  return $stmt;
}

function sdart_stock_lists()
{
  global $conn;

  $data = [];

  $stmt = $conn->prepare("SELECT * FROM prep_stock WHERE purpose in ('ARV')");
  $stmt->execute($data);

  return $stmt;
}

function pep_stock_lists()
{
  global $conn;

  $data = [];

  $stmt = $conn->prepare("SELECT * FROM prep_stock WHERE purpose in ('nPEP', 'oPEP')");
  $stmt->execute($data);

  return $stmt;
}

function prep_stock_lists($conn, $startDate = NULL, $endDate = NULL)
{

  $where = "WHERE purpose in ('PrEP')";
  $data = [];

  if ($startDate && $endDate) {
    $where = "WHERE purpose in ('PrEP') AND received_date >= ? AND received_date < ?";
    $data = [$startDate, $endDate];
  }

  $stmt = $conn->prepare("SELECT * FROM prep_stock {$where}");
  $stmt->execute($data);

  return $stmt;
}

function prep_stock_spending($conn, $section = '', $uic = null, $filter = '2014-10-01', $startDate = NULL, $endDate = NULL, $source_id = NULL)
{

  if (!$filter) $filter = '2014-10-01';

  $data = [$filter];

  $where_cause = "WHERE a.prep_distribution in ('จ่าย PrEP', 'ยังไม่จ่าย PrEP') AND a.service_date >= ?";

  if ($section == 'enter_nap' || $section == 'enter_lab') $where_cause = "WHERE a.prep_distribution in ('จ่าย PrEP', 'ยังไม่จ่าย PrEP') AND a.service_date >= ?";

  if ($uic) {
    $where_cause = "WHERE a.prep_distribution in ('จ่าย PrEP', 'ยังไม่จ่าย PrEP') AND a.uic = ? AND a.service_date >= ?";
    $data = [$uic, $filter];

    if ($source_id) {
      $where_cause = "WHERE a.prep_distribution in ('จ่าย PrEP', 'ยังไม่จ่าย PrEP') AND a.uic = ? AND a.sn = ? AND a.service_date >= ?";
      $data = [$uic, $source_id, $filter];
    }
  }

  if ($startDate && $endDate) {
    $where_cause = "WHERE a.prep_distribution in ('จ่าย PrEP', 'ยังไม่จ่าย PrEP') AND a.service_date >= ? AND a.service_date < ?";
    $data = [$startDate, $endDate];
  }

  $query = "SELECT 
  a.sn, 
  a.clinic_id, 
  a.q, 
  a.bangrak_hn, 
  a.bangrak_date, 
  a.service_date, 
  a.hiv_result, 
  a.sti_result, 
  a.hcv_result, 
  a.prep, 
  a.uic, 
  a.firstname, 
  a.lastname, 
  a.phone, 
  a.sex, 
  a.id_card, 
  a.prep_med_type, 
  a.prep_med_amount, 
  a.counselor, 
  a.prep_distribution,
  a.prep_no_distribution_reason,
  b.prep_comment, b.prep_set_visit, b.nextFuDate, b.condomDistribute, b.lubricantDistribute, b.client_signature,
  c.prep_today_route, 
  c.prep_screening, 
  c.prep_service_visit, 
  c.prep_service_method, 
  c.prep_taken_check, 
  c.prep_service_step, 
  c.serviceProvide,
  d.prep_nap_code, 
  e.input_id, e.nap_status, e.nap_code, e.nap_staff, e.nap_date, e.nap_comment, e.lab_nap_creatinine_code, e.lab_nap_hbsag_code, e.lab_nap_status, e.lab_nap_staff, e.lab_nap_date, e.lab_nap_comment,
  f.lab_creatinine_result, f.lab_egfr_result, f.lab_hbsag_result, f.lab_file_upload, f.hiv_file_upload, f.creatinine_file_upload, f.hbsag_file_upload, f.lab_status, f.lab_staff, f.lab_date, f.lab_comment,
  g.extra_blood, g.extra_blood_services, g.hiv_result, g.reported_at as hiv_date,

  g.hbsag_result as clinic_hbsag_result,
  g.creatinine_result as clinic_creatinine_result,
  g.egfr_result as clinic_egfr_result,
  g.alt_result as clinic_alt_result,

  h.type, h.source, h.source_id, h.hospital_hn, h.request_cbs, h.prep_approve_location, h.request_date, h.prep_approve_signature, h.prep_approve_date, h.prep_approve_doctor, h.update_status, h.update_reference, h.update_staff, h.cbo_request_approve_comment, h.cbo_request_bloodtest_comment, h.hospital_addlab_comment, h.hospital_request_approve_comment, h.hospital_approve_comment, h.hospital_not_approve_comment, h.update_date, h.created_at,
  i.esig_date,
  nc.healthcare,
  nc.hospital,
  nc.weight,
  nc.height,	
  nc.pressure,	
  nc.heartrate,	
  nc.temperature,	
  nc.nationality,	
  nc.occupation,	
  nc.education,	
  nc.address1,	
  nc.tambol,	
  nc.ampher,	
  nc.province,	
  nc.postcode,
  ta.district_code,
  ta.amphoe_code,
  ta.province_code

  FROM clinic a
  LEFT JOIN prep_profile d ON d.uic = a.uic
  LEFT JOIN prep_record_nap e ON e.input_id = concat('PrEP_Clinic_', a.sn,'_',a.uic)
  LEFT JOIN prep_record_lab f ON f.input_id = concat('PrEP_Clinic_', a.sn,'_',a.uic)
  LEFT JOIN prep_hospital_request h ON h.input_id = concat('PrEP_Clinic_', a.sn,'_',a.uic)
  LEFT JOIN clinicnewcase nc ON a.newcase_id = nc.id
  LEFT JOIN thai_address ta ON nc.tambol = ta.district and nc.ampher = ta.amphoe and nc.province = ta.province
  LEFT JOIN clinicpostcounseling b ON a.post_id = b.id
  LEFT JOIN clinicprecounseling c ON a.pre_id = c.id
  LEFT JOIN cliniclabresult g ON a.lab_id = g.id
  LEFT JOIN esignature i ON i.reference_code = b.client_signature 
  {$where_cause} group by a.sn
  ";

  $stmt = $conn->prepare($query);
  $stmt->execute($data);

  return $stmt;
}

function npep_stock_spending($section = '', $uic = null, $filter = '2014-10-01', $startDate = NULL, $endDate = NULL)
{
  global $conn;

  if (!$filter) $filter = '2014-10-01';

  $data = [$filter];

  $where_cause = "WHERE (a.npep_med_1_amount != '' AND a.npep_med_1_amount is not NULL) AND (a.npep_med_2_amount != '' AND a.npep_med_2_amount is not NULL) AND a.service_date >= ?";

  if ($section == 'enter_nap' || $section == 'enter_lab') $where_cause = "WHERE a.npep_distribution in ('จ่าย nPEP', 'ยังไม่จ่าย nPEP') AND a.service_date >= ?";

  if ($uic) {
    $where_cause = "WHERE a.npep_distribution in ('จ่าย nPEP', 'ไม่จ่าย nPEP') AND a.uic = ? AND a.service_date >= ?";
    $data = [$uic, $filter];
  }

  if ($startDate && $endDate) {
    $where_cause = "WHERE a.npep_distribution in ('จ่าย nPEP', 'ไม่จ่าย nPEP') AND a.service_date >= ? a.service_date < ?";
    $data = [$startDate, $endDate];
  }

  $query = "SELECT 
    a.*,
    a.id as npep_post_id,
    a.cbs as npep_cbs, 
    b.*,
    c.*,
    d.*,

    a.id,

    f.lab_creatinine_result, 
    f.lab_egfr_result, 
    f.lab_hbsag_result, 
    f.lab_alt_result, 
    f.lab_file_upload, 
    f.lab_status, 
    f.lab_staff, 
    f.lab_date, 
    f.lab_comment,

    g.extra_blood, 
    g.extra_blood_services, 
    g.hiv_result, 
    g.reported_at as hiv_date,

    h.type, 
    h.source, 
    h.source_id, 
    h.input_id, 
    h.hospital_hn, 
    h.request_cbs, 
    h.request_date, 
    h.npep_approve_signature, 
    h.npep_approve_date, 
    h.npep_approve_doctor, 
    h.update_status, 
    h.update_staff, 
    h.cbo_request_approve_comment, 
    h.cbo_request_bloodtest_comment, 
    h.hospital_addlab_comment, 
    h.hospital_request_approve_comment, 
    h.hospital_approve_comment, 
    h.hospital_not_approve_comment, 
    h.update_reference, 
    h.update_date, 
    h.created_at,

    i.esig_code
  from npep_postcounseling a
  LEFT JOIN npep_profile d ON d.uic = a.uic
  LEFT JOIN npep_record_lab f ON f.input_id = concat('nPEP_Clinic_', a.id,'_',a.uic)
  LEFT JOIN npep_record_nap e ON e.input_id = concat('nPEP_Clinic_', a.id,'_',a.uic)
  LEFT JOIN npep_hospital_request h ON h.input_id = concat('nPEP_Clinic_', a.id,'_',a.uic)
  LEFT JOIN clinicpostcounseling b ON a.clinic_id = b.clinic_id and a.queue = b.queue 
  LEFT JOIN clinicprecounseling c ON a.clinic_id = c.clinic_id and a.queue = c.queue 
  LEFT JOIN cliniclabresult g ON a.clinic_id = g.clinic_id and a.queue = g.queue 
  LEFT JOIN esignature i ON i.reference_code = a.npep_client_signature {$where_cause} group by a.id
  ";

  $stmt = $conn->prepare($query);
  $stmt->execute($data);

  return $stmt;
}

function sdart_stock_spending($section = '', $uic = null, $filter = '2014-10-01')
{
  global $conn;

  if (!$filter) $filter = '2014-10-01';

  $data = [$filter];

  $where_cause = "WHERE a.hivSameDay = 'sameday' AND a.hivSameDayDate >= ?";

  if ($uic) {
    $where_cause = "WHERE a.uic = ? AND a.hivSameDay = 'sameday' AND a.hivSameDayDate >= ?";
    $data = [$uic, $filter];
  }

  $query = "SELECT 
    a.*,

    h.type, 
    h.source, 
    h.source_id, 
    h.input_id, 
    h.hospital_hn, 
    h.request_cbs, 
    h.request_date, 
    h.sdart_approve_signature, 
    h.sdart_approve_date, 
    h.sdart_approve_doctor, 
    h.update_status, 
    h.update_staff, 
    h.cbo_request_approve_comment, 
    h.cbo_request_bloodtest_comment, 
    h.hospital_addlab_comment, 
    h.hospital_request_approve_comment, 
    h.hospital_approve_comment, 
    h.hospital_not_approve_comment, 
    h.update_reference, 
    h.update_date, 
    h.created_at

  from caresupportdatabase a
  LEFT JOIN sdart_hospital_request h ON h.input_id = concat('SDART_', a.id,'_',a.uic)
  {$where_cause} group by a.id
  ";

  $stmt = $conn->prepare($query);
  $stmt->execute($data);

  return $stmt;
}

function prep_refill_spending($conn, $uic = '', $startDate = NULL, $endDate = NULL)
{

  $data = [];
  $where_uic = "";

  if ($uic) {
    $data = [$uic];
    $where_uic = "WHERE a.uic = ?";
  }

  if ($startDate && $endDate) {
    $where_uic = "WHERE a.service_date >= ? AND a.service_date < ?";
    $data = [$startDate, $endDate];
  }

  $query = "SELECT 
  
  a.*, 
  
  b.prep_nap_code, 
  b.kp, 
  b.id_card, 
  b.last_prep_method, 
  b.firstname, 
  b.lastname, 
  b.phone,
  
  c.hiv_result, 
  c.sti_result, 
  c.hcv_result, 
  c.service_date as hiv_date,

  d.condomDistribute, 
  d.lubricantDistribute,
  
  e.input_id, 
  e.nap_status, 
  e.nap_code, 
  e.nap_staff, 
  e.nap_date, 
  e.nap_comment,
  
  h.type, 
  h.source, 
  h.source_id, 
  h.hospital_hn, 
  h.request_cbs, 
  h.request_date, 
  h.prep_approve_location,
  h.prep_approve_signature, 
  h.prep_approve_date, 
  h.prep_approve_doctor, 
  h.update_status, 
  h.update_reference, 
  h.update_staff, 
  h.cbo_request_approve_comment, 
  h.cbo_request_bloodtest_comment, 
  h.hospital_addlab_comment, 
  h.hospital_request_approve_comment, 
  h.hospital_approve_comment, 
  h.hospital_not_approve_comment, 
  h.update_date, 
  h.created_at,

  nc.healthcare,
  nc.hospital,

  i.esig_code, i.esig_date

    from prep_spending a
    LEFT JOIN prep_profile b ON b.uic = a.uic
    LEFT JOIN clinic c ON c.uic = a.uic AND c.sn = (SELECT max(sn) from clinic where uic = c.uic)
    LEFT JOIN clinicpostcounseling d ON d.clinic_id = c.clinic_id and d.queue = c.q 
    LEFT JOIN clinicnewcase nc ON nc.clinic_id = c.clinic_id and nc.queue = c.q 
    LEFT JOIN prep_record_nap e ON e.input_id = concat('PrEP_Refill_', a.id,'_',a.uic)
    LEFT JOIN prep_hospital_request h ON h.input_id = concat('PrEP_Refill_', a.id,'_',a.uic)
    LEFT JOIN esignature i ON i.reference_code = a.client_signature 
    {$where_uic} ORDER by a.id
  ";

  $stmt = $conn->prepare($query);
  $stmt->execute($data);

  return $stmt;
}

function checkPrEPPre($condition)
{
  global $conn;

  $data = [$condition['clinic_id'], implode(',', $condition['queue'])];

  $stmt = $conn->prepare("SELECT id, uic, queue,prep_taken_check,	prep_taken_route,	prep_location,	prep_code,	prep_provide,	prep_no_provide_reason,prep_not_pass_criteria,prep_today_route,	prep_service_step,	prep_service_visit,	prep_service_method,	prep_service_express,	prep_adherence,	prep_check_consent from clinicprecounseling where clinic_id = $data[0] and queue in ($data[1])");
  $stmt->execute();

  return $stmt;
}

function checkPrEPLab($condition)
{
  global $conn;

  $data = [$condition['clinic_id'], implode(',', $condition['queue'])];

  $stmt = $conn->prepare("SELECT id, uic, queue, extra_blood, extra_blood_services from cliniclabresult where clinic_id = $data[0] and queue in ($data[1])");
  $stmt->execute();

  return $stmt;
}

function checkPrEPPost($condition)
{
  global $conn;

  $data = [$condition['clinic_id'], implode(',', $condition['queue'])];

  $stmt = $conn->prepare("SELECT id, uic, queue, prep_distribution, prep_no_distribution_reason, prep_code, prep_med_type, prep_med_amount, prep_set_visit, prep_comment from clinicpostcounseling where clinic_id = $data[0] and queue in ($data[1])");
  $stmt->execute();

  return $stmt;
}

function checkPrEPRefill($date)
{
  global $conn;

  $data = [$date];

  $stmt = $conn->prepare("SELECT * from prep_spending where date(created_at) = ?");
  $stmt->execute($data);

  return $stmt;
}

function prepStockLists($conn, $active = false, $lists_stock = false)
{

  $is_active = '';
  $lists_stock_condition = '';

  if ($active) $is_active = "AND a.active = 1";

  if ($lists_stock) $lists_stock_condition = "AND a.purpose = 'PrEP'";

  $query = "SELECT

  a.id,
  a.lot_number,
  a.stock_method,
  a.med,
  a.purpose,
  a.prefix,
  a.expiration_date,
  a.received_date,
  a.received_value,
  a.received_from,
  a.evidence_files,
  a.active,
  a.title,
  a.created_at,

  d.lot_number as stock_lot, sum(d.sum) as stock_spend,
  (a.received_value - coalesce(sum(d.sum), 0)) as stock_left

  FROM prep_stock a 
  LEFT JOIN (SELECT id, prep_med_type, sum(prep_med_amount) as sum
    FROM `clinicpostcounseling`
    WHERE prep_med_type != ''
    group by prep_med_type
    order by sum, id) b ON b.prep_med_type = concat(a.med, '_', a.lot_number, ':', a.id)
  LEFT JOIN (SELECT id, lot_number, stock_method, sum(received_value) as sum
    FROM `prep_stock`
    WHERE stock_method = 'จ่ายออก'
    group by lot_number
    order by sum, id) d ON d.lot_number = concat(a.lot_number, ':', a.id)
  WHERE a.stock_method = 'รับเข้า' {$lists_stock_condition} {$is_active}
  group by a.id";

  $stmt = $conn->prepare($query);
  $stmt->execute();

  $prep_stock = $stmt->fetchall();

  // prep refill
  $stmt = $conn->prepare("SELECT id, stock_lot, value
    FROM `prep_spending`
    order by id");

  $stmt->execute();
  $data = $stmt->fetchall();

  $refill_arr = [];

  foreach ($data as $key => $item) {

    $stock = json_decode($item->stock_lot);
    $value = json_decode($item->value);

    foreach ($stock as $s_k => $s_v) {
      if (array_key_exists($s_v, $refill_arr)) {
        $refill_arr[$s_v] = $refill_arr[$s_v] + $value[$s_k];
      } else {
        $refill_arr[$s_v] = $value[$s_k];
      }
    }
  }
  // prep refill

  // prep clinic
  $stmt = $conn->prepare("SELECT id, prep_med_type, prep_med_amount FROM `clinicpostcounseling` WHERE prep_med_type != '' order by id");

  $stmt->execute();
  $clinic_data = $stmt->fetchall(PDO::FETCH_ASSOC);

  $clinic_arr = [];

  $map_data = array_map(function ($aaa) {
    $response = [];

    $response['id'] = $aaa['id'];
    $response['stock_lot'] = $aaa['prep_med_type'];
    $response['value'] = $aaa['prep_med_amount'];
    return $response;
  }, $clinic_data);

  $clinic_arr = [];

  foreach ($map_data as $key => $item) {

    $item = (object) $item;

    $stock = json_decode($item->stock_lot);
    $value = json_decode($item->value);

    foreach ($stock as $s_k => $s_v) {

      $stock_lot = explode("_", $s_v)[1] ?? $s_v;

      if (array_key_exists($stock_lot, $clinic_arr)) {
        $clinic_arr[$stock_lot] = $clinic_arr[$stock_lot] + $value[$s_k];
      } else {
        $clinic_arr[$stock_lot] = $value[$s_k];
      }
    }
  }
  // prep clinic

  // $refill_arr
  // $clinic_arr

  $stock_lists = [];

  foreach ($prep_stock as $stock) {

    // image
    $evidence = "";
    $evidence_files = json_decode($stock->evidence_files);
    $img_count = count($evidence_files);
    foreach ($evidence_files as $key => $img) {

      $show = "";
      if ($key > 0) $show = "hidden";

      $image_url = __DIR__ . "/../prep/{$img}";

      $evidence .= "<a class='$show' data-fancybox='{$stock->lot_number}:{$stock->id}' data-src='../showImage.php?file={$image_url}' data-caption='{$stock->lot_number}:{$stock->id}'><label class='label label-info' style='font-size: 1.2rem; cursor: pointer;'>$img_count <i class='fa fa-file-image-o' aria-hidden='true'></i></label></a>";
    }
    // image

    // (int)$stock->refill_spend + (int)$stock->clinic_spend + 
    $stock_name = $stock->lot_number . ":" . $stock->id;
    if ($stock->stock_method == 'จ่ายออก') $stock_name = $stock->lot_number;

    if (!array_key_exists($stock_name, $stock_lists)) {
      $stock_lists[$stock_name] = [
        'id' => $stock->id,
        'stock_method' => $stock->stock_method,
        'purpose' => $stock->purpose,
        'prefix' => $stock->prefix,
        'stock_name' => $stock_name,
        'lot' => $stock->lot_number,
        'evidence_files' => $evidence,
        'total' => (int)$stock->received_value,
        'spend' => (int)$stock->stock_spend,
        'left' => (int)$stock->stock_left,
        'med' => $stock->med,
        'from' => $stock->received_from,
        'expire' => $stock->expiration_date,
        'active' => $stock->active,
        'title' => $stock->title,
        'created_at' => $stock->created_at,
        'received_date' => dateOnly($stock->received_date)
      ];
      if (isset($refill_arr[$stock_name])) {
        $stock_lists[$stock_name]["spend"] = $stock_lists[$stock_name]["spend"] + $refill_arr[$stock_name];
        $stock_lists[$stock_name]["left"] = $stock_lists[$stock_name]["left"] - $refill_arr[$stock_name];
      }
      if (isset($clinic_arr[$stock_name])) {
        $stock_lists[$stock_name]["spend"] = $stock_lists[$stock_name]["spend"] + $clinic_arr[$stock_name];
        $stock_lists[$stock_name]["left"] = $stock_lists[$stock_name]["left"] - $clinic_arr[$stock_name];
      }
    }
  }

  return $stock_lists;
}

function npepStockLists($active = false)
{
  global $conn;

  $is_active = '';

  if ($active) $is_active = "AND a.active = 1";

  $query = "SELECT

  a.id,
  a.lot_number,
  a.stock_method,
  a.med,
  a.purpose,
  a.prefix,
  a.expiration_date,
  a.received_date,
  a.received_value,
  a.received_from,
  a.evidence_files,
  a.active,
  a.title,
  a.created_at,

  d.lot_number as stock_lot, sum(d.sum) as stock_spend,
  (a.received_value - coalesce(sum(d.sum), 0)) as stock_left

  FROM prep_stock a 
  LEFT JOIN (SELECT id, prep_med_type, sum(prep_med_amount) as sum
    FROM `clinicpostcounseling`
    WHERE prep_med_type != ''
    group by prep_med_type
    order by sum, id) b ON b.prep_med_type = concat(a.med, '_', a.lot_number, ':', a.id)
  LEFT JOIN (SELECT id, lot_number, stock_method, sum(received_value) as sum
    FROM `prep_stock`
    WHERE stock_method = 'จ่ายออก'
    group by lot_number
    order by sum, id) d ON d.lot_number = concat(a.lot_number, ':', a.id)
  WHERE a.stock_method = 'รับเข้า' AND a.purpose in ('nPEP', 'oPEP') {$is_active}
  group by a.id";

  $stmt = $conn->prepare($query);
  $stmt->execute();

  $prep_stock = $stmt->fetchall();

  // prep refill
  $stmt = $conn->prepare("SELECT id, stock_lot, value
    FROM `prep_spending`
    order by id");

  $stmt->execute();
  $data = $stmt->fetchall();

  $refill_arr = [];

  foreach ($data as $key => $item) {

    $stock = json_decode($item->stock_lot);
    $value = json_decode($item->value);

    foreach ($stock as $s_k => $s_v) {
      if (array_key_exists($s_v, $refill_arr)) {
        $refill_arr[$s_v] = $refill_arr[$s_v] + $value[$s_k];
      } else {
        $refill_arr[$s_v] = $value[$s_k];
      }
    }
  }
  // prep refill

  // prep clinic
  $stmt = $conn->prepare("SELECT id, npep_med_type, npep_med_amount FROM `npep_postcounseling` WHERE npep_med_type != '' order by id");

  $stmt->execute();
  $clinic_data = $stmt->fetchall(PDO::FETCH_ASSOC);

  $clinic_arr = [];

  $map_data = array_map(function ($aaa) {
    $response = [];

    $response['id'] = $aaa['id'];
    $response['stock_lot'] = $aaa['npep_med_type'];
    $response['value'] = $aaa['npep_med_amount'];
    return $response;
  }, $clinic_data);

  $clinic_arr = [];

  foreach ($map_data as $key => $item) {

    $item = (object) $item;

    $stock = json_decode($item->stock_lot);
    $value = json_decode($item->value);

    foreach ($stock as $s_k => $s_v) {

      $stock_lot = explode("_", $s_v)[1] ?? $s_v;

      if (array_key_exists($stock_lot, $clinic_arr)) {
        $clinic_arr[$stock_lot] = $clinic_arr[$stock_lot] + $value[$s_k];
      } else {
        $clinic_arr[$stock_lot] = $value[$s_k];
      }
    }
  }
  // prep clinic

  // $refill_arr
  // $clinic_arr

  $stock_lists = [];

  foreach ($prep_stock as $stock) {

    // image
    $evidence = "";
    $evidence_files = json_decode($stock->evidence_files);
    $img_count = count($evidence_files);
    foreach ($evidence_files as $key => $img) {

      $show = "";
      if ($key > 0) $show = "hidden";

      $image_url = __DIR__ . "/../prep/{$img}";

      $evidence .= "<a class='$show' data-fancybox='{$stock->lot_number}:{$stock->id}' data-src='../showImage.php?file={$image_url}' data-caption='{$stock->lot_number}:{$stock->id}'><label class='label label-info' style='font-size: 1.2rem; cursor: pointer;'>$img_count <i class='fa fa-file-image-o' aria-hidden='true'></i></label></a>";
    }
    // image

    // (int)$stock->refill_spend + (int)$stock->clinic_spend + 
    $stock_name = $stock->lot_number . ":" . $stock->id;
    if ($stock->stock_method == 'จ่ายออก') $stock_name = $stock->lot_number;

    if (!array_key_exists($stock_name, $stock_lists)) {
      $stock_lists[$stock_name] = [
        'id' => $stock->id,
        'stock_method' => $stock->stock_method,
        'purpose' => $stock->purpose,
        'prefix' => $stock->prefix,
        'stock_name' => $stock_name,
        'lot' => $stock->lot_number,
        'evidence_files' => $evidence,
        'total' => (int)$stock->received_value,
        'spend' => (int)$stock->stock_spend,
        'left' => (int)$stock->stock_left,
        'med' => $stock->med,
        'from' => $stock->received_from,
        'expire' => $stock->expiration_date,
        'active' => $stock->active,
        'title' => $stock->title,
        'created_at' => $stock->created_at,
        'received_date' => dateOnly($stock->received_date)
      ];
      if (isset($refill_arr[$stock_name])) {
        $stock_lists[$stock_name]["spend"] = $stock_lists[$stock_name]["spend"] + $refill_arr[$stock_name];
        $stock_lists[$stock_name]["left"] = $stock_lists[$stock_name]["left"] - $refill_arr[$stock_name];
      }
      if (isset($clinic_arr[$stock_name])) {
        $stock_lists[$stock_name]["spend"] = $stock_lists[$stock_name]["spend"] + $clinic_arr[$stock_name];
        $stock_lists[$stock_name]["left"] = $stock_lists[$stock_name]["left"] - $clinic_arr[$stock_name];
      }
    }
  }

  return $stock_lists;
}

function sdartStockLists($active = false)
{
  global $conn;

  $is_active = '';

  if ($active) $is_active = "AND a.active = 1";

  $query = "SELECT

  a.id,
  a.lot_number,
  a.stock_method,
  a.med,
  a.purpose,
  a.prefix,
  a.expiration_date,
  a.received_date,
  a.received_value,
  a.received_from,
  a.evidence_files,
  a.active,
  a.title,
  a.created_at,

  d.lot_number as stock_lot, sum(d.sum) as stock_spend,
  (a.received_value - coalesce(sum(d.sum), 0)) as stock_left

  FROM prep_stock a 
  LEFT JOIN (SELECT id, prep_med_type, sum(prep_med_amount) as sum
    FROM `clinicpostcounseling`
    WHERE prep_med_type != ''
    group by prep_med_type
    order by sum, id) b ON b.prep_med_type = concat(a.med, '_', a.lot_number, ':', a.id)
  LEFT JOIN (SELECT id, lot_number, stock_method, sum(received_value) as sum
    FROM `prep_stock`
    WHERE stock_method = 'จ่ายออก'
    group by lot_number
    order by sum, id) d ON d.lot_number = concat(a.lot_number, ':', a.id)
  WHERE a.stock_method = 'รับเข้า' AND a.purpose = 'ARV' {$is_active}
  group by a.id";

  $stmt = $conn->prepare($query);
  $stmt->execute();

  $prep_stock = $stmt->fetchall();

  // prep refill
  $stmt = $conn->prepare("SELECT id, stock_lot, value
    FROM `prep_spending`
    order by id");

  $stmt->execute();
  $data = $stmt->fetchall();

  $refill_arr = [];

  foreach ($data as $key => $item) {

    $stock = json_decode($item->stock_lot);
    $value = json_decode($item->value);

    foreach ($stock as $s_k => $s_v) {
      if (array_key_exists($s_v, $refill_arr)) {
        $refill_arr[$s_v] = $refill_arr[$s_v] + $value[$s_k];
      } else {
        $refill_arr[$s_v] = $value[$s_k];
      }
    }
  }
  // prep refill

  // prep clinic
  $stmt = $conn->prepare("SELECT id, npep_med_type, npep_med_amount FROM `npep_postcounseling` WHERE npep_med_type != '' order by id");

  $stmt->execute();
  $clinic_data = $stmt->fetchall(PDO::FETCH_ASSOC);

  $clinic_arr = [];

  $map_data = array_map(function ($aaa) {
    $response = [];

    $response['id'] = $aaa['id'];
    $response['stock_lot'] = $aaa['npep_med_type'];
    $response['value'] = $aaa['npep_med_amount'];
    return $response;
  }, $clinic_data);

  $clinic_arr = [];

  foreach ($map_data as $key => $item) {

    $item = (object) $item;

    $stock = json_decode($item->stock_lot);
    $value = json_decode($item->value);

    foreach ($stock as $s_k => $s_v) {

      $stock_lot = explode("_", $s_v)[1] ?? $s_v;

      if (array_key_exists($stock_lot, $clinic_arr)) {
        $clinic_arr[$stock_lot] = $clinic_arr[$stock_lot] + $value[$s_k];
      } else {
        $clinic_arr[$stock_lot] = $value[$s_k];
      }
    }
  }
  // prep clinic

  // $refill_arr
  // $clinic_arr

  $stock_lists = [];

  foreach ($prep_stock as $stock) {

    // image
    $evidence = "";
    $evidence_files = json_decode($stock->evidence_files);
    $img_count = count($evidence_files);
    foreach ($evidence_files as $key => $img) {

      $show = "";
      if ($key > 0) $show = "hidden";

      $image_url = __DIR__ . "/../prep/{$img}";

      $evidence .= "<a class='$show' data-fancybox='{$stock->lot_number}:{$stock->id}' data-src='../showImage.php?file={$image_url}' data-caption='{$stock->lot_number}:{$stock->id}'><label class='label label-info' style='font-size: 1.2rem; cursor: pointer;'>$img_count <i class='fa fa-file-image-o' aria-hidden='true'></i></label></a>";
    }
    // image

    // (int)$stock->refill_spend + (int)$stock->clinic_spend + 
    $stock_name = $stock->lot_number . ":" . $stock->id;
    if ($stock->stock_method == 'จ่ายออก') $stock_name = $stock->lot_number;

    if (!array_key_exists($stock_name, $stock_lists)) {
      $stock_lists[$stock_name] = [
        'id' => $stock->id,
        'stock_method' => $stock->stock_method,
        'purpose' => $stock->purpose,
        'prefix' => $stock->prefix,
        'stock_name' => $stock_name,
        'lot' => $stock->lot_number,
        'evidence_files' => $evidence,
        'total' => (int)$stock->received_value,
        'spend' => (int)$stock->stock_spend,
        'left' => (int)$stock->stock_left,
        'med' => $stock->med,
        'from' => $stock->received_from,
        'expire' => $stock->expiration_date,
        'active' => $stock->active,
        'title' => $stock->title,
        'created_at' => $stock->created_at,
        'received_date' => dateOnly($stock->received_date)
      ];
      if (isset($refill_arr[$stock_name])) {
        $stock_lists[$stock_name]["spend"] = $stock_lists[$stock_name]["spend"] + $refill_arr[$stock_name];
        $stock_lists[$stock_name]["left"] = $stock_lists[$stock_name]["left"] - $refill_arr[$stock_name];
      }
      if (isset($clinic_arr[$stock_name])) {
        $stock_lists[$stock_name]["spend"] = $stock_lists[$stock_name]["spend"] + $clinic_arr[$stock_name];
        $stock_lists[$stock_name]["left"] = $stock_lists[$stock_name]["left"] - $clinic_arr[$stock_name];
      }
    }
  }

  return $stock_lists;
}

function checkQuarter($date = '')
{
  if ($date == '') $date = date('Y-m-d');

  $Q1 = [10, 11, 12];
  $Q2 = [1, 2, 3];
  $Q3 = [4, 5, 6];
  $Q4 = [7, 8, 9];

  $split = explode('-', $date);

  $response = [];

  $response['this_fy'] = $split[0];

  $response['this_month_start'] = $split[0] . '-' . $split[1] . '-01';
  $response['this_month_end'] = $split[0] . '-' . sprintf("%02d", ($split[1] + 1)) . '-01';

  if (in_array($split[1], $Q2)) {
    $response['this_quarter'] = 2;
    $response['this_quarter_start'] = $split[0] . '-01-01';
    $response['this_quarter_end'] = $split[0] . '-04-01';
  }
  if (in_array($split[1], $Q3)) {
    $response['this_quarter'] = 3;
    $response['this_quarter_start'] = $split[0] . '-04-01';
    $response['this_quarter_end'] = $split[0] . '-07-01';
  }
  if (in_array($split[1], $Q4)) {
    $response['this_quarter'] = 4;
    $response['this_quarter_start'] = $split[0] . '-07-01';
    $response['this_quarter_end'] = $split[0] . '-10-01';
  }

  $response['q1_start'] = ($split[0] - 1) . '-10-01';
  $response['q1_end'] = $split[0] . '-01-01';
  $response['q2_start'] = $split[0] . '-01-01';
  $response['q2_end'] = $split[0] . '-04-01';
  $response['q3_start'] = $split[0] . '-04-01';
  $response['q3_end'] = $split[0] . '-07-01';
  $response['q4_start'] = $split[0] . '-07-01';
  $response['q4_end'] = $split[0] . '-10-01';

  if (in_array($split[1], $Q1)) {

    $response['this_fy'] = $split[0] + 1;
    $response['this_quarter'] = 1;
    $response['this_quarter_start'] = $split[0] . '-10-01';
    $response['this_quarter_end'] = ($split[0] + 1) . '-01-01';
    $response['q1_start'] = $split[0] . '-10-01';
    $response['q1_end'] = ($split[0] + 1) . '-01-01';
    $response['q2_start'] = ($split[0] + 1) . '-01-01';
    $response['q2_end'] = ($split[0] + 1) . '-04-01';
    $response['q3_start'] = ($split[0] + 1) . '-04-01';
    $response['q3_end'] = ($split[0] + 1) . '-07-01';
    $response['q4_start'] = ($split[0] + 1) . '-07-01';
    $response['q4_end'] = ($split[0] + 1) . '-10-01';
  }

  if ($split[1] == 12) $response['this_month_end'] = ($split[0] + 1) . '-01-01';


  return $response;
}

function thaiDateToDateTime($thaiDate)
{
  // แยกวัน/เดือน/ปี
  [$day, $month, $year] = explode('/', $thaiDate);

  // แปลงเดือนภาษาไทย
  $months = [
    "ม.ค." => 1,
    "ก.พ." => 2,
    "มี.ค." => 3,
    "เม.ย." => 4,
    "พ.ค." => 5,
    "มิ.ย." => 6,
    "ก.ค." => 7,
    "ส.ค." => 8,
    "ก.ย." => 9,
    "ต.ค." => 10,
    "พ.ย." => 11,
    "ธ.ค." => 12
  ];

  // บางครั้งเดือนมาเป็นตัวเลขแล้ว เช่น "04/11/2567"
  $m = isset($months[$month]) ? $months[$month] : intval($month);

  // ปีพ.ศ. → ค.ศ.
  $year = intval($year) - 543;

  return new DateTime(sprintf("%04d-%02d-%02d", $year, $m, $day));
}

function resultColor($d)
{

  $color = 'default';

  if ($d == 'R' || $d == 'P') $color = 'danger';
  if ($d == 'I') $color = 'warning';
  if ($d == 'N') $color = 'success';
  if ($d == '-') $color = 'black';

  return $color;
}

function fetchSiteSpecific()
{

  global $conn;

  $stmt = $conn->prepare("SELECT * FROM site_specific limit 1");
  $stmt->execute();

  return $stmt->fetch();
}

function fetchLastReferAndRetain()
{

  global $conn;

  $stmt = $conn->prepare("SELECT uic FROM caresupportdatabase");
  $stmt->execute();

  $care_uic = $stmt->fetchall();

  $uicList = [];

  foreach ($care_uic as $item) {
    $uicLists[] = $item->uic;
  }

  // dump($uicLists);

  // fetch last record of refer and retain case and sort by date select only first uic found

  $stmt = $conn->prepare("SELECT id, referDate, referor, topic, uic FROM refertotreat order by referDate desc");
  $stmt->execute();

  $referData = $stmt->fetchall();

  $referList = [];

  foreach ($referData as $item) {

    if (!array_key_exists($item->uic, $referList)) {
      $referList[$item->uic][] = $item->id;
      $referList[$item->uic][] = 'Refer';
      $referList[$item->uic][] = $item->uic;
      $referList[$item->uic][] = $item->topic;
      $referList[$item->uic][] = $item->referor;
      $referList[$item->uic][] = $item->referDate;
    }
  }

  // dump($referData);
  // dump($referList);

  $stmt = $conn->prepare("SELECT id, retainDate, cbs, topic, uic FROM retaincare order by retainDate desc");
  $stmt->execute();

  $retainData = $stmt->fetchall();

  $retainList = [];

  foreach ($retainData as $item) {

    if (!array_key_exists($item->uic, $retainList)) {
      $retainList[$item->uic][] = $item->id;
      $retainList[$item->uic][] = 'Refer';
      $retainList[$item->uic][] = $item->uic;
      $retainList[$item->uic][] = $item->topic;
      $retainList[$item->uic][] = $item->cbs;
      $retainList[$item->uic][] = $item->retainDate;
    }
  }

  // dump($retainData);
  // dump($retainList);

  sort($referList);
  sort($retainList);

  $careDataMix = array_merge($referList, $retainList);

  // dump($careDataMix);

  return $careDataMix;
}

function lastReferCBS($uic, $topic)
{

  global $conn;

  $data = [$uic, $topic];

  $stmt = $conn->prepare("SELECT referor, topic FROM refertotreat WHERE uic = ? AND topic = ? order by id DESC limit 1");
  $stmt->execute($data);

  return $stmt->fetch();
}

function age_range($age)
{

  if ($age >= 65) return 'มากกว่า 65';
  if ($age >= 60) return '60 - 64';
  if ($age >= 55) return '55 - 59';
  if ($age >= 50) return '50 - 54';
  if ($age >= 45) return '45 - 49';
  if ($age >= 40) return '40 - 44';
  if ($age >= 35) return '35 - 39';
  if ($age >= 30) return '30 - 34';
  if ($age >= 25) return '25 - 29';
  if ($age >= 20) return '20 - 24';
  if ($age >= 15) return '15 - 19';
  if ($age >= 10) return '10 - 14';
  if ($age >= 5) return '5 - 9';
  if ($age >= 1) return '1 - 4';

  return 'น้อยกว่า 1';
}

function fetch_KP_PREV($startDate, $endDate, $period = NULL)
{
  global $conn;

  $data = [$startDate, $endDate];

  $response = [];
  $no = 0;

  $stmt = $conn->prepare("SELECT 
  a.*,
  b.service_date as tested_date,
  c.offer_hiv_testing as clinic_offer_hiv_testing,
  c.offer_condom_lub as clinic_offer_condom_lub,
  c.offer_prep as clinic_offer_prep,
  e.location as tested_location

  FROM reach a 
  LEFT JOIN clinic b ON a.id = b.refer_number AND a.uic = b.uic 
  LEFT JOIN clinicnewcase c ON c.clinic_id = b.clinic_id AND c.uic = b.uic AND c.queue = b.q
  LEFT JOIN cliniclog e ON b.clinic_id = e.id
  WHERE a.reachdate >= ? AND a.reachdate < ?");
  $stmt->execute($data);
  $reaches = $stmt->fetchall();
  foreach ($reaches as $item) {

    $age = ageByUic($item->uic, $item->reachdate);

    $response[$no]['id'] = $no + 1;
    $response[$no]['source'] = 'Reach';
    $response[$no]['source_id'] = $item->id;
    $response[$no]['type'] = $item->type;
    $response[$no]['location'] = $item->location;
    $response[$no]['tmn_token'] = '';
    $response[$no]['tmn_platform'] = '';
    $response[$no]['cbs'] = $item->cbs;
    $response[$no]['date'] = $item->reachdate;
    $response[$no]['tested_date'] = $item->tested_date;
    $response[$no]['tested_location'] = $item->tested_location;
    $response[$no]['uic'] = $item->uic;
    $response[$no]['age'] = $age;
    $response[$no]['age_range'] = age_range($age);
    $response[$no]['kp'] = $item->sex;
    $response[$no]['firstname'] = $item->firstname;
    $response[$no]['lastname'] = $item->lastname;
    $response[$no]['nickname'] = $item->name;
    $response[$no]['id_card'] = $item->peopleid;
    $response[$no]['phone'] = $item->phone;
    $response[$no]['hiv_tellresult'] = $item->tellresult;
    $response[$no]['invite_testing'] = $item->checkhivtest;
    $response[$no]['comment'] = $item->comment;
    $response[$no]['offer_hiv_testing'] = $item->offer_hiv_testing;
    $response[$no]['offer_condom_lub'] = $item->offer_condom_lub;
    $response[$no]['offer_prep'] = $item->offer_prep;
    $response[$no]['clinic_offer_hiv_testing'] = $item->clinic_offer_hiv_testing;
    $response[$no]['clinic_offer_condom_lub'] = $item->clinic_offer_condom_lub;
    $response[$no]['clinic_offer_prep'] = $item->clinic_offer_prep;
    $response[$no]['clinic_type'] = "";

    $no++;
  }

  // $stmt = $conn->prepare("SELECT a.*, 
  // b.sex, 
  // b.nickname, 
  // b.firstname, 
  // b.lastname, 
  // b.id_card, 
  // b.phone,
  // c.offer_hiv_testing as clinic_offer_hiv_testing,
  // c.offer_condom_lub as clinic_offer_condom_lub,
  // c.offer_prep as clinic_offer_prep,
  // c.reachCbs as reachCbs,
  // d.service_date as tested_date,
  // e.location as tested_location

  // FROM retainnegative a
  // LEFT JOIN clinic b ON a.sn = b.sn 
  // LEFT JOIN clinicnewcase c ON c.recruitID = a.id AND c.uic = a.uic
  // LEFT JOIN clinic d ON c.clinic_id = d.clinic_id AND c.uic = d.uic AND c.queue = d.q 
  // LEFT JOIN cliniclog e ON d.clinic_id = e.id
  // WHERE ((a.method = 'โทรศัพท์' AND a.phoneOption like '%เคสยินดีให้ข้อมูล%') OR (a.method = 'Online' AND a.onlineOption like '%ยินดีให้ข้อมูล%') OR (a.method = 'พบปะ/เยี่ยมบ้าน')) AND a.retainDate >= ? AND a.retainDate < ?");
  // $stmt->execute($data);
  // $reaches = $stmt->fetchall();
  // foreach ($reaches as $item) {

  //   $age = ageByUic($item->uic, $item->retainDate);

  //   $response[$no]['id'] = $no + 1;
  //   $response[$no]['source'] = 'Negative Retain';
  //   $response[$no]['source_id'] = $item->id;
  //   $response[$no]['type'] = $item->method;
  //   $response[$no]['location'] = 'DIC';
  //   $response[$no]['tmn_token'] = '';
  //   $response[$no]['tmn_platform'] = '';
  //   $response[$no]['cbs'] = $item->cbs;
  //   $response[$no]['reachCbs'] = $item->reachCbs;
  //   $response[$no]['date'] = $item->retainDate;
  //   $response[$no]['tested_date'] = $item->tested_date;
  //   $response[$no]['tested_location'] = $item->tested_location;
  //   $response[$no]['uic'] = $item->uic;
  //   $response[$no]['age'] = $age;
  //   $response[$no]['age_range'] = age_range($age);
  //   $response[$no]['kp'] = $item->sex;
  //   $response[$no]['firstname'] = $item->firstname;
  //   $response[$no]['lastname'] = $item->lastname;
  //   $response[$no]['nickname'] = $item->nickname;
  //   $response[$no]['id_card'] = $item->id_card;
  //   $response[$no]['phone'] = $item->phone;
  //   $response[$no]['hiv_tellresult'] = 'Negative';
  //   $response[$no]['invite_testing'] = $item->inviteToTest;
  //   $response[$no]['comment'] = $item->retainDetail;
  //   $response[$no]['offer_hiv_testing'] = $item->offer_hiv_testing;
  //   $response[$no]['offer_condom_lub'] = $item->offer_condom_lub;
  //   $response[$no]['offer_prep'] = $item->offer_prep;
  //   $response[$no]['clinic_offer_hiv_testing'] = $item->clinic_offer_hiv_testing;
  //   $response[$no]['clinic_offer_condom_lub'] = $item->clinic_offer_condom_lub;
  //   $response[$no]['clinic_offer_prep'] = $item->clinic_offer_prep;
  //   $response[$no]['clinic_type'] = "";

  //   $no++;
  // }

  $stmt = $conn->prepare("SELECT a.*, 
  b.testMeNowCheck,
  b.testMeNowToken,
  b.testMeNowPlatform,
  b.testMeNowCode,
  b.love2testCheck,
  b.love2testCode,
  b.recruiting,
  b.wantTestReason,
  b.offer_hiv_testing as clinic_offer_hiv_testing,
  b.offer_condom_lub as clinic_offer_condom_lub,
  b.offer_prep as clinic_offer_prep,
  b.reachCbs as reachCbs,
  e.location as tested_location

  FROM clinic a 
  LEFT JOIN cliniclog e ON a.clinic_id = e.id
  LEFT JOIN clinicnewcase b ON a.clinic_id = b.clinic_id and a.q = b.queue
  WHERE a.cbs = 'walk in' AND a.service_date >= ? AND a.service_date < ?");
  $stmt->execute($data);
  $reaches = $stmt->fetchall();
  foreach ($reaches as $item) {

    $age = ageByUic($item->uic, $item->service_date);

    $response[$no]['id'] = $no + 1;
    $response[$no]['source'] = 'Clinic Walk-in';
    $response[$no]['source_id'] = $item->sn;
    $response[$no]['type'] = $item->route;
    $response[$no]['location'] = $item->mobile;
    $response[$no]['tmn_token'] = $item->testMeNowToken;
    $response[$no]['tmn_platform'] = $item->testMeNowPlatform;
    $response[$no]['cbs'] = $item->counselor;
    $response[$no]['reachCbs'] = $item->reachCbs;
    $response[$no]['date'] = $item->service_date;
    $response[$no]['tested_date'] = $item->service_date;
    $response[$no]['tested_location'] = $item->tested_location;
    $response[$no]['uic'] = $item->uic;
    $response[$no]['age'] = $age;
    $response[$no]['age_range'] = age_range($age);
    $response[$no]['kp'] = $item->sex;
    $response[$no]['firstname'] = $item->firstname;
    $response[$no]['lastname'] = $item->lastname;
    $response[$no]['nickname'] = $item->nickname;
    $response[$no]['id_card'] = $item->id_card;
    $response[$no]['phone'] = $item->phone;
    $response[$no]['hiv_tellresult'] = $item->hiv_result == '-' ? 'check_comment' : '';
    $response[$no]['invite_testing'] = $item->wantTestReason;
    $response[$no]['comment'] = checkSerialize($item->comment1);
    $response[$no]['clinic_offer_hiv_testing'] = $item->clinic_offer_hiv_testing;
    $response[$no]['clinic_offer_condom_lub'] = $item->clinic_offer_condom_lub;
    $response[$no]['clinic_offer_prep'] = $item->clinic_offer_prep;
    $response[$no]['cinic_type'] = $item->type;

    $no++;
  }


  return $response;
}

function fetch_KP_PREV_RSAT($startDate, $endDate, $period = NULL)
{
  global $conn;

  $data = [$startDate, $endDate];

  $response = [];
  $no = 0;

  $stmt = $conn->prepare("SELECT 
  a.*,
  b.service_date as tested_date,
  c.offer_hiv_testing as clinic_offer_hiv_testing,
  c.offer_condom_lub as clinic_offer_condom_lub,
  c.offer_prep as clinic_offer_prep,
  e.location as tested_location

  FROM reach a 
  LEFT JOIN clinic b ON a.id = b.refer_number AND a.uic = b.uic 
  LEFT JOIN clinicnewcase c ON c.clinic_id = b.clinic_id AND c.uic = b.uic AND c.queue = b.q
  LEFT JOIN cliniclog e ON b.clinic_id = e.id
  WHERE a.reachdate >= ? AND a.reachdate < ?");
  $stmt->execute($data);
  $reaches = $stmt->fetchall();
  foreach ($reaches as $item) {

    $age = ageByUic($item->uic, $item->reachdate);

    $response[$no]['id'] = $no + 1;
    $response[$no]['source'] = 'Reach';
    $response[$no]['source_id'] = $item->id;
    $response[$no]['type'] = $item->type;
    $response[$no]['location'] = $item->location;
    $response[$no]['tmn_token'] = '';
    $response[$no]['tmn_platform'] = '';
    $response[$no]['cbs'] = $item->cbs;
    $response[$no]['date'] = $item->reachdate;
    $response[$no]['tested_date'] = $item->tested_date;
    $response[$no]['tested_location'] = $item->tested_location;
    $response[$no]['uic'] = $item->uic;
    $response[$no]['age'] = $age;
    $response[$no]['age_range'] = age_range($age);
    $response[$no]['kp'] = $item->sex;
    $response[$no]['firstname'] = $item->firstname;
    $response[$no]['lastname'] = $item->lastname;
    $response[$no]['nickname'] = $item->name;
    $response[$no]['id_card'] = $item->peopleid;
    $response[$no]['phone'] = $item->phone;
    $response[$no]['hiv_tellresult'] = $item->tellresult;
    $response[$no]['invite_testing'] = $item->checkhivtest;
    $response[$no]['comment'] = $item->comment;
    $response[$no]['offer_hiv_testing'] = $item->offer_hiv_testing;
    $response[$no]['offer_condom_lub'] = $item->offer_condom_lub;
    $response[$no]['offer_prep'] = $item->offer_prep;
    $response[$no]['clinic_offer_hiv_testing'] = $item->clinic_offer_hiv_testing;
    $response[$no]['clinic_offer_condom_lub'] = $item->clinic_offer_condom_lub;
    $response[$no]['clinic_offer_prep'] = $item->clinic_offer_prep;
    $response[$no]['clinic_type'] = "";

    $no++;
  }

  $stmt = $conn->prepare("SELECT a.*, 
  b.sex, 
  b.nickname, 
  b.firstname, 
  b.lastname, 
  b.id_card, 
  b.phone,
  c.offer_hiv_testing as clinic_offer_hiv_testing,
  c.offer_condom_lub as clinic_offer_condom_lub,
  c.offer_prep as clinic_offer_prep,
  c.reachCbs as reachCbs,
  d.service_date as tested_date,
  e.location as tested_location
  
  FROM retainnegative a
  LEFT JOIN clinic b ON a.sn = b.sn 
  LEFT JOIN clinicnewcase c ON c.recruitID = a.id AND c.uic = a.uic
  LEFT JOIN clinic d ON c.clinic_id = d.clinic_id AND c.uic = d.uic AND c.queue = d.q 
  LEFT JOIN cliniclog e ON d.clinic_id = e.id
  WHERE ((a.method = 'โทรศัพท์' AND a.phoneOption like '%เคสยินดีให้ข้อมูล%') OR (a.method = 'Online' AND a.onlineOption like '%ยินดีให้ข้อมูล%') OR (a.method = 'พบปะ/เยี่ยมบ้าน')) AND a.retainDate >= ? AND a.retainDate < ?");
  $stmt->execute($data);
  $reaches = $stmt->fetchall();
  foreach ($reaches as $item) {

    $age = ageByUic($item->uic, $item->retainDate);

    $response[$no]['id'] = $no + 1;
    $response[$no]['source'] = 'Negative Retain';
    $response[$no]['source_id'] = $item->id;
    $response[$no]['type'] = $item->method;
    $response[$no]['location'] = 'DIC';
    $response[$no]['tmn_token'] = '';
    $response[$no]['tmn_platform'] = '';
    $response[$no]['cbs'] = $item->cbs;
    $response[$no]['reachCbs'] = $item->reachCbs;
    $response[$no]['date'] = $item->retainDate;
    $response[$no]['tested_date'] = $item->tested_date;
    $response[$no]['tested_location'] = $item->tested_location;
    $response[$no]['uic'] = $item->uic;
    $response[$no]['age'] = $age;
    $response[$no]['age_range'] = age_range($age);
    $response[$no]['kp'] = $item->sex;
    $response[$no]['firstname'] = $item->firstname;
    $response[$no]['lastname'] = $item->lastname;
    $response[$no]['nickname'] = $item->nickname;
    $response[$no]['id_card'] = $item->id_card;
    $response[$no]['phone'] = $item->phone;
    $response[$no]['hiv_tellresult'] = 'Negative';
    $response[$no]['invite_testing'] = $item->inviteToTest;
    $response[$no]['comment'] = $item->retainDetail;
    $response[$no]['offer_hiv_testing'] = $item->offer_hiv_testing;
    $response[$no]['offer_condom_lub'] = $item->offer_condom_lub;
    $response[$no]['offer_prep'] = $item->offer_prep;
    $response[$no]['clinic_offer_hiv_testing'] = $item->clinic_offer_hiv_testing;
    $response[$no]['clinic_offer_condom_lub'] = $item->clinic_offer_condom_lub;
    $response[$no]['clinic_offer_prep'] = $item->clinic_offer_prep;
    $response[$no]['clinic_type'] = "";

    $no++;
  }

  $stmt = $conn->prepare("SELECT a.*, 
  b.testMeNowCheck,
  b.testMeNowToken,
  b.testMeNowPlatform,
  b.testMeNowCode,
  b.love2testCheck,
  b.love2testCode,
  b.recruiting,
  b.wantTestReason,
  b.offer_hiv_testing as clinic_offer_hiv_testing,
  b.offer_condom_lub as clinic_offer_condom_lub,
  b.offer_prep as clinic_offer_prep,
  b.reachCbs as reachCbs,
  e.location as tested_location

  FROM clinic a 
  LEFT JOIN cliniclog e ON a.clinic_id = e.id
  LEFT JOIN clinicnewcase b ON a.clinic_id = b.clinic_id and a.q = b.queue
  WHERE a.cbs = 'walk in' AND a.service_date >= ? AND a.service_date < ?");
  $stmt->execute($data);
  $reaches = $stmt->fetchall();
  foreach ($reaches as $item) {

    $age = ageByUic($item->uic, $item->service_date);

    $response[$no]['id'] = $no + 1;
    $response[$no]['source'] = 'Clinic Walk-in';
    $response[$no]['source_id'] = $item->sn;
    $response[$no]['type'] = $item->route;
    $response[$no]['location'] = $item->mobile;
    $response[$no]['tmn_token'] = $item->testMeNowToken;
    $response[$no]['tmn_platform'] = $item->testMeNowPlatform;
    $response[$no]['cbs'] = $item->counselor;
    $response[$no]['reachCbs'] = $item->reachCbs;
    $response[$no]['date'] = $item->service_date;
    $response[$no]['tested_date'] = $item->service_date;
    $response[$no]['tested_location'] = $item->tested_location;
    $response[$no]['uic'] = $item->uic;
    $response[$no]['age'] = $age;
    $response[$no]['age_range'] = age_range($age);
    $response[$no]['kp'] = $item->sex;
    $response[$no]['firstname'] = $item->firstname;
    $response[$no]['lastname'] = $item->lastname;
    $response[$no]['nickname'] = $item->nickname;
    $response[$no]['id_card'] = $item->id_card;
    $response[$no]['phone'] = $item->phone;
    $response[$no]['hiv_tellresult'] = $item->hiv_result == '-' ? 'check_comment' : '';
    $response[$no]['invite_testing'] = $item->wantTestReason;
    $response[$no]['comment'] = checkSerialize($item->comment1);
    $response[$no]['clinic_offer_hiv_testing'] = $item->clinic_offer_hiv_testing;
    $response[$no]['clinic_offer_condom_lub'] = $item->clinic_offer_condom_lub;
    $response[$no]['clinic_offer_prep'] = $item->clinic_offer_prep;
    $response[$no]['cinic_type'] = $item->type;

    $no++;
  }


  return $response;
}

function fetch_HTS_TST()
{
  global $conn;

  $q1_start = checkQuarter()['q1_start'];
  $this_quarter_end = checkQuarter()['this_quarter_end'];

  $data = [$q1_start, $this_quarter_end];

  $response = [];
  $no = 0;

  $stmt = $conn->prepare("SELECT * FROM reach WHERE reachdate >= ? AND reachdate < ?");
  $stmt->execute($data);
  $reaches = $stmt->fetchall();
  foreach ($reaches as $item) {

    $age = ageByUic($item->uic, $item->service_date);

    $response[$no]['id'] = $no + 1;
    $response[$no]['source'] = 'Reach';
    $response[$no]['source_id'] = $item->id;
    $response[$no]['type'] = $item->type;
    $response[$no]['location'] = $item->location;
    $response[$no]['tmn_token'] = '';
    $response[$no]['tmn_platform'] = '';
    $response[$no]['cbs'] = $item->cbs;
    $response[$no]['date'] = $item->reachdate;
    $response[$no]['uic'] = $item->uic;
    $response[$no]['age'] = $age;
    $response[$no]['age_range'] = age_range($age);
    $response[$no]['kp'] = $item->sex;
    $response[$no]['firstname'] = $item->firstname;
    $response[$no]['lastname'] = $item->lastname;
    $response[$no]['nickname'] = $item->name;
    $response[$no]['id_card'] = $item->peopleid;
    $response[$no]['phone'] = $item->phone;
    $response[$no]['hiv_tellresult'] = $item->tellresult;
    $response[$no]['invite_testing'] = $item->checkhivtest;
    $response[$no]['comment'] = $item->comment;

    $no++;
  }

  return $response;
}

function ageByUic($uic, $dateCompare = '')
{

  $birthDate = birthdayByUic($uic)['birthDay'];

  return ageByBirthDate($birthDate, $dateCompare);
}

function birthdayByUic($uic)
{

  $uicYear = (int) substr($uic, -2, 2);
  $uicMonth = (int) substr($uic, -4, 2);
  $uicDate = (int) substr($uic, -6, 2);

  $birthYear = 0;

  // $birthYear = $uicYear > 80 ? 2400 + $uicYear : 2500 + $uicYear;
  $birthYear = $uicYear + 2500 - 543 >= date('Y') ? $uicYear + 2400 - 543 : $uicYear + 2500 - 543;

  $birthDay = sprintf("%04d-%02d-%02d", $birthYear, $uicMonth, $uicDate);

  $data = [];
  $data['birthDate'] = $uicDate;
  $data['birthMonth'] = $uicMonth;
  $data['birthYearB'] = $birthYear + 543;
  $data['birthYearC'] = $birthYear;
  $data['birthDay'] = $birthDay;

  return $data;
}

function fetchClinicDataByCase($startDate, $endDate, $period = null)
{
  global $conn;

  $where = "WHERE a.clinic_id >= '0' AND a.service_date >= ? AND a.service_date < ?";

  $data = [$startDate, $endDate];

  if ($period == 'all') {
    $where = "WHERE a.clinic_id >= '0'";
    $data = [];
  }

  $stmt = $conn->prepare("SELECT 
  a.sn, 
  a.q, 
  a.clinic_id, 
  a.prep, 
  a.prep_med_amount, 
  a.service_date, 
  a.type, 
  a.mobile, 
  a.uic, 
  a.count, 
  a.age, 
  a.sex, 
  a.firstname, 
  a.lastname, 
  a.nickname, 
  a.phone, 
  a.wp, 
  a.referor, 
  a.cbs, 
  a.refer_number, 
  a.id_card, 
  a.counselor, 
  a.hiv_result, 
  a.sti_result, 
  a.hcv_result, 
  a.rpr_result, 
  a.ct_result, 
  a.ng_result, 
  a.cd4, 
  a.cd4percent, 
  a.comment1, 
  a.comment2,

  b.recruitType, 
  b.walkin_type, 
  b.walkin_route, 
  b.recruitCode, 
  b.testMeNowCheck, 
  b.testMeNowToken, 
  b.testMeNowPlatform, 
  b.testMeNowCode, 
  b.love2testCheck, 
  b.love2testCode, 
  b.recruiting, 
  b.healthcare, 
  b.hospital, 
  b.occupation, 
  b.nationality, 
  b.tbScore, 
  b.wantTestReason, 
  b.useInjectDrug, 
  b.hivTested,
  b.registered_cbs,

  l2t.tracking_ref,

  c.type as reach_type, 
  c.cbs as reach_cbs, 
  c.source as reach_source, 
  c.qrcode as reach_qrcode, 
  c.location as reach_location, 
  c.reachdate as reach_date, 
  c.pm_uic as reach_pm_uic,

  d.tpha_result, 
  d.tpha_refer_to_treat, 
  d.tpha_no_refer_reason,
  
  e.isExpress, 
  e.lastHivDate, 
  e.lastHivLocation, 
  e.prep_screening, 
  e.prep_today_route, 
  e.prep_service_visit, 
  e.prep_taken_check, 
  e.prep_provide, 
  e.prep_service_step, 
  e.prep_no_provide_reason, 
  e.serviceProvide,

  f.incentiveDistribute, 
  f.nextFuTopic, 
  f.prep_set_visit, 
  f.nextFuDate, 
  f.prep_no_distribution_reason,

  f.condomDistribute as condomTotal,
  f.condomDistribute49 as condom49,
  f.condomDistribute52 as condom52,
  f.condomDistribute53 as condom53,
  f.condomDistribute54 as condom54,
  f.condomDistribute56 as condom56,
  f.lubricantDistribute as lubricant,

  -- g.hiv_result,
  -- g.tpha_result,
  -- g.vdrl_result,
  -- g.cd4_result,
  -- g.cd4_value,
  -- g.cd4_percent,
  -- g.hcv_result,
  g.ct_result,
  g.ng_result,
  g.lab_staff_collect,
  g.lab_staff_report,
  g.lab_staff_confirm,
  -- g.vl_result,
  -- g.vl_result_value,

  -- h.lab_topic as late_lab_topic,
  h.lab_result_ng as late_lab_result_ng,
  h.lab_result_ct as late_lab_result_ct,
  -- h.lab_result_cd4 as late_lab_result_cd4,
  -- h.lab_result_vl as late_lab_result_vl,
  -- h.lab_date as late_lab_date,
  -- h.lab_report_staff as late_lab_report_staff,
  -- h.lab_report_time as late_lab_report_time,
  -- h.lab_confirm_staff as late_lab_confirm_staff,
  -- h.lab_confirm_time as late_lab_confirm_time,
  -- h.lab_remarks as late_lab_remarks
  addcare.hivKnowResult as hiv_known

  FROM clinic a
  LEFT JOIN clinicnewcase b ON a.newcase_id = b.id
  LEFT JOIN love2test l2t ON b.love2testCode = l2t.res_id
  LEFT JOIN reach c ON a.refer_number = c.id AND a.uic = c.uic
  LEFT JOIN caresupportdatabase d ON a.uic = d.uic
  LEFT JOIN clinicprecounseling e ON a.pre_id = e.id
  LEFT JOIN clinicpostcounseling f ON a.post_id = f.id
  LEFT JOIN cliniclabresult g ON a.lab_id = g.id
  LEFT JOIN late_lab_result h ON a.latelab_id = h.id
  LEFT JOIN addcarehistory addcare ON a.clinic_id = addcare.clinic_id AND a.q = addcare.queue
  $where
  group by a.sn
  ");

  $stmt->execute($data);

  return $stmt;
}

function prep_summary($fy = '')
{
  $date = '';

  if ($fy != '') $date = ($fy - 1) . '-10-01';

  $data = selectDatabase('prep_profile', [], [], ['id' => 'DESC'])->fetchall();

  $response = [];

  $prep_current = array_filter($data, function ($item) use ($date) {
    return $item->last_prep_date >= $date && json_decode($item->last_prep_value)[0] > 0;
  });
  $prep_new_in_site = array_filter($data, function ($item) use ($date) {
    return $item->enroll_date >= $date && json_decode($item->last_prep_value)[0] > 0;
  });
  $prep_new_in_life = array_filter($data, function ($item) use ($date) {
    return $item->enroll_date >= $date && $item->prep_exp_enroll == 'ครั้งแรกในชีวิต' && json_decode($item->last_prep_value)[0] > 0;
  });
  $prep_curr_nhso = array_filter($prep_current, function ($item) use ($date) {
    return $item->last_prep_project == 'PrEP สปสช';
  });
  $prep_curr_princess = array_filter($prep_current, function ($item) use ($date) {
    return $item->last_prep_project == 'PrEP Princess';
  });
  $prep_curr_gf = array_filter($prep_current, function ($item) use ($date) {
    return $item->last_prep_project == 'PrEP GF';
  });
  $prep_curr_gf = array_filter($prep_current, function ($item) use ($date) {
    return $item->last_prep_project == 'PrEP GF';
  });
  $prep_curr_daily = array_filter($prep_current, function ($item) use ($date) {
    return $item->last_prep_method == 'Daily PrEP';
  });
  $prep_curr_ondemand = array_filter($prep_current, function ($item) use ($date) {
    return $item->last_prep_method == 'On-demand PrEP';
  });

  $response['prep_total'] = count($data);
  $response['prep_current'] = count($prep_current);
  $response['prep_new_in_site'] = count($prep_new_in_site);
  $response['prep_new_in_life'] = count($prep_new_in_life);
  $response['prep_curr_nhso'] = count($prep_curr_nhso);
  $response['prep_curr_princess'] = count($prep_curr_princess);
  $response['prep_curr_gf'] = count($prep_curr_gf);
  $response['prep_curr_gf'] = count($prep_curr_gf);
  $response['prep_curr_daily'] = count($prep_curr_daily);
  $response['prep_curr_ondemand'] = count($prep_curr_ondemand);

  return $response;
}

function genderByKp($kp)
{
  $male = ['MSM', 'MSW', 'TG', 'TGW', 'TGSW', 'PWID-Male', 'Male'];
  $female = ['FSW', 'TGM', 'PWID-Female', 'Female'];
  if (in_array($kp, $male)) return 'ชาย';
  if (in_array($kp, $female)) return 'หญิง';

  return 'NA';
}

// fetch export database data
function ihriClinicLists($period)
{
  global $conn;

  if ($period == '') {
    $start = checkQuarter()['q1_start'];
    $end = date('Y-m-d');
  }

  if ($period == 'month') {
    $start = checkQuarter()['this_month_start'];
    $end = checkQuarter()['this_month_end'];
  }

  if ($period == 'quarter') {
    $start = checkQuarter()['this_quarter_start'];
    $end = checkQuarter()['this_quarter_end'];
  }

  if ($period == 'fy23') {
    $start = checkQuarter("2023-01-01")['q1_start'];
    $end = checkQuarter("2023-01-01")['q4_end'];
  }

  if ($period == 'fy22') {
    $start = checkQuarter("2022-01-01")['q1_start'];
    $end = checkQuarter("2022-01-01")['q4_end'];
  }

  if ($period == 'fy21') {
    $start = checkQuarter("2021-01-01")['q1_start'];
    $end = checkQuarter("2021-01-01")['q4_end'];
  }

  if ($period == 'all') {
    $start = '2020-10-01';
    $end = date('Y-m-d');
  }

  $data = [$start, $end];

  $stmt = $conn->prepare("SELECT 
    c.sn, 
    c.prep, 
    c.service_date, 
    c.uic, 
    c.nickname, 
    c.phone, 
    c.sex, 
    c.cbs, 
    c.type, 
    c.id_card, 
    c.hiv_result, 
    c.sti_result, 
    c.hcv_result, 
    c.cd4, 
    c.cd4percent, 
    c.comment1, 
    c.comment2, 
    c.eCascadeCheck, 
    c.eCascadeStaff, 
    c.eCascadeDate, 
    n.*, 
    p.condomDistribute, 
    p.lubricantDistribute,
    p.prep_distribution,
    p.prep_no_distribution_reason,
    p.prep_comment,
    pre.serviceProvide,
    pre.prep_provide,
    pre.prep_taken_check,
    pre.prep_service_visit,
    lab.extra_blood,
    lab.extra_blood_services
    FROM clinic c 
    LEFT JOIN clinicnewcase n ON c.newcase_id = n.id
    LEFT JOIN cliniclabresult lab ON c.lab_id = lab.id
    LEFT JOIN clinicprecounseling pre ON c.pre_id = pre.id
    LEFT JOIN clinicpostcounseling p ON c.post_id = p.id
    WHERE c.clinic_id > 0 AND c.service_date >= ? AND c.service_date < ?
    order by c.sn");

  $stmt->execute($data);

  return $stmt;
}

function arrayToJSON($arr)
{
  // Accept either comma-separated string or array
  if (is_array($arr)) {
    $prepare_arr = $arr;
  } else if (is_string($arr)) {
    $prepare_arr = explode(',', $arr);
  } else if (is_null($arr)) {
    $prepare_arr = [];
  } else {
    // fallback: cast scalars to string then explode
    $prepare_arr = explode(',', strval($arr));
  }

  $response = [];
  foreach ($prepare_arr as $item) {
    if ($item !== '' && $item !== null) $response[] = $item;
  }

  return json_encode($response, JSON_UNESCAPED_UNICODE);
}

function referTopicLabel($topic)
{
  if ($topic == 'HIV') return labelbig($topic, 'danger');
  if ($topic == 'Syphilis') return labelbig($topic, 'warning');
  if ($topic == 'HCV') return labelbig($topic, 'info');
  if ($topic == 'STIs') return labelbig($topic, 'info');
  if ($topic == 'NG') return labelbig($topic, 'maroon');
  if ($topic == 'CT') return labelbig($topic, 'purple');
}

function replace_unicode_escape_sequence($match)
{
  return mb_convert_encoding(pack('H*', $match[1]), 'UTF-8', 'UCS-2BE');
}

function unicode_decode($str)
{
  return preg_replace_callback('/\\\\u([0-9a-f]{4})/i', 'replace_unicode_escape_sequence', $str);
}

function array_pluck($array, $key)
{
  return array_map(function ($v) use ($key) {
    return is_object($v) ? $v->$key : $v[$key];
  }, $array);
}

//get IP
function getUserIP()
{
  $client  = @$_SERVER['HTTP_CLIENT_IP'];
  $forward = @$_SERVER['HTTP_X_FORWARDED_FOR'];
  $remote  = $_SERVER['REMOTE_ADDR'];

  if (filter_var($client, FILTER_VALIDATE_IP)) {
    $ip = $client;
  } elseif (filter_var($forward, FILTER_VALIDATE_IP)) {
    $ip = $forward;
  } else {
    $ip = $remote;
  }
  return $ip;
}

function base64_to_png($base64_string, $output_file)
{
  // open the output file for writing
  $ifp = fopen($output_file, 'wb');

  // split the string on commas
  // $data[ 0 ] == "data:image/png;base64"
  // $data[ 1 ] == <actual base64 string>
  $data = explode(',', $base64_string);

  // we could add validation here with ensuring count( $data ) > 1
  fwrite($ifp, base64_decode($data[1]));

  // clean up the file resource
  fclose($ifp);

  return $output_file;
}

function generateRandomString($length = 10, $letters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ')
{
  $characters = $letters;
  $charactersLength = strlen($characters);
  $randomString = '';
  for ($i = 0; $i < $length; $i++) {
    $randomString .= $characters[rand(0, $charactersLength - 1)];
  }
  return $randomString;
}

function testmenow_fetch_single($api_token, $resCodeLong)
{

  $client = new Client();

  $targeturl = "https://admin.testmenow.net/api/reservation";
  $data = [
    'api_token' => $api_token,
    'resCodeLong' => $resCodeLong,
  ];
  $url = $targeturl . '?' . http_build_query($data);
  $response = $client->get($url, [
    'allow_redirects' => true,
  ]);
  $contents = $response->getBody()->getContents();
  $dataArr = json_decode($contents, true);
  // dump($data);
  // dump($dataArr);
  return $dataArr;
}

function rsatbooking_fetch_multi($api_token, $start_date, $end_date, $clinic_name = 'rsat_bkk')
{
  $client = new Client();

  $url = "https://www.rsat.info/booking/api/get_appointments.php";

  try {
    $response = $client->post($url, [
      'headers' => [
        'Authorization' => 'Bearer ' . $api_token,
        'Accept'        => 'application/json',
        'Content-Type'  => 'application/json',
      ],
      'json' => [
        'start_date'   => $start_date,
        'end_date'     => $end_date,
        'clinic_name'  => $clinic_name,
      ],
      'allow_redirects' => true,
    ]);

    $contents = $response->getBody()->getContents();
    $dataArr = json_decode($contents, true);
    return $dataArr;
  } catch (\Exception $e) {
    // สำหรับ debug หรือบันทึก error
    // Log::error('RSAT Booking API Error: ' . $e->getMessage());
    return null;
  }
}

function testmenow_fetch_multi($api_token, $start_date, $end_date)
{

  $client = new Client();

  $targeturl = "https://admin.testmenow.net/api/reservations";
  $data = [
    'api_token' => $api_token,
    'dateFrom' => $start_date,
    'dateTo' => $end_date
  ];
  $url = $targeturl . '?' . http_build_query($data);
  $response = $client->get($url, [
    'allow_redirects' => true,
  ]);
  $contents = $response->getBody()->getContents();
  $dataArr = json_decode($contents, true);
  // dump($data);
  // dump($dataArr);
  return $dataArr;
}

function testmenow_update_reservation($api_token, $item = [
  'res_code_long' => null,
  'is_confirmed' => 0,
  'is_arrived' => 0,
  'uic_code' => null,
  'kp_status' => -1,
  'tested_status' => -1,
  'clinic_notes' => null
])
{

  // "is_confirmed"  0 Not confirmed, 1 Confirmed
  // "is_arrived"  0 Not Arrived, 1 Arrived 
  // "kp_status"  -1 No KP, 1 MSM, 2 TG, 3 MSW, 4 TGSW, 5 FSW, 6 TGM, 97 GPM, 98 GPF
  // "tested_status" -1 Not Tested, 0 Negative, 1 positive, 2 indeterminate

  $client = new Client();

  $targeturl = "https://admin.testmenow.net/api/reservation";

  $data = [
    "query" => ['api_token' => $api_token],
    "json" => $item,
    "allow_redirects" => true,
  ];
  $response = $client->request('POST', $targeturl, $data);
  $contents = $response->getBody()->getContents();
  $dataArr = json_decode($contents, true);
  // dump($data);
  // dump($dataArr);
  return $dataArr;
}

function love2test_update_reservation($bearerToken, $data)
{

  $data['arrived_status'] = $data['is_arrived'];
  unset($data['is_arrived']);
  $data["confirm_status"] = 0;
  $data["confirm_date"] = null;

  // The API endpoint
  $url = "https://data.love2test.org/api/appointment/update";

  // cURL headers
  $headers = [
    "Authorization: Bearer {$bearerToken}",
    "Content-Type: application/x-www-form-urlencoded"
  ];

  // Initialize cURL session
  $curl = curl_init();

  // Set cURL options
  curl_setopt($curl, CURLOPT_URL, $url);
  curl_setopt($curl, CURLOPT_POST, true);
  curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data)); // Data is sent as form-urlencoded
  curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
  curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

  // Execute the cURL session and fetch the response
  $response = curl_exec($curl);

  // Check for errors
  if (curl_errno($curl)) {
    // If an error occurs, you may want to throw an exception or handle it as per your need
    echo 'Error:' . curl_error($curl);
  }

  // Close cURL session
  curl_close($curl);

  // Output the response from the API
  return json_decode($response);
  ///
}

function rsatbooking_update_reservation($bearerToken, $data)
{

  // $data['arrived_status'] = $data['is_arrived'];
  // unset($data['is_arrived']);
  // $data["confirm_status"] = 0;
  // $data["confirm_date"] = null;

  // // The API endpoint
  // $url = "https://data.love2test.org/api/appointment/update";

  // // cURL headers
  // $headers = [
  //     "Authorization: Bearer {$bearerToken}",
  //     "Content-Type: application/x-www-form-urlencoded"
  // ];

  // // Initialize cURL session
  // $curl = curl_init();

  // // Set cURL options
  // curl_setopt($curl, CURLOPT_URL, $url);
  // curl_setopt($curl, CURLOPT_POST, true);
  // curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data)); // Data is sent as form-urlencoded
  // curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
  // curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

  // // Execute the cURL session and fetch the response
  // $response = curl_exec($curl);

  // // Check for errors
  // if (curl_errno($curl)) {
  //     // If an error occurs, you may want to throw an exception or handle it as per your need
  //     echo 'Error:' . curl_error($curl);
  // }

  // // Close cURL session
  // curl_close($curl);

  // // Output the response from the API
  // return json_decode($response);
  // ///
}

function nhso_get_lab_request($api_key, $username, $data = [])
{
  /// proxy
  $data_set = [
    "api_key" => $api_key,
    "username" => $username,
    "data" => $data
  ];

  $labRequest = file_get_contents('https://mybunchee.com/api/get_lab_request.php', false, stream_context_create([
    'http' => [
      'method' => 'POST',
      'header'  => 'Content-Type: application/json',
      'content' => json_encode($data_set)
    ]
  ]));
  return json_decode($labRequest, true);
  /// proxy

  // //// direct route
  // $url = 'https://dmis.nhso.go.th/NAPPLUSLABAPI/api/get_lab_request';

  // // Headers
  // $headers = [
  //   'Content-Type: application/json',
  //   'UserName: '. $username,
  //   'Password: '. $api_key
  // ];

  // // Initialize cURL session
  // $ch = curl_init($url);

  // // Set options
  // curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
  // curl_setopt($ch, CURLOPT_POST, true);
  // curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
  // curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

  // // Execute request and get response
  // $response = curl_exec($ch);

  // // Check for errors
  // if (curl_errno($ch)) {
  //   echo 'Error:' . curl_error($ch);
  // } else {
  //   // Decode the JSON response
  //   $responseData = json_decode($response, true);
  // }

  // // Close cURL session
  // curl_close($ch);

  // return $responseData;
  // //// direct route
}

function nhso_get_lab_result($api_key, $username, $data = [])
{
  /// proxy
  $data_set = [
    "api_key" => $api_key,
    "username" => $username,
    "data" => $data
  ];

  $labRequest = file_get_contents('https://mybunchee.com/api/get_lab_result.php', false, stream_context_create([
    'http' => [
      'method' => 'POST',
      'header'  => 'Content-Type: application/json',
      'content' => json_encode($data_set)
    ]
  ]));
  return json_decode($labRequest, true);
  /// proxy
}

function nhso_set_lab_result($api_key, $username, $data = [])
{

  /// proxy
  $data_set = [
    "api_key" => $api_key,
    "username" => $username,
    "data" => $data
  ];

  $labRequest = file_get_contents('https://mybunchee.com/api/set_lab_result.php', false, stream_context_create([
    'http' => [
      'method' => 'POST',
      'header'  => 'Content-Type: application/json',
      'content' => json_encode($data_set)
    ]
  ]));
  return json_decode($labRequest, true);
  /// proxy

  // //// direct route
  // $url = 'https://dmis.nhso.go.th/NAPPLUSLABAPI/api/set_lab_result';

  // // Headers
  // $headers = [
  //   'Content-Type: application/json',
  //   'UserName: '. $username,
  //   'Password: '. $api_key
  // ];

  // // Initialize cURL session
  // $ch = curl_init($url);

  // // Set options
  // curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
  // curl_setopt($ch, CURLOPT_POST, true);
  // curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
  // curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

  // // Execute request and get response
  // $response = curl_exec($ch);

  // // Check for errors
  // if (curl_errno($ch)) {
  //   echo 'Error:' . curl_error($ch);
  // } else {
  //   // Decode the JSON response
  //   $responseData = json_decode($response, true);
  // }

  // // Close cURL session
  // curl_close($ch);

  // return $responseData;
  // //// direct route
}

function ihri_uid_api($data, $token = '')
{

  // The API endpoint
  $url = "http://demopribta.ihri.org/api_ihri_services/uic_center.php";
  $url = "http://demopribta.ihri.org/api_ihri_services/uic_center_revises.php";

  // cURL headers
  $token = 'up71j6moDhOaOMLuTwOJAm1GefUtaPjDvtSg5kV1JJDfxOfwDTWA9cm37JiyfcUh';

  $headers = [
    "Authorization: Bearer $token",
    "Content-Type: application/x-www-form-urlencoded"
  ];

  // Initialize cURL session
  $curl = curl_init();

  // Set cURL options
  curl_setopt($curl, CURLOPT_URL, $url);
  curl_setopt($curl, CURLOPT_POST, true);
  curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data)); // Data is sent as form-urlencoded
  curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
  curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

  // Execute the cURL session and fetch the response
  $response = curl_exec($curl);

  // Check for errors
  if (curl_errno($curl)) {
    // If an error occurs, you may want to throw an exception or handle it as per your need
    echo 'Error:' . curl_error($curl);
  }

  // Close cURL session
  curl_close($curl);

  // Output the response from the API
  return json_decode($response);
  ///
}

function testmenow_create_ott($api_token, $item = [
  "kp_label" => "MSM",
  "age" => 36,
  "client_name" => "Wat",
  "uic_code" => "อช120228",
  "client_phone" => "**********",
  "client_status" => "index",
  "num_getting_contract" => 0,
  "num_getting_client" => 0,
  "num_getting_provider" => 0,
  "num_getting_pass" => 0,
  "pred_s_m_b" => 0,
  "pred_s_m_a" => 0,
  "pred_s_m_u" => 0,
  "pred_s_f_b" => 0,
  "pred_s_f_a" => 0,
  "pred_s_f_u" => 0,
  "pred_n_m_b" => 0,
  "pred_n_m_a" => 0,
  "pred_n_m_u" => 0,
  "pred_n_f_b" => 0,
  "pred_n_f_a" => 0,
  "pred_n_f_u" => 0
])
{

  $client = new Client();

  $targeturl = "https://admin.testmenow.net/api/ott/create";
  $data = [
    "query" => ['api_token' => $api_token],
    "json" => $item
  ];
  $response = $client->request('POST', $targeturl, $data);
  $contents = $response->getBody()->getContents();
  $dataArr = json_decode($contents, true);
  // dump($data);
  // dump($dataArr);
  return $dataArr;
}

function testmenow_ott_sms($api_token, $item = [
  "phone" => null,
  "seed_uid" => null,
  "smstype" => null,
  "custommessage" => ""
])
{

  $client = new Client();

  // W smstype 1 for index client referral no need phone and custommessage
  // X smstype 2 Our staff can distribute a link anonymously [Sex or Needle partners]
  // Y smstype 3 Our staff can distribute a link anonymously [did not share sex or needles]

  $targeturl = "https://admin.testmenow.net/api/ott/sendsms";
  $data = [
    "query" => ['api_token' => $api_token],
    "json" => $item

  ];
  $response = $client->request('POST', $targeturl, $data);
  $contents = $response->getBody()->getContents();
  $dataArr = json_decode($contents, true);
  // dump($data);
  // dump($dataArr);
  return $dataArr;
}

function LineNotify($text = "ส่ง LINE Notify", $send_1 = true, $send_2 = false, $send_3 = false, $send_4 = false, $send_5 = false, $send_6 = false, $send_7 = false, $send_8 = false)
{

  return;

  //Line notify
  $secrets = selectDatabase('linenotify')->fetchall();

  $instance_1 = new KS\Line\LineNotify($secrets[0]->secret);
  $instance_2 = new KS\Line\LineNotify($secrets[1]->secret ?? $secrets[0]->secret);
  $instance_3 = new KS\Line\LineNotify($secrets[2]->secret ?? $secrets[0]->secret);
  $instance_4 = new KS\Line\LineNotify($secrets[3]->secret ?? $secrets[0]->secret);
  $instance_5 = new KS\Line\LineNotify($secrets[4]->secret ?? $secrets[0]->secret);
  $instance_6 = new KS\Line\LineNotify($secrets[5]->secret ?? $secrets[0]->secret);
  $bas_instance = new KS\Line\LineNotify("*******************************************");
  $sms_instance = new KS\Line\LineNotify("*******************************************");

  // $image_path = 'promote.png'; //Line notify allow only jpeg and png file
  // $ln->send($text,$image_path);

  // group monitor
  if ($send_1) $instance_1->send($text);
  // group all
  if ($send_2) $instance_2->send($text);
  // developer
  if ($send_3) $instance_3->send($text);
  // group prep
  if ($send_4) $instance_4->send($text);
  // group questionnaire - custom
  if ($send_5) $instance_5->send($text);
  if ($send_6) $instance_6->send($text);
  if ($send_7) $bas_instance->send($text);
  if ($send_8) $sms_instance->send($text);
}

function LineNotifyToToken($text = "ส่ง LINE Notify", $token = 'p0NBs9Hw8VhqnFndyINni9oPAk4sQI8rZV8eKeZdn6w')
{

  return;

  $instance = new KS\Line\LineNotify($token);
  $instance->send($text);
}

function LineNotifyToTokenWithPic($text = "ส่ง LINE Notify", $pic = '', $token = 'p0NBs9Hw8VhqnFndyINni9oPAk4sQI8rZV8eKeZdn6w')
{

  return;

  $instance = new KS\Line\LineNotify($token);
  $instance->send($text, $pic);
}

function LineNotifyWithPic($text = "ส่ง LINE Notify", $pic = '', $send_1 = true, $send_2 = false, $send_3 = false, $send_4 = false, $send_5 = false, $send_6 = false)
{

  return;

  if (!$pic) $pic = NULL;
  //Line notify
  $secrets = selectDatabase('linenotify')->fetchall();

  $instance_1 = new KS\Line\LineNotify($secrets[0]->secret);
  $instance_2 = new KS\Line\LineNotify($secrets[1]->secret ?? $secrets[0]->secret);
  $instance_3 = new KS\Line\LineNotify($secrets[2]->secret ?? $secrets[0]->secret);
  $instance_4 = new KS\Line\LineNotify($secrets[3]->secret ?? $secrets[0]->secret);
  $instance_5 = new KS\Line\LineNotify($secrets[4]->secret ?? $secrets[0]->secret);
  $instance_6 = new KS\Line\LineNotify($secrets[5]->secret ?? $secrets[0]->secret);

  // $image_path = 'promote.png'; //Line notify allow only jpeg and png file
  // $ln->send($text,$image_path);

  if ($send_1) $instance_1->send($text, $pic);
  if ($send_2) $instance_2->send($text, $pic);
  if ($send_3) $instance_3->send($text, $pic);
  if ($send_4) $instance_4->send($text, $pic);
  if ($send_5) $instance_5->send($text, $pic);
  if ($send_6) $instance_6->send($text, $pic);
}

function url()
{
  if (isset($_SERVER['HTTPS'])) {
    $protocol = ($_SERVER['HTTPS'] && $_SERVER['HTTPS'] != "off") ? "https" : "http";
  } else {
    $protocol = 'http';
  }
  // return $protocol . "://" . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
  return $protocol . "://" . $_SERVER['HTTP_HOST'];
}

function eCascade_kp_code_convert($data)
{

  $arr = [
    "1" => "MSM",
    "2" => "TG",
    "3" => "MSW",
    "4" => "TG-SW",
    "5" => "FSW",
    "6" => "TGM",
    "7" => "NONE (non-msm trans)",
    "10" => "Male Non-KP",
    "11" => "Female Non-KP",
  ];

  $response = ["key" => null, "value" => null];
  $key = '1';

  if ($data == 'MSM') $key = '1';
  if ($data == 'TG') $key = '2';
  if ($data == 'MSW') $key = '3';
  if ($data == 'TGSW') $key = '4';
  if ($data == 'FSW') $key = '5';
  if ($data == 'TGM') $key = '6';
  // if($data == 'trans') $key = '7'; 
  if ($data == 'Male') $key = '10';
  if ($data == 'Female') $key = '11';

  $response = ["key" => $key, "value" => $arr[$key]];

  return $response;
}


function uicSplit($uic)
{

  $first_letter = substr($uic, 0, 3);
  $second_letter = substr($uic, 3, 3);
  $day_number = substr($uic, 6, 2);
  $month_number = substr($uic, 8, 2);
  $year_number = substr($uic, 10, 2);

  // if(mb_detect_encoding($uic) == "UTF-8") $first_letter = substr($uic, 3, 3);
  if (mb_detect_encoding($uic) == "ASCII") {
    $first_letter = strtoupper(substr($uic, 0, 1));
    $second_letter = strtoupper(substr($uic, 1, 1));
    $day_number = substr($uic, 2, 2);
    $month_number = substr($uic, 4, 2);
    $year_number = substr($uic, 6, 2);
  }

  $response = [
    'first_letter' => $first_letter,
    'second_letter' => $second_letter,
    'day_number' => $day_number,
    'month_number' => $month_number,
    'year_number' => $year_number
  ];

  return $response;
}

function eCascade_instance_id()
{

  $letters = '0123456789abcdef';

  return generateRandomString(8, $letters) . "-" . generateRandomString(4, $letters) . "-4" . generateRandomString(3, $letters) . "-" . generateRandomString(4, $letters) . "-" . generateRandomString(12, $letters);
}

function eCascade_new_condom_use_convert($data)
{

  $arr = [
    '1' => "Always",
    '2' => "Most of the time",
    '3' => "Sometimes",
    '4' => "Never"
  ];

  $response = ["key" => null, "value" => null];

  if ($data == 'ใช้ทุกครั้ง') $response = ["key" => array_search($arr['1'], $arr), "value" => $arr['1']];
  if ($data == 'ส่วนใหญ่ แต่ไม่ทุกครั้ง') $response = ["key" => array_search($arr['2'], $arr), "value" => $arr['2']];
  if ($data == 'ใช้เป็นบางครั้ง') $response = ["key" => array_search($arr['3'], $arr), "value" => $arr['3']];
  if ($data == 'ไม่เคยใช้เลย') $response = ["key" => array_search($arr['4'], $arr), "value" => $arr['4']];

  return $response;
}

function eCascade_hiv_result_convert($data)
{

  $arr = [
    '1' => "Negative",
    '2' => "Positive",
    '3' => "Inconclusive",
    '4' => "8",
    '9' => "Don't want to tell (A6)"
  ];

  if ($data == 'N') $response = ["key" => array_search($arr['1'], $arr), "value" => $arr['1']];
  if ($data == 'R') $response = ["key" => array_search($arr['2'], $arr), "value" => $arr['2']];
  if ($data == 'I') $response = ["key" => array_search($arr['3'], $arr), "value" => $arr['3']];

  return $response;
}

function eCascade_new_drug_use_convert($data)
{

  $arr = [
    "1" => "I never take drugs",
    "2" => "I take drugs that are injected with a needle",
    "3" => "I take drugs that are not injected",
    "4" => "I take drugs that are both injected and not injected",
    "5" => "No answer",
    "9" => "This was not asked",
  ];

  $response = ["key" => null, "value" => null];

  if ($data == 'ไม่เคยใช้') $response = ["key" => array_search($arr['1'], $arr), "value" => $arr['1']];
  if ($data == 'เคยใช้') $response = ["key" => array_search($arr['2'], $arr), "value" => $arr['2']];

  return $response;
}

function eCascade_uic_latin_convert($uic)
{

  $compare_arr = [
    "A" => "A",
    "B" => "B",
    "C" => "C",
    "D" => "D",
    "E" => "E",
    "F" => "F",
    "G" => "G",
    "H" => "H",
    "I" => "I",
    "J" => "J",
    "K" => "K",
    "L" => "L",
    "M" => "M",
    "N" => "N",
    "O" => "O",
    "P" => "P",
    "Q" => "Q",
    "R" => "R",
    "S" => "S",
    "T" => "T",
    "U" => "U",
    "V" => "V",
    "W" => "W",
    "X" => "X",
    "Y" => "Y",
    "Z" => "Z",
    "ก" => "a",
    "ข" => "b",
    // "ฃ" => "",
    "ค" => "c",
    // "ฅ" => "",
    "ฆ" => "d",
    "ง" => "e",
    "จ" => "f",
    "ฉ" => "g",
    "ช" => "h",
    "ซ" => "i",
    "ฌ" => "j",
    "ญ" => "k",
    "ฎ" => "l",
    "ฏ" => "m",
    "ฐ" => "n",
    "ฑ" => "o",
    "ฒ" => "p",
    "ณ" => "q",
    "ด" => "r",
    "ต" => "s",
    "ถ" => "t",
    "ท" => "u",
    "ธ" => "v",
    "น" => "w",
    "บ" => "x",
    "ป" => "y",
    "ผ" => "z",
    "ฝ" => "A",
    "พ" => "B",
    "ฟ" => "C",
    "ภ" => "D",
    "ม" => "E",
    "ย" => "F",
    "ร" => "G",
    "ฤ" => "G",
    "ล" => "H",
    "ว" => "I",
    "ศ" => "J",
    "ษ" => "K",
    "ส" => "L",
    "ห" => "M",
    "ฬ" => "N",
    "อ" => "O",
    "ฮ" => "P",
  ];

  $latin_uic = $compare_arr[uicSplit($uic)['first_letter']] . $compare_arr[uicSplit($uic)['second_letter']] . substr($uic, -6);

  $response = [
    'latin_uic' => $latin_uic,
    'first_letter' => $compare_arr[uicSplit($uic)['first_letter']],
    'second_letter' => $compare_arr[uicSplit($uic)['second_letter']],
    'day_number' => uicSplit($uic)['day_number'],
    'month_number' => uicSplit($uic)['month_number'],
    'year_number' => uicSplit($uic)['year_number']
  ];

  return $response;
}

function eCascade_username($site, $user_id)
{
  $site_code = [
    "caremat" => "01",
    "mplus_cmi" => "02",
    "mplus_cri" => "03",
    "mplus_plk" => "04",
    "rsat_bkk" => "05",
    "rsat_cbi" => "06",
    "rsat_ska" => "07",
    "rsat_ubn" => "08",
  ];

  $user_id_fill = sprintf("%03d", $user_id);

  $response = "77{$site_code[$site]}{$user_id_fill}";

  return $response;
}

function eCascade_deviceID($site)
{
  $site_code = [
    "caremat" => "CAREMAT",
    "mplus_cmi" => "MPLUSCMI",
    "mplus_cri" => "MPLUSCRI",
    "mplus_plk" => "MPLUSPLK",
    "rsat_bkk" => "RSATBKK",
    "rsat_cbi" => "RSATCBI",
    "rsat_ska" => "RSATSKA",
    "rsat_ubn" => "RSATUBN",
  ];


  $response = "ACTSE-{$site_code[$site]}";

  return $response;
}

function eCascade_org_code($data)
{

  $arr = [
    "1001" =>  "Mplus CM",
    "1002" =>  "Caremat",
    "1003" =>  "Sarapee",
    "1004" =>  "Piman",
    "1005" =>  "Nakornping",
    "1501" =>  "Mplus CR",
    "2001" =>  "RSAT BKK Central",
    "2002" =>  "SWING BKK",
    "2003" =>  "Bangkok Health Hub [songkran 2016]",
    "2004" =>  "Sathorn [songkran 2016]",
    "2005" =>  "SP Sathorn",
    "2006" =>  "SP Pulse",
    "2007" =>  "The Poz",
    "2008" =>  "Anon Clinic",
    "2009" =>  "Tangerine",
    "2010" =>  "RSAT Nonthaburi",
    "2011" =>  "RSAT Pathum Thani",
    "2012" =>  "RSAT Samut Prakan",
    "2013" =>  "SWING SKI",
    "2031" =>  "Pribta",
    "3001" =>  "SWING PATT",
    "3002" =>  "Sisters PATT",
    "3003" =>  "Banglamung",
    "3004" =>  "RSAT Chonburi",
    "4001" =>  "RSAT Hat Yai",
    "4002" =>  "Hat Yai Hospital",
    "5001" =>  "RSAT Ubon",
    "5002" =>  "Thawanghin Clinic",
    "5003" =>  "Sunpasit Hospital",
    "6001" =>  "Mplus PL",
    // "9999" => "ACTSE",
  ];

  $response = ["key" => null, "value" => null];

  // default caremat
  $response = ["key" => "1002", "value" => $arr["1002"]];

  if ($data == 'caremat') $response = ["key" => "1002", "value" => $arr["1002"]];
  if ($data == 'mplus_cmi') $response = ["key" => "1001", "value" => $arr["1001"]];
  if ($data == 'mplus_cri') $response = ["key" => "1501", "value" => $arr["1501"]];
  if ($data == 'mplus_plk') $response = ["key" => "6001", "value" => $arr["6001"]];
  if ($data == 'rsat_bkk') $response = ["key" => "2001", "value" => $arr["2001"]];
  if ($data == 'rsat_cbi') $response = ["key" => "3004", "value" => $arr["3004"]];
  if ($data == 'rsat_ska') $response = ["key" => "4001", "value" => $arr["4001"]];
  if ($data == 'rsat_ubn') $response = ["key" => "5001", "value" => $arr["5001"]];
  // if ($data == 'actse') $response = ["key" => "9999", "value" => $arr["9999"]];

  return $response;
}

function eCascade_retention_info($data, $data2 = null, $data3 = null)
{

  $arr = [
    "1" => "On ART",
    "2" => "Stopped ART",
    "3" => "LTFU > 90 days",
    "4" => "Deceased",
    "5" => "Initiate ART",
    "6" => "Not ART",
    "7" => "Physical checkup for ART",
    "8" => "Return to ART",
    "9" => "9",
  ];

  $response = ["key" => null, "value" => null];

  // if($data == 'Online') $response = ["key" => "1002", "value" => $arr["1002"]];
  // if($data == 'โทรศัพท์') $response = ["key" => "1002", "value" => $arr["1002"]];
  // if($data == 'พบปะ/เยี่ยมบ้าน') $response = ["key" => "1002", "value" => $arr["1002"]]; 
  // if($data == 'ได้ข้อมูลจากโรงพยาบาล') $response = ["key" => "1002", "value" => $arr["1002"]]; 
  if ($data == 'ติดตามไม่ได้เกิน 90 วัน') $response = ["key" => "3", "value" => $arr["3"]];
  if ($data == 'เคสเสียชีวิต') $response = ["key" => "4", "value" => $arr["4"]];
  if ($data == 'ส่งต่อให้หน่วยงานอื่นดูแลต่อ') $response = ["key" => "9", "value" => $arr["9"]];
  // if ($data == 'ปรับสถานะการส่งต่อ') $response = ["key" => "4", "value" => $arr["4"]];
  if ($data2 == 'ติดต่อได้ ได้รับข่าวการเสียชีวิต') $response = ["key" => "4", "value" => $arr["4"]];
  if (in_array($data2, ['ปิดเครื่อง/ ไม่มีสัญญาณ/ ไม่รับสาย', 'ไม่รับสาย/ ปิดเครื่อง/ ไม่มีสัญญาณ', 'อ่านข้อความ แต่ไม่ตอบกลับ', 'ไม่ตอบสนอง/ ไม่อ่านข้อความ', 'ไม่ตอบสนอง/ ไม่อ่าน', 'ติดต่อได้ เคสยังไม่ว่าง', 'ติดต่อได้ เคสไม่ต้องการให้ติดตามอีก', 'อ่าน แต่ไม่ตอบกลับ', 'เบอร์ปิด/ เบอร์โทรยกเลิก', 'เบอร์ผิด/ ผู้รับระบุไม่ใช่เคส'])) $response = ["key" => "9", "value" => $arr["9"]];
  if (in_array($data2, ['ติดต่อได้ เคสปฎิเสธการให้ข้อมูล', 'ตอบกลับข้อความ ไม่ให้ข้อมูล HIV'])) $response = ["key" => "9", "value" => $arr["9"]];

  if ($data3 == 'หยุดยา') $response = ["key" => "2", "value" => $arr["2"]];
  if (in_array($data3, ['กินยาครั้งแรก', 'กินยาครั้งแรกในชีวิต'])) $response = ["key" => "5", "value" => $arr["5"]];
  if ($data3 == 'ยังไม่ได้กินยา ARV') $response = ["key" => "6", "value" => $arr["6"]];
  if (in_array($data3, ['กินยาต่อเนื่อง', 'กินยาไม่ต่อเนื่อง', 'ปรับสูตรยา'])) $response = ["key" => "1", "value" => $arr["1"]];

  return $response;
}

function eCascade_detail_on_not_contacted($data = null)
{

  $arr = [
    "1" => "changed_number",
    "2" => "phone_is_off",
    "3" => "not_answer_phone",
    "5" => "answers_but_refuses_talk",
    "6" => "other",
  ];

  $response = ["key" => 6, "value" => $arr["6"]];

  if ($data == 'เบอร์ผิด/ ผู้รับระบุไม่ใช่เคส') $response = ["key" => "1", "value" => $arr["1"]];
  if (in_array($data, ['ปิดเครื่อง/ ไม่มีสัญญาณ/ ไม่รับสาย', 'ไม่รับสาย/ ปิดเครื่อง/ ไม่มีสัญญาณ', 'อ่านข้อความ แต่ไม่ตอบกลับ', 'ไม่ตอบสนอง/ ไม่อ่านข้อความ', 'ไม่ตอบสนอง/ ไม่อ่าน', 'อ่าน แต่ไม่ตอบกลับ'])) $response = ["key" => "2", "value" => $arr["2"]];
  // if($data == 'ปิดเครื่อง/ ไม่มีสัญญาณ/ ไม่รับสาย') $response = ["key" => "3", "value" => $arr["3"]]; 
  // if($data == 'เคสเสียชีวิต') $response = ["key" => "4", "value" => $arr["4"]]; 
  if (in_array($data, ['ติดต่อได้ เคสปฎิเสธการให้ข้อมูล', 'ตอบกลับข้อความ ไม่ให้ข้อมูล HIV', 'ติดต่อได้ เคสไม่ต้องการให้ติดตามอีก'])) $response = ["key" => "5", "value" => $arr["5"]];

  return $response;
}


function dataready($data)
{
  $data = trim($data);
  $data = stripslashes($data);
  $data = htmlspecialchars($data);
  return $data;

  // use
  // dataready($_POST['editor']);

  // decode for show
  // html_entity_decode($article_text);
}

function app_update_save_api($url, $api_token, $title, $body)
{

  $client = new Client();

  $targeturl = $url;
  $data = [
    'api_token' => $api_token,
    'title' => $title,
    'body' => $body
  ];
  $url = $targeturl . '?' . http_build_query($data);
  $response = $client->get($url, [
    'allow_redirects' => true,
  ]);
  $contents = $response->getBody()->getContents();
  $dataArr = json_decode($contents, true);
  // dump($data);
  // dump($dataArr);
  // return $dataArr;

  return $dataArr;
}

function crossSiteCheckFunction($url, $api_token, $uic)
{

  $client = new Client();

  $targeturl = $url;
  $data = [
    'api_token' => $api_token,
    'uic' => $uic,
  ];
  $url = $targeturl . '?' . http_build_query($data);
  $response = $client->get($url, [
    'allow_redirects' => true,
  ]);
  $contents = $response->getBody()->getContents();
  $dataArr = json_decode($contents, true);

  return $dataArr;
}

function fileExists($filePath)
{
  return is_file($filePath) && file_exists($filePath);
}

function fetchCareSupportReferral()
{
  global $conn;
  $stmt = $conn->prepare("SELECT 
  a.*,
  b.*
  FROM caresupport_activity a 
  LEFT JOIN caresupportdatabase b ON a.uic = b.uic
  WHERE 
  (b.hiv_refer_status = 'ส่งต่อ' OR 
  b.syphilis_refer_status = 'ส่งต่อ' OR 
  b.hcv_refer_status = 'ส่งต่อ' OR 
  b.ct_refer_status = 'ส่งต่อ' OR 
  b.ng_refer_status = 'ส่งต่อ' OR
  b.stis_refer_status = 'ส่งต่อ'
  ) ORDER by a.service_date DESC");
  $stmt->execute();

  return $stmt->fetchall(PDO::FETCH_ASSOC);
}

function ihri_check_pid($token = "adr3885fda5yuisag4", $uid, $kp, $project = "HORMONES", $type = false)
{
  if (isset($uid)) {

    // token: adr3885fda5yuisag4
    // uid: P20-11914
    // site: 1
    // projid: HORMONES
    // groupid: 1 // 1=TGW, 2=TGM

    // projid: SDART
    // groupid: 1=Prospective, 2=Retrospective

    // Site No IHRI Code Site Name
    // 01 RBK RSAT Bangkok
    // 02 SBK SWING Bangkok
    // 03 SPT SWING Pattaya
    // 04 STPT SISTER Pattaya
    // 05 CCM CAREMAT Chiang Mai
    // 06 RHY RSAT Songklha (Hat Yai)
    // 07 IHRI IHRI Bangkok
    // 09 MCM MPlus Chiangmai
    // 11 MCR MPlus Chiangrai
    // 12 RUB RSAT Ubonratchathani
    // 14 MPL MPlus Phitsanulok
    // 15 RCB RSAT Chonburi

    $clinic_id_lists = [
      "actse" => ["00", "ACTSE", "ACTSE"], // dummy will fail pid
      "rsat_bkk" => ["01", "RBK", "RSAT Bangkok"],
      "swing_bkk" => ["02", "SBK", "SWING Bangkok"],
      "swing_pty" => ["03", "SPT", "SWING Pattaya"],
      "sister_pty" => ["04", "STPT", "SISTER Pattaya"],
      "caremat" => ["05", "CCM", "CAREMAT Chiang Mai"],
      "rsat_ska" => ["06", "RHY", "RSAT Songklha (Hat Yai)"],
      "ihri" => ["07", "IHRI", "IHRI Bangkok"],
      "mplus_cmi" => ["09", "MCM", "MPlus Chiangmai"],
      "mplus_cri" => ["11", "MCR", "MPlus Chiangrai"],
      "rsat_ubn" => ["12", "RUB", "RSAT Ubonratchathani"],
      "mplus_plk" => ["14", "MPL", "MPlus Phitsanulok"],
      "rsat_cbi" => ["15", "RCB", "RSAT Chonburi"],
      // "mplus_nma" => ["16", "MKM", "MPlus Nakornratchasima"],
      // "swing_spk" => ["17", "SBK2", "SWING Sapankway"],
    ];

    $project_id_lists = [
      "HORMONES",
      "SDART"
    ];

    $group_id_lists = [
      "HORMONES" => ["1", "2"],
      "SDART" => ["1", "2"]
    ];

    $targeturl = "http://161.82.242.164/pribta21demo2/proj_api_getpid.php";
    if ($type) $targeturl = "http://161.82.242.164/pribta21/proj_api_getpid.php";

    $kp_select = 1;
    $project = "HORMONES";

    if (!in_array($kp, ['TGM', 'Female', 'FSW', 'Female', 'PWID-Female'])) $kp_select = 0;
    if (in_array($kp, ['TGM', 'Female', 'FSW', 'Female', 'PWID-Female'])) $kp_select = 1;

    $site = '';
    $site_code = '';
    $token = $token;
    $uid = $uid;
    $projid = $project; //HORMONES
    $groupid = $group_id_lists["HORMONES"][$kp_select];

    foreach ($clinic_id_lists as $key => $item) {
      if ($_SESSION['site_specific']->sitename14 == $key) {
        $site = $item[0];
        $site_code = $item[1];
      }
    }

    // temp site
    // $site = "2";
    // $groupid = "2";

    // http://161.82.242.164/weclinic/api/uic_center.php?citizen_id=1234123412342&fname=yxxxx&lname=xxxxx&clinic_id=RBK&dob=1985-02-12&phone_no=0855555555

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $targeturl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, [
      "token" => $token,
      "uid" => $uid,
      "site" => $site,
      "projid" => $projid,
      "groupid" => $groupid
    ]);
    $curl_response = curl_exec($ch);
    curl_close($ch);

    $uid_site = [
      "CCM" => "CAREMAT Chiang Mai",
      "MCM" => "MPlus Chiangmai",
      "MCR" => "MPlus Chiangrai",
      "MPL" => "MPlus Phitsanulok",
      "MKM" => "MPlus Nakhon Ratchasima",
      "RBK" => "RSAT Bangkok",
      "RCB" => "RSAT Chonburi",
      "RHY" => "RSAT Hadyai",
      "RUB" => "RSAT Ubonratchathani",
      "SBK" => "SWING Bangkok",
      "SBK2" => "SWING Saphan Khwai",
      "SPT" => "SWING Pattaya",
      "STPT" => "SISTER Pattaya"
    ];

    $data = json_decode($curl_response, true);

    return $data;

    // if ($data['found'] == 'Y') $data['uid_site'] = $uid_site[$data['clinic_id']];
    // if ($data['found'] == 'N') $data['uid_site'] = $uid_site[$site];

    // $response_code = [
    //   [
    //     "pid" => "$pid",
    //     "res" => 1,
    //     "msg_info" => "Create PID : $pid",
    //     "msg_err" => ""
    //   ],
    //   [
    //     "pid" => "$pid",
    //     "res" => 2,
    //     "site" => "$site_code",
    //     "msg_info" => "$uid already has PID: $pid in $site_code.",
    //     "msg_err" => ""
    //   ],
    //   [
    //     "pid" => "",
    //     "res" => 3,
    //     "nodata" => $query,
    //     "msg_info" => "",
    //     "msg_err" => "NO Data: Site $site is not found."
    //   ],
    //   [
    //     "pid" => "",
    //     "missing_param" => "$query,$query,$query,$query",
    //     "res" => 4,
    //     "msg_info" => "Missing parameter to create pid. [$query,$query,$query,$query]"
    //   ],
    //   [
    //     "pid" => "",
    //     "res" => 5,
    //     "msg_info" => "Invalid token"
    //   ],
    // ];

    // check
    // - 5 valid token
    // - 4 complete param => token, uid, site, projid, groupid
    // - 3 currect param value
    // - 2 exists PID
    // - 1 PID created

    // echo json_encode($dataArr);
    // echo json_encode($data);
  }
}

function checkIHRIUID($targeturl, $id, $fname, $lname, $clinic_id, $dob, $phone_no, $citizen_id)
{

  $data = [
    "id" => $id,
    "fname" => $fname,
    "lname" => $lname,
    "clinic_id" => $clinic_id,
    "dob" => $dob,
    "phone_no" => $phone_no,
    "citizen_id" => $citizen_id
  ];

  // dump($data);

  $ch = curl_init();
  curl_setopt($ch, CURLOPT_URL, $targeturl);
  curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
  curl_setopt($ch, CURLOPT_HEADER, 0);
  curl_setopt($ch, CURLOPT_POST, 1);
  curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
  $curl_response = curl_exec($ch);
  curl_close($ch);

  return $curl_response;
}

function checkIHRIUIDTest($targeturl, $data)
{
  $bearerToken = 'up71j6moDhOaOMLuTwOJAm1GefUtaPjDvtSg5kV1JJDfxOfwDTWA9cm37JiyfcUh';

  $ch = curl_init();
  curl_setopt($ch, CURLOPT_URL, $targeturl);
  curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
  curl_setopt($ch, CURLOPT_HEADER, 0);
  curl_setopt($ch, CURLOPT_POST, 1);
  curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));

  // Set the headers including the Authorization header with the Bearer token
  curl_setopt($ch, CURLOPT_HTTPHEADER, [
    "Authorization: Bearer $bearerToken",
    "Content-Type: application/x-www-form-urlencoded"
  ]);

  $curl_response = curl_exec($ch);
  curl_close($ch);

  if ($curl_response === false) {
    $error_msg = curl_error($ch);
    $error_no = curl_errno($ch);
    curl_close($ch);
    return "cURL error ($error_no): $error_msg";
  }

  return $curl_response;
}

function check_prep_request_api($site, $api_token = 'Oj70Oi376YJbmCL')
{

  $site_lists = [
    "caremat" => "https://caremat.actse-clinic.com/api/prep_request_api.php",
    "mplus_cmi" => "https://mplus-cmi.actse-clinic.com/api/prep_request_api.php",
    "mplus_cri" => "https://mplus-cri.actse-clinic.com/api/prep_request_api.php",
    "mplus_plk" => "https://mplus-plk.actse-clinic.com/api/prep_request_api.php",
    "rsat_bkk" => "https://rsat-bkk.actse-clinic.com/api/prep_request_api.php",
    "rsat_ska" => "https://rsat-ska.actse-clinic.com/api/prep_request_api.php",
    "rsat_ubn" => "https://rsat-ubn.actse-clinic.com/api/prep_request_api.php",
    "rsat_cbi" => "https://rsat-cbi.actse-clinic.com/api/prep_request_api.php",
  ];


  $client = new Client();

  $targeturl = $site_lists[$site];
  $data = [
    "site" => $site,
    "api_token" => $api_token
  ];

  $url = $targeturl . '?' . http_build_query($data);
  $response = $client->get($url, [
    'allow_redirects' => true,
  ]);
  $contents = $response->getBody()->getContents();
  $dataArr = json_decode($contents, true);
  // dump($data);
  // dump($dataArr);
  return $dataArr;
}

function fetch_prep_for_hospital($conn)
{

  $prep_stock_spending = prep_stock_spending($conn)->fetchall();
  $prep_refill_spending = prep_refill_spending($conn)->fetchall();

  // filter for prep_approve_location
  $prep_stock_spending = array_filter($prep_stock_spending, function ($item) {
    return $item->prep_approve_location == $_SESSION['prep_approve_location'];
  });

  $prep_refill_spending = array_filter($prep_refill_spending, function ($item) {
    return $item->prep_approve_location == $_SESSION['prep_approve_location'];
  });
  // filter for prep_approve_location

  $npep_stock_spending = npep_stock_spending()->fetchall();
  $sdart_stock_spending = sdart_stock_spending()->fetchall();

  $requestPrEP = 0;
  $requestnPEP = 0;
  $requestSDART = 0;
  $bloodtest = 0;

  foreach ($sdart_stock_spending as $item) {
    if ($item->update_status == 'ขออนุมัติยา') $requestSDART++;
    if ($item->update_status == 'ขออนุมัติยาหลังจากบันทึก Lab') $requestSDART++;
    // if ($item->update_status == 'แจ้งส่งเลือดตรวจ') $bloodtest++;
  }

  foreach ($npep_stock_spending as $item) {
    if ($item->update_status == 'ขออนุมัติยา') $requestnPEP++;
    if ($item->update_status == 'ขออนุมัติยาหลังจากบันทึก Lab') $requestnPEP++;
    // if ($item->update_status == 'แจ้งส่งเลือดตรวจ') $bloodtest++;
  }

  foreach ($prep_stock_spending as $item) {
    if ($item->update_status == 'ขออนุมัติยา') $requestPrEP++;
    if ($item->update_status == 'ขออนุมัติยาหลังจากบันทึก Lab') $requestPrEP++;
    if ($item->update_status == 'แจ้งส่งเลือดตรวจ') $bloodtest++;
  }

  foreach ($prep_refill_spending as $item) {
    if ($item->update_status == 'ขออนุมัติยา') $requestPrEP++;
    if ($item->update_status == 'ขออนุมัติยาหลังจากบันทึก Lab') $requestPrEP++;
    if ($item->update_status == 'แจ้งส่งเลือดตรวจ') $bloodtest++;
  }

  $response = [];
  $response['requestPrEP'] = $requestPrEP;
  $response['requestnPEP'] = $requestnPEP;
  $response['requestSDART'] = $requestSDART;
  $response['bloodtest'] = $bloodtest;

  return $response;
}

function arrays_are_equal($array1, $array2)
{
  array_multisort($array1);
  array_multisort($array2);
  return (serialize($array1) === serialize($array2));
}

function gf_nationality($nationality)
{
  if ($nationality == 'ไทย') return '1';
  if ($nationality == 'พม่า') return '2';
  if ($nationality == 'ลาว') return '3';
  if ($nationality == 'กัมพูชา') return '4';
  if ($nationality == 'เวียดนาม') return '6';
  return '5';
}

function sendSMS($phone_input, $header = "ACTSE", $text_input = "text")
{

  $phone = '66' . substr(str_replace('-', '', $phone_input), 1);
  if (substr($phone_input, 0, 2) == '66' && strlen($phone_input) == '11') $phone = $phone_input;

  if (strlen($phone) == 11) {

    $sms = new thsms();

    $sms->username   = 'actse';
    $sms->password   = 'xitgmLwmp76';

    $sms->send($header, $phone, $text_input);
  }
}

function dd_json($value)
{
  echo json_encode($value, JSON_UNESCAPED_UNICODE);
  die();
}

function logging($text, $filename = null)
{
  $now = date("Y-m-d H:i:s");

  $log = "{$now}\n{$text}\n####\n";

  $logDir = __DIR__ . '/../log/';
  $fileName = $filename ?? 'log_' . date("Ymd");
  $fileName .= '.txt';
  $file = $logDir . $fileName;

  // Ensure the log directory exists
  if (!is_dir($logDir)) {
    mkdir($logDir, 0777, true);
  }

  // If file doesn't exist, create and set permissions
  if (!file_exists($file)) {
    file_put_contents($file, ''); // create the file
    chmod($file, 0666); // read-write for owner, group, others
  }

  // Check if file is writable before writing
  if (is_writable($file)) {
    file_put_contents($file, $log, FILE_APPEND);
  } else {
    error_log("Logging error: Cannot write to file {$file}");
  }
}

function userHasRoles($roles)
{
  return isset($_SESSION['user_role'])
    && is_array($_SESSION['user_role'])
    && !empty(array_intersect((array)$roles, $_SESSION['user_role']));
}

function userHasCode($codes)
{
  return isset($_SESSION['user_code'])
    && in_array($_SESSION['user_code'], (array)$codes);
}

function userHasSiteName($siteNames, $siteName = null)
{
  $currentSiteName = $siteName ?? ($_SESSION['site_specific']->sitename14 ?? null);

  return isset($currentSiteName) && in_array($currentSiteName, (array)$siteNames);
}

function fetchHormonesByUIC($uic)
{
  global $conn;
  $stmt = $conn->prepare("SELECT 
    a.*,
    b.service_date as hr_service_date,
    b.first_hormones_date,
    b.first_estradiol,	
    b.first_testosterone,	
    b.last_hormones_date,	
    b.last_estradiol,	
    b.last_testosterone,	
    b.gender_reassignment,	
    b.hormones_used,	
    b.first_hormones_used_age,	
    b.first_hormones_role_model,	
    b.first_hormones_injection,	
    b.first_hormones_injection_staff,
    c.hormones_lab_date,
    c.hormones_estradiol,
    c.hormones_testosterone
            
    FROM (( module_hormones_history a
    INNER JOIN module_hormones_profile b ON a.uic = b.uic)
    INNER JOIN module_hormones_lab_result c ON a.uic = c.uic)
    
    WHERE a.uic = ?
    ");
  $stmt->execute([$uic]);

  return $stmt;
}

function on_goingColor($data)
{
  if ($data == 'yes') return labelbig('ยังกินอยู่', 'success');
  if ($data == 'no') return labelbig('หยุดกิน', 'danger');

  return labelbig('NA', 'default');
}
function drugListCheckLable($drug_code, $drug_name_else)
{
  $hormones_drug_lists = [
    // Estrogen - Progesterone comblnation นาคุมกำเนิด
    ["code" => "10", "name" => "EE + cyproterone (ใดแอน, ซุซี,พรีม,มีน่า)"],
    ["code" => "11", "name" => "EE + levonorgestrel (อเลซซ่า,แอนนา)"],
    ["code" => "12", "name" => "EE + Drospirenone (ยาสมิน,ยาส)"],
    ["code" => "13", "name" => "EE + Gestodene (เฟโมตีน,เมลลินอน)"],
    ["code" => "14", "name" => "EE + Norethisterone (นอริมิน)"],
    ["code" => "15", "name" => "EE + Norgestimate (ดีซึนนา)"],
    ["code" => "16", "name" => "EE + Norelgestromin (อึฟร่า)"],
    ["code" => "17", "name" => "EE + Desogestrel (อเลนโวนา,มาวีลอน)"],
    // (*EE - ethinyl estradiol เอเอสตราไดออล)
    // Estrogen ฮอร์มนเพศมกุจ)
    ["code" => "23", "name" => "Estradiol valerate (โปรดีโนวา)"],
    ["code" => "24", "name" => "Estradiol hemihydrate (แอคทิเวล,เอสโดรเฟรม)"],
    ["code" => "25", "name" => "Estradiol hemihydrate gel (ดิวิเจล)"],
    ["code" => "26", "name" => "17 beta estradiol gel (เอสโตรเจล)"],
    ["code" => "27", "name" => "Estrogen conjugate (พรีมาริน,เอสโดรมอน)"],
    ["code" => "28", "name" => "Depo estradiol valerate (โปรคืนอน เตโป)"],
    ["code" => "29", "name" => "Estradiol cypionate (เอสตราไดออลไซบีโอเนท)"],
    ["code" => "30", "name" => "Ethinyl estradiol (โปรวีโรนัม โดย ไซดัส)"],
    ["code" => "31", "name" => "Ethinyl estradiol (เอททีนอร์ม เคโป)"],
    // Anti-androgen ยาด้านฮอร์โมนเพศขาย)
    ["code" => "32", "name" => "Cyproterone (แอนโดรคัวร์,ไซโปรสแดท)"],
    ["code" => "36", "name" => "Flutamide (ฟลูลัม,ฟีเจอเรล)"],
    ["code" => "37", "name" => "Nilutamide (อีโคโซน)"],
    ["code" => "38", "name" => "Bicalutamide (ตาโซเด็ก)"],
    ["code" => "39", "name" => "Spironolactone (แอลแด็กโตน, ไฮเลส)"],
    ["code" => "40", "name" => "GnRH analogues (เอลิการ์ด, อีบันโทน)"],
    ["code" => "41", "name" => "Finasteride (ฮาริฟิน, โพรพิเซีย,ฟิไรด์,โพรสการ์,สเตอร์เข็ย, สเตอร์ซาร์)"],
    // Progesterone (ฮอร์มนเพศหญิง)
    ["code" => "33", "name" => "Medroxyprogesterone (เมดรอกข์โปรเจสเทอโรu)"],
    ["code" => "34", "name" => "Levonorgestrel (แพลน มี)"],
    ["code" => "35", "name" => "Depo progesterone hexanoate (โปรคูตอน)"],
    // Testosterone ฮอร์โมนเพศชาย)
    ["code" => "45", "name" => "T.enanthate (เดโปเทส 250,เทสโทริรอน)"],
    ["code" => "46", "name" => "T.undecanoate (เนมิโด)"],
    ["code" => "47", "name" => "T.propionate (มาร์ช)"],
    ["code" => "48", "name" => "Testosterone (แอนดรอย เทสโทแคป)"],
    ["code" => "49", "name" => "T-gel (นอนโดรเจล)"],
    ["code" => "50", "name" => "Mesterolone (โปรวีโรนัม โดย เชอริง)"],
    ["code" => "99", "name" => "ฮอร์โมนชนิดอื่นๆ"],
    ["code" => "00", "name" => "ยาที่ไม่ใช่ ฮอร์โมน"],
  ];

  $response = '';
  foreach ($hormones_drug_lists as $item) {
    if ($item['code'] == $drug_code) $response = $item['name'];
  }
  if ($drug_code == '99' || $drug_code == '00') $response = $drug_name_else;
  return $response;
}

function routeListCheckLabel($route, $route_else)
{

  $hormones_route_lists = [
    ["code" => "01", "eng" => "PO", "thai" => "ทางปาก", "label" => "ทางปาก (PO)"],
    ["code" => "02", "eng" => "IV", "thai" => "ฉีดยาเข้าทางหลอดเลือดดำ", "label" => "ฉีดยาเข้าทางหลอดเลือดดำ (IV)"],
    ["code" => "03", "eng" => "IM", "thai" => "ฉีดยาเข้าชั้นกล้ามเนื้ units", "label" => "ฉีดยาเข้าชั้นกล้ามเนื้อ (IM)"],
    ["code" => "04", "eng" => "Topical", "thai" => "ทางผิวหนัง", "label" => "ทางผิวหนัง (Topical)"],
    ["code" => "05", "eng" => "Rectal", "thai" => "เหน็บทางทวารหนัก", "label" => "เหน็บทางทวารหนัก (Rectal)"],
    ["code" => "06", "eng" => "Inhale", "thai" => "ทางการสูดดม", "label" => "ทางการสูดดม (Inhale)"],
    ["code" => "07", "eng" => "SQ", "thai" => "ฉีดยาเข้าชั้นใต้ผิวหนัง", "label" => "ฉีดยาเข้าชั้นใต้ผิวหนัง (SQ)"],
    ["code" => "08", "eng" => "Other", "thai" => "อื่นๆ", "label" => "อื่นๆ (Other)"],
  ];

  $response = 'NA';

  foreach ($hormones_route_lists as $item) {
    if ($item['eng'] == $route) $response = $item['label'];
  }

  if ($route == 'Other') $response = $route_else;

  return $response;
}

function doseListCheckLabel($dose, $dose_else)
{
  $hormones_dose_lists = [
    ["code" => "01", "eng" => "mg", "thai" => "มิลลิกรัม", "label" => "มิลลิกรัม (mg)"],
    ["code" => "02", "eng" => "U", "thai" => "หน่วย", "label" => "หน่วย (U)"],
    ["code" => "03", "eng" => "MU", "thai" => "Million units", "label" => "Million units (MU)"],
    ["code" => "04", "eng" => "mcg", "thai" => "ไมโครกรัม", "label" => "ไมโครกรัม (mcg)"],
    ["code" => "05", "eng" => "CAP", "thai" => "แคปซูล", "label" => "แคปซูล (CAP)"],
    ["code" => "06", "eng" => "ml", "thai" => "มิลลิลิตรหรือซีซี", "label" => "มิลลิลิตรหรือซีซี (ml)"],
    ["code" => "07", "eng" => "TAB", "thai" => "เม็ด", "label" => "เม็ด (TAB)"],
    ["code" => "08", "eng" => "G", "thai" => "กรัม", "label" => "กรัม (G)"],
    ["code" => "09", "eng" => "mEg", "thai" => "มิลลิอิควิวาเลนท์", "label" => "มิลลิอิควิวาเลนท์ (mEg)"],
    ["code" => "10", "eng" => "Gtt", "thai" => "หยด", "label" => "หยด (Gtt)"],
    ["code" => "11", "eng" => "IU", "thai" => "หน่วยสากล", "label" => "หน่วยสากล (IU)"],
    ["code" => "12", "eng" => "Amp", "thai" => "หลอด", "label" => "หลอด (Amp)"],
    ["code" => "13", "eng" => "Other", "thai" => "อื่นๆ", "label" => "อื่นๆ (Other)"],
  ];

  $response = "NA";
  foreach ($hormones_dose_lists as $item) {
    if ($item['eng'] == $dose) $response = $item['label'];
  }
  if ($dose == 'Other') $response = $dose_else;

  return $response;
}

function frequencyListCheckLable($frequency, $frequency_else)
{
  $hormones_frequency_lists = [
    ["code" => "01", "eng" => "PRN", "thai" => "ตามต้องการ", "label" => "ตามต้องการ (PRN)"],
    ["code" => "02", "eng" => "Single dose", "thai" => "ครั้งเดียว", "label" => "ครั้งเดียว (Single dose)"],
    ["code" => "03", "eng" => "OD", "thai" => "วันละครั้ง", "label" => "วันละครั้ง (OD)"],
    ["code" => "04", "eng" => "HS", "thai" => "ก่อนนอน", "label" => "ก่อนนอน (HS)"],
    ["code" => "05", "eng" => "BID", "thai" => "วันละ 2 ครั้ง", "label" => "วันละ 2 ครั้ง (BID)"],
    ["code" => "06", "eng" => "TID", "thai" => "วันละ 3 ครั้ง", "label" => "วันละ 3 ครั้ง (TID)"],
    ["code" => "07", "eng" => "QID", "thai" => "วันละ 4 ครั้ง", "label" => "วันละ 4 ครั้ง (QID)"],
    ["code" => "08", "eng" => "Weekly", "thai" => "สัปดาห์ละวัน", "label" => "สัปดาห์ละวัน (Weekly)"],
    ["code" => "09", "eng" => "Bi-Weekly", "thai" => "สัปดาห์เว้นสัปดาห์", "label" => "สัปดาห์เว้นสัปดาห์ (Bi-Weekly)"],
    ["code" => "10", "eng" => "Monthly", "thai" => "เตือนละครั้ง", "label" => "เตือนละครั้ง (Monthly)"],
    ["code" => "11", "eng" => "Other", "thai" => "อื่นๆ", "label" => "อื่นๆ (Other)"],
  ];

  $response = 'NA';
  foreach ($hormones_frequency_lists as $item) {
    if ($item['eng'] == $frequency) $response = $item['label'];
  }

  if ($frequency == 'Other') $response = $frequency_else;

  return $response;
}

function stop_reasonListCheck($stop_reason, $stop_reason_else)
{
  $hormones_stop_reason_lists = [
    ["code" => "01", "eng" => "Side effects", "thai" => "อาการผิดปกติจากการใช้ฮอร์โมน", "label" => "Side effects (อาการผิดปกติจากการใช้ฮอร์โมน) กรุณาระบุอาการในหมายเหตุ"],
    ["code" => "02", "eng" => "Did not have desired effects", "thai" => "ไม่พึ่งพอใจกับผลการเปลี่ยนแปลง", "label" => "Did not have desired effects (ไม่พึ่งพอใจกับผลการเปลี่ยนแปลง)"],
    ["code" => "03", "eng" => "Inconvenient", "thai" => "ใช้ยากไม่สะดวกใช้", "label" => "Inconvenient (ใช้ยาก/ไม่สะดวกใช้)"],
    ["code" => "04", "eng" => "Financial concerns", "thai" => "ปัญหาค่าใช้จ่าย", "label" => "Financial concerns (ปัญหาค่าใช้จ่าย)"],
    ["code" => "05", "eng" => "Go to surgery such as sex reassignment surgory", "thai" => "ผ่าตัด", "label" => "Go to surgery such as sex reassignment surgory (ผ่าตัด)"],
    ["code" => "06", "eng" => "Physician prescription", "thai" => "แพทส์สั่ง", "label" => "Physician prescription (แพทส์สั่ง)"],
    ["code" => "07", "eng" => "Other", "thai" => "อื่นๆ", "label" => "Other (อื่นๆ) กรุณาระบุอาการในหมายเหตุ **"],
    ["code" => "08", "eng" => "No reason", "thai" => "ไม่มีเหตุผล", "label" => "No reason (ไม่มีเหตุผล)"],
  ];

  $response = 'NA';
  foreach ($hormones_stop_reason_lists as $item) {
    if ($item['eng'] == $stop_reason) $response = $item['label'];
  }

  if ($stop_reason == 'No reason') $response = $stop_reason_else;

  return $response;
}

function getFilesInFolders($folder_path, $type = ['png', 'peg', 'jpg', 'gif'])
{

  $files = scandir($folder_path, 1);

  $response = [];

  $response['files_name'] = [];
  $response['files_path'] = [];

  foreach ($files as $item) {
    if (in_array(substr($item, -3), ['png', 'peg', 'jpg'])) {
      $response['files_name'][] = $item;
      $response['files_path'][] = "{$folder_path}/{$item}";
    }
  }

  return $response;
}

function validateDate($date, $format = 'Y-m-d')
{
  $d = DateTime::createFromFormat($format, $date);
  // The Y ( 4 digits year ) returns TRUE for any integer with any number of digits so changing the comparison from == to === fixes the issue.
  return $d && $d->format($format) === $date;
}

function fetchLabWaitResultVL($clinic_id = NULL, $startDate, $endDate)
{
  global $conn;

  $data = [$startDate, $endDate];
  // $where = "WHERE a.services like '%ตรวจ HIV-VL%' AND a.created_at >= ? AND a.created_at < ?";
  $where = "WHERE a.vl_result = 'ผล VL ยังไม่ออก' AND a.created_at >= ? AND a.created_at < ?";

  // if ($clinic_id) {
  $data = [$startDate, $endDate];

  //   $where .= " AND a.clinic_id = ?";
  // }

  $stmt = $conn->prepare("SELECT
    a.*,
    c.serviceProvide,
    c.created_at, 
    c.preComment,
    b.clinicQueue as clinic_queue,
    b.uid_code as uid,
    b.phone1 as phone,
    b.nickname,
    d.lab_topic,
    d.lab_date,	
    d.lab_result_vl,	
    d.lab_result_vl_value,	
    d.lab_report_staff,
    d.lab_report_time,
    d.lab_confirm_staff,
    d.lab_confirm_time,
    d.lab_files,
    d.lab_remarks,
    d.lab_status
  FROM cliniclabresult a
  LEFT JOIN clinicnewcase b ON a.clinic_id = b.clinic_id AND a.queue = b.queue AND a.uic = b.uic
  LEFT JOIN clinicprecounseling c ON a.clinic_id = c.clinic_id AND a.queue = c.queue AND a.uic = c.uic
  LEFT JOIN late_lab_result d ON a.clinic_id = d.clinic_id AND a.queue = d.queue AND a.uic = d.uic AND lab_topic = 'HIV-VL'
  $where");
  $stmt->execute($data);

  return $stmt;
}

function fetchLabWaitResultElse($clinic_id = NULL, $startDate, $endDate)
{
  global $conn;

  $data = [$startDate, $endDate];
  // $where = "WHERE a.services like '%ตรวจ HIV-VL%' AND a.created_at >= ? AND a.created_at < ?";
  $where = "WHERE a.created_at >= ? AND a.created_at < ?";

  // if ($clinic_id) {
  $data = [$startDate, $endDate];

  $stmt = $conn->prepare("SELECT
    a.*,
    c.serviceProvide,
    c.created_at, 
    c.preComment,
    b.clinicQueue as clinic_queue,
    b.uid_code as uid,
    b.phone1 as phone,
    b.nickname,
    d.lab_topic,
    d.lab_date,	
    d.lab_report_staff,
    d.lab_files,
    d.lab_remarks,
    d.lab_status
  FROM cliniclabresult a
  LEFT JOIN clinicnewcase b ON a.clinic_id = b.clinic_id AND a.queue = b.queue AND a.uic = b.uic
  LEFT JOIN clinicprecounseling c ON a.clinic_id = c.clinic_id AND a.queue = c.queue AND a.uic = c.uic
  LEFT JOIN late_lab_result d ON a.clinic_id = d.clinic_id AND a.queue = d.queue AND a.uic = d.uic AND lab_topic = 'ELSE'
  $where");
  $stmt->execute($data);

  return $stmt;
}

function fetchLabWaitResultCD4($clinic_id = NULL, $startDate, $endDate)
{
  global $conn;

  $data = [$startDate, $endDate];
  $where = "WHERE a.cd4_result = 'ผล CD4 ยังไม่ออก' AND a.created_at >= ? AND a.created_at < ?";

  // if ($clinic_id) {
  $data = [$startDate, $endDate];

  //   $where .= " AND a.clinic_id = ?";
  // }

  $stmt = $conn->prepare("SELECT
    a.*,
    c.serviceProvide,
    c.created_at, 
    c.preComment,
    b.clinicQueue as clinic_queue,
    b.uid_code as uid,
    b.phone1 as phone,
    b.nickname,
    d.lab_topic,
    d.lab_date,	
    d.lab_result_cd4,	
    d.lab_result_cd4_value,	
    d.lab_result_cd4_percent,	
    d.lab_report_staff,
    d.lab_report_time,
    d.lab_confirm_staff,
    d.lab_confirm_time,
    d.lab_files,
    d.lab_remarks,
    d.lab_status
  FROM cliniclabresult a
  LEFT JOIN clinicnewcase b ON a.clinic_id = b.clinic_id AND a.queue = b.queue AND a.uic = b.uic
  LEFT JOIN clinicprecounseling c ON a.clinic_id = c.clinic_id AND a.queue = c.queue AND a.uic = c.uic
  LEFT JOIN late_lab_result d ON a.clinic_id = d.clinic_id AND a.queue = d.queue AND a.uic = d.uic AND lab_topic = 'CD4'
  $where");
  $stmt->execute($data);

  return $stmt;
}

function Operationtime($item)
{

  $reg_duration = $item->reg_started && $item->bra_submitted ? time_diff($item->reg_started, $item->bra_submitted) : 0;
  $pre_duration = $item->pre_started && $item->pre_submitted ? time_diff($item->pre_started, $item->pre_submitted) : 0;
  $lab_duration = $item->lab_started && $item->lab_confirmed ? time_diff($item->lab_started, $item->lab_confirmed) : 0;
  $post_duration = $item->post_started && $item->post_submitted ? time_diff($item->post_started, $item->post_submitted) : 0;
  $gabreg_pre = $item->bra_submitted && $item->pre_started ? time_diff($item->bra_submitted, $item->pre_started) : 0;
  $gabpre_lab = $item->pre_submitted && $item->lab_started ? time_diff($item->pre_submitted, $item->lab_started) : 0;
  $gablab_post = $item->lab_confirmed && $item->post_started ? time_diff($item->lab_confirmed, $item->post_started) : 0;
  $total_duration = $item->reg_started && $item->post_submitted ? time_diff($item->reg_started, $item->post_submitted) : 0;

  // $operation = [
  //   "reg_duration" => "",
  //   "pre_duration" => "",
  //   "lab_duration" => "",
  //   "post_duration" => "",
  //   "gab_reg_pre" => "",
  //   "gab_pre_lab" => "",
  //   "gab_lab_post" => "",
  //   "total" => ""
  // ];

  $hour = 60;
  $day = 24;

  $reg_sec = $reg_duration % 60;
  $reg_min = floor($reg_duration / 60);
  $reg_hour = floor($reg_min / $hour);
  $reg_day = floor($reg_hour / $day);
  $reg_hour_min = floor($reg_min) - ($hour * $reg_hour);
  $reg_day_hour = floor($reg_hour) - ($day * $reg_day);

  $pre_sec = $pre_duration % 60;
  $pre_min = floor($pre_duration / 60);
  $pre_hour = floor($pre_min / $hour);
  $pre_day = floor($pre_hour / $day);
  $pre_hour_min = floor($pre_min) - ($pre_hour * $hour);
  $pre_day_hour = floor($pre_hour) - ($pre_day * $day);

  $lab_sec = $lab_duration % 60;
  $lab_min = floor($lab_duration / 60);
  $lab_hour = floor($lab_min / 60);
  $lab_day = floor($lab_hour / $day);
  $lab_hour_min = floor($lab_min) - ($lab_hour * $hour);
  $lab_day_hour = floor($lab_hour) - ($lab_day * $day);

  $post_sec = $post_duration % 60;
  $post_min = floor($post_duration / 60);
  $post_hour = floor($post_min / 60);
  $post_day = floor($post_hour / $day);
  $post_hour_min = floor($post_min) - ($post_hour * 60);
  $post_day_hour = floor($post_hour) - ($post_day * 24);

  $total_sec = $total_duration % 60;
  $total_min = floor($total_duration / 60);
  $total_hour = floor($total_min / $hour);
  $total_day = floor($total_hour / $day);
  $total_hour_min = floor($total_min) - ($total_hour * $hour);
  $total_day_hour = floor($total_hour) - ($total_day * $day);

  $gab_reg_pre_sec = $gabreg_pre % 60;
  $gab_reg_pre_min = floor($gabreg_pre / 60);
  $gab_reg_pre_hour = floor($gab_reg_pre_min / $hour);
  $gab_reg_pre_day = floor($gab_reg_pre_hour / $day);

  $gab_reg_pre_hour_min = floor($gab_reg_pre_min) - ($gab_reg_pre_hour * $hour);
  $gab_reg_pre_day_hour = floor($gab_reg_pre_hour) - ($gab_reg_pre_day * $day);

  $gab_pre_lab_sec = $gabpre_lab % 60;
  $gab_pre_lab_min = floor($gabpre_lab / 60);
  $gab_pre_lab_hour = floor($gab_pre_lab_min / $hour);
  $gab_pre_lab_day = floor($gab_pre_lab_hour / $day);

  $gab_pre_lab_hour_min = floor($gab_pre_lab_min) - ($gab_pre_lab_hour * $hour);
  $gab_pre_lab_day_hour = floor($gab_pre_lab_hour) - ($gab_pre_lab_day * $day);

  $gab_lab_post_sec = $gablab_post % 60;
  $gab_lab_post_min = floor($gablab_post / 60);
  $gab_lab_post_hour = floor($gab_lab_post_min / $hour);
  $gab_lab_post_day = floor($gab_lab_post_hour / $day);
  $gab_lab_post_hour_min = floor($gab_lab_post_min) - ($gab_lab_post_hour * $hour);
  $gab_lab_post_day_hour = floor($gab_lab_post_hour) - ($gab_lab_post_day * $day);

  $reg_duration = "";
  if ($reg_day > 0) $pre_duration = "$reg_day วัน ";
  if ($reg_day_hour > 0) $reg_duration = "$reg_day_hour ชั่วโมง ";
  if ($reg_hour_min > 0) $reg_duration .= "$reg_hour_min นาที ";
  if ($reg_sec > 0) $reg_duration .= "$reg_sec วินาที";

  $pre_duration = "";
  if ($pre_day > 0) $pre_duration = "$pre_day วัน ";
  if ($pre_day_hour > 0) $pre_duration .= "$pre_day_hour ชั่วโมง ";
  if ($pre_hour_min > 0) $pre_duration .= "$pre_hour_min นาที ";
  if ($pre_sec > 0) $pre_duration .= "$pre_sec วินาที";

  $lab_duration = "";
  if ($lab_day > 0) $lab_duration = "$lab_day วัน ";
  if ($lab_day_hour > 0) $lab_duration .= "$lab_day_hour ชั่วโมง ";
  if ($lab_hour_min > 0) $lab_duration .= "$lab_hour_min นาที ";
  if ($lab_sec > 0) $lab_duration .= "$lab_sec วินาที";

  $post_duration = "";
  if ($post_day > 0) $post_duration = "$post_day วัน ";
  if ($post_day_hour > 0) $post_duration .= "$post_day_hour ชั่วโมง ";
  if ($post_hour_min > 0) $post_duration .= "$post_hour_min นาที ";
  if ($post_sec > 0) $post_duration .= "$post_sec วินาที";

  $gab_reg_pre = "";
  if ($gab_reg_pre_day > 0) $gab_reg_pre = "$gab_reg_pre_day วัน ";
  if ($gab_reg_pre_day_hour > 0) $gab_reg_pre .= "$gab_reg_pre_day_hour ชั่วโมง ";
  if ($gab_reg_pre_hour_min > 0) $gab_reg_pre .= "$gab_reg_pre_hour_min นาที ";
  if ($gab_reg_pre_sec > 0) $gab_reg_pre .= "$gab_reg_pre_sec วินาที";

  $gab_pre_lab = "";
  if ($gab_pre_lab_day > 0) $gab_pre_lab = "$gab_pre_lab_day วัน ";
  if ($gab_pre_lab_day_hour > 0) $gab_pre_lab .= "$gab_pre_lab_day_hour ชั่วโมง ";
  if ($gab_pre_lab_hour_min > 0) $gab_pre_lab .= "$gab_pre_lab_hour_min นาที ";
  if ($gab_pre_lab_sec > 0) $gab_pre_lab .= "$gab_pre_lab_sec วินาที";

  $gab_lab_post = "";
  if ($gab_lab_post_day > 0) $gab_lab_post = "$gab_lab_post_day วัน ";
  if ($gab_lab_post_day_hour > 0) $gab_lab_post .= "$gab_lab_post_day_hour ชั่วโมง ";
  if ($gab_lab_post_hour_min > 0) $gab_lab_post .= "$gab_lab_post_hour_min นาที ";
  if ($gab_lab_post_sec > 0) $gab_lab_post .= "$gab_lab_post_sec วินาที";

  $total = "";
  if ($total_day > 0) $total = "$total_day วัน ";
  if ($total_day_hour > 0) $total .= "$total_day_hour ชั่วโมง ";
  if ($total_hour_min > 0) $total .= "$total_hour_min นาที ";
  if ($total_sec > 0) $total .= "$total_sec วินาที";

  $operation = [

    "reg_duration" => $reg_duration,
    "pre_duration" => $pre_duration,
    "lab_duration" => $lab_duration,
    "post_duration" => $post_duration,
    "gab_reg_pre" => $gab_reg_pre,
    "gab_pre_lab" => $gab_pre_lab,
    "gab_lab_post" => $gab_lab_post,
    "total" => $total

  ];

  return $operation;
}

function findTablesWithColumn($db_name, $col_name)
{
  global $conn;

  $stmt = $conn->prepare("SELECT TABLE_NAME
FROM INFORMATION_SCHEMA.COLUMNS
WHERE COLUMN_NAME = ?
AND TABLE_SCHEMA = ?;");

  $stmt->execute([$col_name, $db_name]);

  $data = $stmt->fetchAll();

  $table_list = array_map(function ($item) {
    return $item->TABLE_NAME;
  }, $data);

  return $table_list;
}

function changeDatabaseUIC(
  $database,
  $uic_old,
  $uic_new,
  $change_reason,
  $firstname_old = null,
  $lastname_old = null,
  $nickname_old = null,
  $firstname_new = null,
  $lastname_new = null,
  $nickname_new = null,
  $id_card = null,
  $change_date = null
) {
  global $conn;

  if ($uic_old === $uic_new) {
    return ['total' => 0, 'details' => [], 'note' => 'uic_old equals uic_new; no-op'];
  }

  $col_name = 'uic';
  $count_col = 'count';

  $table_lists = findTablesWithColumn($database, $col_name);
  $count_lists = findTablesWithColumn($database, $count_col);

  // ตารางพิเศษที่ใช้ชื่อคอลัมน์ UIC ต่างไป
  $specialMap = [
    'esignature'     => ['esig_uic'],
    'index_partner'  => ['seed_uic'],
    'indextesting'   => ['indexUIC'],
  ];

  // รวมรายการตาราง
  $table_lists = array_unique(array_merge($table_lists, array_keys($specialMap)));

  $details = [];
  $recountDetails = [];
  $totalAffected = 0;

  // ใช้ transaction ครอบทั้งหมด
  $conn->beginTransaction();
  try {
    foreach ($table_lists as $table) {
      $affected = 0;

      if (isset($specialMap[$table])) {
        foreach ($specialMap[$table] as $col) {
          $affected += updateDatabase($table, [$col => $uic_new], [$col => $uic_old]); // ควรเป็น prepared ภายใน
        }
      } else {
        $affected += updateDatabase($table, [$col_name => $uic_new], [$col_name => $uic_old]);
      }

      // กรณีพิเศษ: บันทึก old_uic ไว้ในแถวที่เพิ่งเปลี่ยน
      if ($table === 'caresupportdatabase') {
        updateDatabase($table, ['old_uic' => $uic_old], [$col_name => $uic_new]);
      }
      if ($table === 'clinic') {
        updateDatabase($table, ['old_uic' => $uic_old], [$col_name => $uic_new]);
      }

      // Re-number คอลัมน์ count (ถ้าตารางนั้นมีคอลัมน์ count)
      if (in_array($table, $count_lists, true)) {
        $key_col = ($table === 'clinic') ? 'sn' : 'id';
        $stmt = selectDatabase(
          $table,
          [$key_col],
          [$col_name => ['=', $uic_new]],
          [$key_col => 'ASC']
        );
        $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $i = 1;
        foreach ($rows as $r) {
          updateDatabase($table, ['count' => $i++], [$key_col => $r[$key_col]]);
        }
        $recountDetails[] = ['table' => $table, 'rows' => count($rows)];
      }

      $details[] = ['table' => $table, 'affected' => $affected];
      $totalAffected += $affected;
    }

    // ecascade_a5: สองคอลัมน์
    $affected_ecascade = updateDatabase(
      'ecascade_a5',
      ['loadname' => $uic_new, 'info_case_name' => $uic_new],
      ['loadname' => $uic_old]
    );
    $details[] = ['table' => 'ecascade_a5', 'affected' => $affected_ecascade];
    $totalAffected += $affected_ecascade;

    // UPDATE ที่มี REPLACE() — ใช้ placeholders ให้ครบ
    $sql = "UPDATE prep_hospital_request
            SET input_id = REPLACE(input_id, ?, ?)
            WHERE uic = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$uic_old, $uic_new, $uic_new]);
    $details[] = ['table' => 'prep_hospital_request(REPLACE)', 'affected' => $stmt->rowCount()];
    $totalAffected += $stmt->rowCount();

    $sql = "UPDATE prep_record_lab
            SET input_id = REPLACE(input_id, ?, ?)
            WHERE uic = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$uic_old, $uic_new, $uic_new]);
    $details[] = ['table' => 'prep_record_lab(REPLACE)', 'affected' => $stmt->rowCount()];
    $totalAffected += $stmt->rowCount();

    // log
    $cbs = $_SESSION['user'] ?? null;
    $change_date = $change_date ?? date("Y-m-d H:i:s");

    if ($totalAffected > 0) {
      insertDatabase('uic_change_log', [
        'cbs'           => $cbs,
        'uic_old'       => $uic_old,
        'uic_new'       => $uic_new,
        'change_reason' => $change_reason,
        'firstname_old' => $firstname_old,
        'lastname_old'  => $lastname_old,
        'nickname_old'  => $nickname_old,
        'firstname_new' => $firstname_new,
        'lastname_new'  => $lastname_new,
        'nickname_new'  => $nickname_new,
        'id_card'       => $id_card,
        'change_date'   => $change_date,
      ]);
    }

    $conn->commit();

    return [
      'total' => $totalAffected,
      'details' => $details,
      'recounted' => $recountDetails,
    ];
  } catch (Throwable $e) {
    $conn->rollBack();
    // คุณอาจ log error ตรงนี้
    return [
      'total' => 0,
      'error' => true,
      'message' => $e->getMessage(),
      'details' => $details,
    ];
  }
}

function PreventionStockLists($active = false, $lists_stock = false)
{
  global $conn;

  $is_active = '';
  $lists_stock_condition = '';

  if ($active) $is_active = "AND a.active = 1";

  // if ($lists_stock) $lists_stock_condition = "AND a.type = 'PrEP'";

  $query = "SELECT

  a.*,

  d.lot_number as stock_lot, 
  sum(d.sum) as stock_spend,
  (a.received_value - coalesce(sum(d.sum), 0)) as stock_left

  FROM prevention_stock a 
  LEFT JOIN (SELECT id, lot_number, stock_method, sum(received_value) as sum
    FROM `prevention_stock`
    WHERE stock_method = 'จ่ายออก'
    group by lot_number
    order by sum, id) d ON d.lot_number = concat(a.lot_number, ':', a.id)
  WHERE a.stock_method = 'รับเข้า' {$is_active}
  group by a.id";

  $stmt = $conn->prepare($query);
  $stmt->execute();

  $prevention_stock = $stmt->fetchall();

  // prep refill

  $refill_arr = [];

  foreach ($prevention_stock as $key => $item) {

    $stock = json_decode($item->stock_lot);
    $value = json_decode($item->value);

    foreach ($stock as $s_k => $s_v) {
      if (array_key_exists($s_v, $refill_arr)) {
        $refill_arr[$s_v] = $refill_arr[$s_v] + $value[$s_k];
      } else {
        $refill_arr[$s_v] = $value[$s_k];
      }
    }
  }

  $clinic_arr = [];

  $clinic_arr = [];

  $stock_lists = [];

  foreach ($prevention_stock as $stock) {
    // (int)$stock->refill_spend + (int)$stock->clinic_spend + 
    $stock_name = $stock->lot_number . ":" . $stock->id;
    if ($stock->stock_method == 'จ่ายออก') $stock_name = $stock->lot_number;

    if (!array_key_exists($stock_name, $stock_lists)) {
      $stock_lists[$stock_name] = [
        'id' => $stock->id,
        'stock_method' => $stock->stock_method,
        'type' => $stock->type,
        'stock_name' => $stock_name,
        'lot' => $stock->lot_number,
        'total' => (int)$stock->received_value,
        'spend' => (int)$stock->stock_spend,
        'left' => (int)$stock->stock_left,
        'from' => $stock->received_from,
        'expire' => $stock->expiration_date,
        'active' => $stock->active,
        'title' => $stock->title,
        'created_at' => $stock->created_at,
        'received_date' => dateOnly($stock->received_date)
      ];
      if (isset($refill_arr[$stock_name])) {
        $stock_lists[$stock_name]["spend"] = $stock_lists[$stock_name]["spend"] + $refill_arr[$stock_name];
        $stock_lists[$stock_name]["left"] = $stock_lists[$stock_name]["left"] - $refill_arr[$stock_name];
      }
      if (isset($clinic_arr[$stock_name])) {
        $stock_lists[$stock_name]["spend"] = $stock_lists[$stock_name]["spend"] + $clinic_arr[$stock_name];
        $stock_lists[$stock_name]["left"] = $stock_lists[$stock_name]["left"] - $clinic_arr[$stock_name];
      }
    }
  }

  return $stock_lists;
}

function prevention_stock_lists($startDate = NULL, $endDate = NULL)
{
  global $conn;

  // $where = "WHERE purpose in ('PrEP')";
  $data = [];

  // if ($startDate && $endDate) {
  //   // $where = "WHERE purpose in ('PrEP') AND received_date >= ? AND received_date < ?";
  //   $data = [$startDate, $endDate];
  // }

  $stmt = $conn->prepare("SELECT * FROM prevention_stock");
  $stmt->execute($data);

  return $stmt;
}


function bangrak_api($conn, $uic, $topic_id, $atClinicClose = false, $username = 'user_ngo', $password = 'ngo_pass1234', $url = 'https://mybunchee.com/api/bangrak_visit_data.php', $edit = false, $bangrak_hn = null, $api = false, $api_type = 'clinic')
{
  // fetch data
  $case_data = prep_stock_spending($conn, $section = '', $uic, $filter = '2014-10-01', NULL, NULL, $topic_id)->fetch();

  if ($case_data) {

    $case_data->prep_screening = json_decode($case_data->prep_screening);

    if (!is_array($case_data->prep_screening)) $case_data->prep_screening = [];

    $case_data->lab_file_upload = $case_data->lab_file_upload ? json_decode($case_data->lab_file_upload) : [];
    $case_data->hiv_file_upload = $case_data->hiv_file_upload ? json_decode($case_data->hiv_file_upload) : [];
    $case_data->creatinine_file_upload = $case_data->creatinine_file_upload ? json_decode($case_data->creatinine_file_upload) : [];
    $case_data->hbsag_file_upload = $case_data->hbsag_file_upload ? json_decode($case_data->hbsag_file_upload) : [];

    $checkSerialize = @unserialize($case_data->serviceProvide);

    if ($checkSerialize) {
      $case_data->serviceProvide = $case_data->serviceProvide ? unserialize($case_data->serviceProvide) : [];
      // return null;
    } else {
      // return $case_data->sn;
      $case_data->serviceProvide = [];
    }

    // patient
    $cid = $case_data->id_card;
    $prename = genderByKp($case_data->sex) == 'หญิง' ? 'นางสาว' : 'นาย';
    $name = $case_data->firstname;
    $lname = $case_data->lastname;
    $sex = genderByKp($case_data->sex) == 'หญิง' ? '2' : '1';
    $birth = birthdayByUic($case_data->uic)['birthDay'];
    $house = $case_data->house;
    $road = $case_data->road;
    $village = $case_data->district_code ? $case_data->address1 : '1 และ 3 ซ.รามคำแหง 97/2';
    $tambon = $case_data->district_code ? substr($case_data->district_code, 0, 2) : "08";
    $amphur = $case_data->district_code ? substr($case_data->district_code, 2, 2) : "06";
    $changwat = $case_data->district_code ? substr($case_data->district_code, 4, 2) : "10";
    $mstatus = "1";

    if (!$case_data->id_card || $case_data->id_card == '' || preg_match('/^9/', $case_data->id_card)) {
      $cid = $case_data->prep;
    }

    // "1" : "โสด",
    // "2" : "คู่",
    // "3" : "หม้าย",
    // "4" : "หย่า",
    // "5" : "ร้าง",
    // "6" : "สมณะ",

    // $occupa = $case_data->occupation;
    $occupa = "000";

    // "000" : "ไม่ทราบ",
    // "201" : "รับราชการ (ข้าราชการพลเรือน)",
    // "209" : "ลูกจ้างชั่วคราว",
    // "210" : "ลูกจ้างประจำ",
    // "302" : "พนักงานรัฐวิสาหกิจ",
    // "303" : "ผู้ปฏิบัติงานหน่วยงานรัฐวิสาหกิจอื่น ๆ",
    // "401" : "เจ้าของกิจการ/ธุรกิจส่วนตัว",
    // "402" : "พนักงานหน่วยงานเอกชน",
    // "403" : "รับจ้างทั่วไป",
    // "407" : "รับจ้างบริษัท/โรงงาน/ก่อสร้าง",
    // "911" : "นักเรียน/นักศึกษา",
    // "912" : "พนักงานบริการทางเพศ",
    // "913" : "บุคลากรสาธารณสุข",

    $nation = $case_data->nationality == 'ไทย' ? '99' : '00';
    $race = $case_data->nationality == 'ไทย' ? '99' : '00';

    $religion = '9';

    // "1" : "พุทธ",
    // "2" : "อิสลาม",
    // "3" : "คริสต์",
    // "4" : "ฮินดู",
    // "5" : "อื่นๆ",
    // "9" : "ไม่ทราบ"
    $education = '99';
    if ($case_data->education == 'น้อยกว่า ประถมศึกษา') $education = '00';
    if ($case_data->education == 'ประถมศึกษา') $education = '01';
    if ($case_data->education == 'มัธยมต้น') $education = '02';
    if ($case_data->education == 'มัธยมปลาย/ปวช.') $education = '14';
    if ($case_data->education == 'อนุปริญญา/ปวส.') $education = '03';
    if ($case_data->education == 'ปริญญาตรี') $education = '04';
    if ($case_data->education == 'ปริญญาโท') $education = '05';
    if ($case_data->education == 'มากกว่าปริญญาโท') $education = '06';

    $educate = $education;

    // "00" : "ไม่รู้หนังสือ",
    // "01" : "ป.4-ป.6",
    // "02" : "มัธยมต้น",
    // "03" : "อนุ-ปวส.",
    // "04" : "ปริญญาตรี",
    // "05" : "ปริญญาโท",
    // "06" : "ปริญญาเอก",
    // "14" : "มัธยมปลาย/ปวช.",
    // "99" : "ไม่ทราบ",


    $tel = $case_data->phone;

    $infoname = $case_data->infoname; // ชื่อญาติ
    $info_rela = $case_data->info_rela; // ความสัมพันธ์
    $info_tel = $case_data->info_tel; // เบอร์ญาติ
    $cdisease = $case_data->cdisease; // โรคประจำตัว

    // nap
    $nap_no1 = "0";
    $nap_no2 = "0";
    $nap_no3 = "0";
    $nap_no4 = "0";
    $nap_no5 = "0";
    $nap_no6 = "0";
    $nap_no7 = "0";
    $nap_no8 = "0";
    $nap_no9 = "0";
    $nap_no10 = "0";
    $nap_no11 = NULL;
    $nap_no12 = "99";

    if (in_array("sex ไม่ป้องกัน", $case_data->prep_screening)) $nap_no1 = "1";
    if (in_array("sex กับ HIV+ ที่ยังไม่เริ่มยา", $case_data->prep_screening)) $nap_no1 = "1";
    if (in_array("sex กับ HIV+ ที่กินยาไม่ต่อเนื่อง", $case_data->prep_screening)) $nap_no1 = "1";
    if (in_array("sex กับ HIV+ ที่ VL ยังสูง", $case_data->prep_screening)) $nap_no1 = "1";
    if (in_array("เป็นหญิงตั้งครรภ์ที่มีคู่ติดเชื้อ HIV", $case_data->prep_screening)) $nap_no2 = "1";
    if (in_array("ใช้สารเสพติดแบบฉีด", $case_data->prep_screening)) $nap_no3 = "1";
    if (in_array("มีเพศสัมพันธ์แบบกลุ่ม", $case_data->prep_screening)) $nap_no4 = "1";
    if (in_array("คู่นอนมากกว่า 1 คน", $case_data->prep_screening)) $nap_no5 = "1";
    if (in_array("ใช้ PEP มากกว่า 1 ครั้ง", $case_data->prep_screening)) $nap_no6 = "1";
    if (in_array("เป็น STIs หรือ มีอาการสงสัย", $case_data->prep_screening)) $nap_no7 = "1";
    if (in_array("ใช้สารเสพติดกลุ่มแอมเฟตามีน (ยาไอซ์, ยาบ้า, ยาอี)", $case_data->prep_screening)) $nap_no8 = "1";
    if (in_array("มีแนวโน้มเสี่ยงใน 3 เดือน", $case_data->prep_screening)) $nap_no9 = "1";
    // if(in_array("ใช้สารเสพติดกลุ่มแอมเฟตามีน (ยาไอซ์, ยาบ้า, ยาอี)", $case_data->prep_screening)) $nap_no10 = "1";
    if ($case_data->last_prep_date) $nap_no11 = $case_data->last_prep_date;
    if (in_array($case_data->sex, ['MSM'])) $nap_no12 = "1";
    if (in_array($case_data->sex, ['TG', 'TGW'])) $nap_no12 = "2";
    if (in_array($case_data->sex, ['TGM'])) $nap_no12 = "3";
    if (in_array($case_data->sex, ['TGSW'])) $nap_no12 = "4";
    if (in_array($case_data->sex, ['MSW'])) $nap_no12 = "5";
    if (in_array($case_data->sex, ['FSW'])) $nap_no12 = "6";
    if (in_array($case_data->sex, ['PWID-Male', 'PWID-Female'])) $nap_no12 = "7";

    // 1	MSM
    // 2	TGW
    // 3	TGM
    // 4	TGSW
    // 5	MSW
    // 6	FSW
    // 7	PWID
    // 8	PWUD
    // 9	Partner of KP
    // 10	Partner of PLHIV
    // 11	Prisoners
    // 12	Migrant
    // 13	ANC
    // 14	คลอดจากแม่ติดเชื้อเอชไอวี
    // 15	oPEP
    // 16	nPEP
    // 17	Population
    // 18	สามี/คู่ของหญิงตั้งครรภ์
    // 99	ไม่ทราบ

    // visit
    $date_time = explode(" ", $case_data->service_date);

    $date = $date_time[0];
    $time = $date_time[1];
    $weight = $case_data->weight;
    $height = $case_data->height;
    $waist = $case_data->waist;
    $bp = $case_data->pressure;
    $pure = $case_data->heartrate;
    $resp = $case_data->resp;
    $sign = $case_data->sign;

    // drug
    // $drugcode = count(json_decode($case_data->prep_med_type)) > 0 ? "TNEM2" : "NA";
    $drugcode = "";
    if ($case_data->prep_today_route == 'PrEP GF') $drugcode = 'GFTNEM';
    if ($case_data->prep_today_route == 'PrEP สปสช') $drugcode = 'NHTNEM';
    $amount = json_decode($case_data->prep_med_amount)[0] > 0 ? json_decode($case_data->prep_med_amount)[0] : "0";
    if (json_decode($case_data->prep_med_amount)[1] > 0) $amount += json_decode($case_data->prep_med_amount)[1];

    // labvalue
    $labvalue = [];

    // 323	HBsAg (Outlab) 	positive | negative
    // 31O	Creatinine	ค่า Creatinine
    // 315	Syphilis	positive | negative
    // 055	HIV	positive | negative

    if (in_array("ตรวจ Anti-HIV", $case_data->serviceProvide)) {

      $hiv_result = 'negative';
      if ($case_data->hiv_result == 'Anti-HIV Positive') $hiv_result = 'positive';

      $labvalue[] = [
        "labcode" => "055",
        "labrs" => $hiv_result,
        // "filedata" => "055",
      ];
    }
    if (in_array("ตรวจ Anti-TP", $case_data->serviceProvide) && in_array($case_data->sti_result, ['P', 'R', 'N'])) {

      $sti_result = 'negative';
      if (in_array($case_data->sti_result, ['P', 'R'])) $sti_result = 'positive';

      $labvalue[] = [
        "labcode" => "315",
        "labrs" => $sti_result,
        // "filedata" => "315",
      ];
    }
    // if (in_array("ตรวจ Creatinine", $case_data->serviceProvide) && is_numeric($case_data->lab_creatinine_result)) {
    if (is_numeric($case_data->lab_creatinine_result)) {

      $labvalue[] = [
        "labcode" => "31O",
        "labrs" => (float) $case_data->lab_creatinine_result,
        // "filedata" => "31O",
      ];
    }
    // if (in_array("ตรวจ eGFR", $case_data->serviceProvide) && is_numeric($case_data->lab_egfr_result)) {
    if (is_numeric($case_data->lab_egfr_result)) {

      $labvalue[] = [
        "labcode" => "88E",
        "labrs" => (float) $case_data->lab_egfr_result,
        // "filedata" => "88E",
      ];
    }
    // if (in_array("ตรวจ HBsAg", $case_data->serviceProvide) && in_array($case_data->lab_hbsag_result, ['Negative', 'Positive'])) {
    if (in_array($case_data->lab_hbsag_result, ['Negative', 'Positive'])) {

      $hbsag_result = 'negative';
      if ($case_data->lab_hbsag_result == 'Positive') $hbsag_result = 'positive';

      $labvalue[] = [
        "labcode" => "323",
        "labrs" => $hbsag_result,
        // "filedata" => "323",
      ];
    }

    // // loop all labvalue and check if there is image file then add to array (OLD)
    // $labresult = [];
    // if (count($case_data->lab_file_upload) > 0 && count($labvalue) > 0) {

    //     foreach ($case_data->lab_file_upload as $item) {
    //         $file_path = __DIR__ . "/prep/" . $item;
    //         $filename = basename($file_path);
    //         $type = pathinfo($file_path, PATHINFO_EXTENSION);
    //         $data = file_get_contents($file_path);
    //         $filedata = base64_encode($data);
    //         // $image = "data:image/{$type};base64," . base64_encode($data);

    //         $labresult[] = [
    //             "filename" => $filename,
    //             "filedata" => $filedata,
    //         ];
    //     }
    // }

    // new check file upload
    // check if HIV file then add to labvalue of labcode 055
    // if (count($case_data->hiv_file_upload) > 0 && count(array_filter($labvalue, function ($item) { return $item['labcode'] == '055';})) > 0) {}
    if ($case_data->hiv_file_upload && count($case_data->hiv_file_upload) > 0 && count($filter_array = array_filter($labvalue, function ($item) {
      return $item['labcode'] == '055';
    })) > 0) {
      foreach ($case_data->hiv_file_upload as $item) {
        $file_path = __DIR__ . "/../prep/" . $item;
        // $filename = basename($file_path);
        // $type = pathinfo($file_path, PATHINFO_EXTENSION);
        $data_hiv = file_get_contents($file_path);
        $filedata = base64_encode($data_hiv);
        // $image = "data:image/{$type};base64," . base64_encode($data);
        $labvalue[array_key_first($filter_array)]['filedata'] = $filedata;
      }
    }
    // check if Syphilis file then add to labvalue of labcode 315
    if ($case_data->hiv_file_upload && count($case_data->hiv_file_upload) > 0 && count($filter_array = array_filter($labvalue, function ($item) {
      return $item['labcode'] == '315';
    })) > 0) {
      foreach ($case_data->hiv_file_upload as $item) {
        $file_path = __DIR__ . "/../prep/" . $item;
        // $filename = basename($file_path);
        // $type = pathinfo($file_path, PATHINFO_EXTENSION);
        $data_syphilis = file_get_contents($file_path);
        $filedata = base64_encode($data_syphilis);
        // $image = "data:image/{$type};base64," . base64_encode($data);
        $labvalue[array_key_first($filter_array)]['filedata'] = $filedata;
      }
    }
    // check if Creatinine file then add to labvalue of labcode 31O
    if ($case_data->creatinine_file_upload && count($case_data->creatinine_file_upload) > 0 && count($filter_array = array_filter($labvalue, function ($item) {
      return $item['labcode'] == '31O';
    })) > 0) {
      foreach ($case_data->creatinine_file_upload as $item) {
        $file_path = __DIR__ . "/../prep/" . $item;
        // $filename = basename($file_path);
        // $type = pathinfo($file_path, PATHINFO_EXTENSION);
        $data_creatinine = file_get_contents($file_path);
        $filedata = base64_encode($data_creatinine);
        // $image = "data:image/{$type};base64," . base64_encode($data);
        $labvalue[array_key_first($filter_array)]['filedata'] = $filedata;
      }
    }
    // check if eGFR file then add to labvalue of labcode 88E
    if ($case_data->creatinine_file_upload && count($case_data->creatinine_file_upload) > 0 && count($filter_array = array_filter($labvalue, function ($item) {
      return $item['labcode'] == '88E';
    })) > 0) {
      foreach ($case_data->creatinine_file_upload as $item) {
        $file_path = __DIR__ . "/../prep/" . $item;
        // $filename = basename($file_path);
        // $type = pathinfo($file_path, PATHINFO_EXTENSION);
        $data_egfr = file_get_contents($file_path);
        $filedata = base64_encode($data_egfr);
        // $image = "data:image/{$type};base64," . base64_encode($data);
        $labvalue[array_key_first($filter_array)]['filedata'] = $filedata;
      }
    }
    // check if HBsAg file then add to labvalue of labcode 323
    if ($case_data->hbsag_file_upload && count($case_data->hbsag_file_upload) > 0 && count($filter_array = array_filter($labvalue, function ($item) {
      return $item['labcode'] == '323';
    })) > 0) {
      foreach ($case_data->hbsag_file_upload as $item) {
        $file_path = __DIR__ . "/../prep/" . $item;
        // $filename = basename($file_path);
        // $type = pathinfo($file_path, PATHINFO_EXTENSION);
        $data_hbsag = file_get_contents($file_path);
        $filedata = base64_encode($data_hbsag);
        // $image = "data:image/{$type};base64," . base64_encode($data);
        $labvalue[array_key_first($filter_array)]['filedata'] = $filedata;
      }
    }

    // fetch data

    $data = [
      "patient" => [
        "cid" => $cid,  // "cid" : "", /* required id_card, passsport */
        "prename" => $prename,  // "prename" : "", /* required นาย, นาง, Mr., Mrs. ect. */
        "name" => $name,  // "name" : "", /* required */
        "lname" => $lname,  // "lname" : "", /* required */
        "sex" => $sex,  // "sex" : "", /* required 1 = ชาย, 2 = หญิง */
        "birth" => $birth,  // "birth" : "", /* required */
        "house" => $house,  // "house" : "", /* */
        "road" => $road,  // "road" : "", /* */
        "village" => str_pad($village, 2, "0", STR_PAD_LEFT),  // "village" : "", /* required */
        "tambon" => $tambon,  // "tambon" : "", /* required Location_Code.xlsx */
        "amphur" => $amphur,  // "amphur" : "", /* required Location_Code.xlsx */
        "changwat" => $changwat,  // "changwat" : "", /* required Location_Code.xlsx */
        "mstatus" => $mstatus,  // "mstatus" : "", /* รหัสสมรส */
        "occupa" => $occupa,  // "occupa" : "", /* รหัสอาชีพ */
        "nation" => $nation,  // "nation" : "", /* required รหัสสัญชาติ */
        "race" => $race,  // "race" : "", /* required รหัสสัญชาติ */
        "religion" => $religion,  // "religion" : "", /* รหัสศาสนา */
        "educate" => $educate,  // "educate" : "", /* รหัสการศึกษา */
        "tel" => $tel,  // "tel" : "", /* */
        "infoname" => $infoname,  // "infoname" : "", /* */
        "info_rela" => $info_rela,  // "info_rela" : "", /* เช่น บิดา มารดา เพื่อน */
        "info_tel" => $info_tel,  // "info_tel" : "", /* */
        "cdisease" => $cdisease,  // "cdisease" : "HIV เบาหวาน ไตเรื้อรัง หลอดเลือดสมอง", /* */
      ],
      "nap" => [
        "no1" => $nap_no1, // "1",
        "no2" => $nap_no2, // "0",
        "no3" => $nap_no3, // "0",
        "no4" => $nap_no4, // "0",
        "no5" => $nap_no5, // "1",
        "no6" => $nap_no6, // "0",
        "no7" => $nap_no7, // "0",
        "no8" => $nap_no8, // "0",
        "no9" => $nap_no9, // "0",
        "no10" => $nap_no10, // "1",
        "no11" => $nap_no11, // "2022-12-15",
        "no12" => $nap_no12, // "13"
      ],
      "visit" => [
        "date" => $date, // "2023-01-01",
        "time" => $time, // "09:00:00",
        "weight" => $weight, // "70",
        "height" => $height, // "170",
        "waist" => $waist, // "86",
        "bp" => $bp, // "122/76",
        "pure" => $pure, // "80",
        "resp" => $resp, // "24",
        "sign" => $sign, // "มีไข้มา 3 วัน มีอาการปัสสาวะแสบขัดร่วมด้วย"
      ],
      "drug" => [
        [
          "drugcode" => $drugcode, // "TNEM2",
          "amount" => (int)$amount * 30, // "50"
        ]
      ],
      "edit" => $edit,
      "ID" => $bangrak_hn
    ];

    // example 
    // https://carematapp.com.test/bangrak_api_fetch.php?uic=%E0%B8%81%E0%B8%9E180128&topic_id=76883

    if (count($labvalue) > 0) $data["labvalue"] = $labvalue;

    if ($atClinicClose) unset($data["labvalue"]);

    // if using api then return data
    if ($api) {
      if ($api_type == 'clinic') unset($data["labvalue"]);
      return $data;
    }

    $data_set = [
      "username" => $username,
      "password" => $password,
      "data" => $data
    ];

    $labRequest = file_get_contents($url, false, stream_context_create([
      'http' => [
        'method' => 'POST',
        'header'  => 'Content-Type: application/json',
        'content' => json_encode($data_set)
      ]
    ]));
    $response_string = json_decode($labRequest, true);

    // Decode the response and return it
    $response = [];
    $response['data'] = $response_string;
    $response['ori'] = $data;
    return $response;
  }
}

function bangrak_api_get_data($user_id, $username = 'user_ngo', $password = 'ngo_pass1234', $url = 'https://mybunchee.com/api/bangrak_approve_check.php')
{

  // proxy
  $data = [
    "ID" => $user_id
  ];

  $data_set = [
    "username" => $username,
    "password" => $password,
    "data" => $data
  ];

  $labRequest = file_get_contents($url, false, stream_context_create([
    'http' => [
      'method' => 'POST',
      'header'  => 'Content-Type: application/json',
      'content' => json_encode($data_set)
    ]
  ]));
  $response_string = json_decode($labRequest, true);

  // Decode the response and return it
  $response = [];
  $response['data'] = $response_string;
  $response['ori'] = $data;
  return $response;
  // proxy

  // // original
  // $base64Auth = base64_encode("$username:$password");

  // $headers = [
  //   "Authorization: Basic $base64Auth",
  //   "Content-Type: application/json"
  // ];

  // $data = [
  //   "ID" => $user_id
  // ];

  // $json_data = json_encode($data);

  // // $url = $url . '?' . http_build_query($data);

  // $curl = curl_init();
  // curl_setopt($curl, CURLOPT_URL, $url);
  // // curl_setopt($curl, CURLOPT_HTTPGET, true);
  // curl_setopt($curl, CURLOPT_POSTFIELDS, $json_data);
  // curl_setopt($curl, CURLOPT_CUSTOMREQUEST, 'GET');
  // curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
  // curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);

  // $response = [];

  // $response_string = curl_exec($curl);
  // curl_close($curl);

  // // "status": "200"
  // // "message": "OK"
  // // "ID": "63F33E5BA7B60"
  // // "patient": "xxx xxx"
  // // "timestmp": "2023-02-20 16:33:15"
  // $response['data'] = json_decode($response_string, true);
  // $response['ori'] = $data;
  // return $response;
  // // original
}

function AIForThaiIDScan($img_file = 'img.jpg')
{
  $curl = curl_init();
  // $img_file = ("img.jpg");
  $data = array("file" => new CURLFile($img_file, mime_content_type($img_file), basename($img_file)));

  curl_setopt_array($curl, array(
    CURLOPT_URL => "https://api.aiforthai.in.th/ocr-id-front-iapp",
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_ENCODING => "",
    CURLOPT_MAXREDIRS => 10,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    CURLOPT_CUSTOMREQUEST => "POST",
    CURLOPT_POSTFIELDS => $data,
    CURLOPT_HTTPHEADER => array(
      "Content-Type: multipart/form-data",
      "apikey: S3Hr9QJlN9IR86iAYrf0aKtMv8NaklWy"
    )
  ));

  $response = curl_exec($curl);
  $err = curl_error($curl);

  curl_close($curl);

  if ($err) return $err;

  return json_decode($response, true);
}

function pharmacy_lists()
{
  global $conn;

  $data = [];

  $stmt = $conn->prepare("SELECT 
  a.id,
  a.clinic_id, 
  a.queue, 
  a.uic, 
  a.name as firstname, 
  a.lastname, 
  a.nickname, 
  a.id_card, 
  a.status,
  b.prep_distribution,
  b.prep_no_distribution_reason,
  b.prep_code,
  b.prep_med_type,
  b.prep_med_amount,
  b.med_organizer_staff, 
  b.med_dispensary_staff, 
  b.client_signature, 
  b.prep_set_visit
  
  FROM clinicnewcase a
  INNER JOIN clinicpostcounseling b ON a.uic = b.uic AND a.clinic_id = b.clinic_id AND a.queue = b.queue
  INNER JOIN cliniclog c ON a.clinic_id = c.id
  where c.status = 'open'
  having b.prep_distribution = 'จ่าย PrEP'");
  $stmt->execute($data);

  return $stmt;
}

function distance($lat1, $lon1, $lat2, $lon2)
{

  $lat1 = $lat1 ? (float) $lat1 : 0;
  $lon1 = $lon1 ? (float) $lon1 : 0;
  $lat2 = $lat2 ? (float) $lat2 : 0;
  $lon2 = $lon2 ? (float) $lon2 : 0;

  $R = 6371.01; // Earth's radius in kilometers
  $lat1Rad = deg2rad($lat1);
  $lon1Rad = deg2rad($lon1);
  $lat2Rad = deg2rad($lat2);
  $lon2Rad = deg2rad($lon2);
  $dLat = $lat2Rad - $lat1Rad;
  $dLon = $lon2Rad - $lon1Rad;
  $a = sin($dLat / 2) * sin($dLat / 2) +
    cos($lat1Rad) * cos($lat2Rad) *
    sin($dLon / 2) * sin($dLon / 2);
  $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
  $distance = $R * $c;
  return $distance;
}

function last_location($cbs)
{

  $data = selectDatabase('loginlog', [], [
    "username" => ['=', $cbs],
    "loginstatus" => ['=', 'Succeed']
  ], ['id' => 'DESC'])->fetch();

  return $data;
}

function ResultLabel($result = '', $size = '1.4rem')
{
  if (in_array($result, [
    'Anti-HIV Positive',
    'Anti-TP Positive',
    'Anti-HCV Reactive',
    'Anti-HCV Positive',
    'Reactive',
    'CT Detected',
    'NG Detected'
  ])) {
    return labelbig($result, "danger");
  }
  if (in_array($result, [
    'Anti-HIV Negative',
    'Anti-TP Negative',
    'Anti-HCV Non-reactive',
    'Anti-HCV Negative',
    'Non-reactive',
    'CT Not Detected',
    'NG Not Detected'
  ])) {
    return labelbig($result, "success");
  }

  if (in_array($result, [
    'Anti-HIV Inconclusive'
  ])) {
    return labelbig($result, "warning");
  }

  if (in_array($result, [
    'ผล CD4 ยังไม่ออก',
    'ผล VL ยังไม่ออก',
    'ผล CT ยังไม่ออก',
    'ผล NG ยังไม่ออก'
  ])) {
    return labelbig($result, "default");
  }

  if ($result == '') return labelbig("NA", "black");
}

function formatBytes($bytes, $precision = 2)
{
  $units = array('B', 'KB', 'MB', 'GB', 'TB');

  $bytes = max($bytes, 0);
  $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
  $pow = min($pow, count($units) - 1);

  $bytes /= (1 << (10 * $pow));

  return round($bytes, $precision) . ' ' . $units[$pow];
}

function containsEnglish($str)
{
  return preg_match('/[a-zA-Z]/', $str);
}

function convertToUpperCase($str)
{
  return strtoupper($str);
}

function cleanPostInput($post)
{
  foreach ($post as $key => $value) {
    if (is_array($value)) {
      $post[$key] = cleanPostInput($value); // recursive
      continue;
    }
    $value = trim((string)$value);
    if ($value === 'null' || $value === 'undefined' || $value === '') {
      $value = null;
    }
    $post[$key] = $value;
  }
  return $post;
}

function db_env_path(): string
{
  // keep .secrets OUTSIDE the public folder
  return __DIR__ . '/../.secrets/.env.php';
}

function is_local_env(array $env): bool
{
  // กติกาตามที่ต้องการ: DB_NAME == carematdb ถือว่าเป็น local
  if (($env['DB_NAME'] ?? '') === 'carematdb') return true;

  return false; // อื่น ๆ ถือว่าเป็น VPS
}

function countReachInFy($reachdate, $uic)
{
  // count reach in the same FY that before this reach
  global $conn;
  $startOfFY = checkQuarter($reachdate)['q1_start'];
  $thisReachDate = $reachdate;
  $stmt = $conn->prepare("SELECT COUNT(id) AS count_in_fy FROM reach WHERE uic = ? AND reachdate >= ? AND reachdate <= ?");
  $stmt->execute([$uic, $startOfFY, $thisReachDate]);
  $count_in_fy = $stmt->fetch(PDO::FETCH_ASSOC);
  return $count_in_fy['count_in_fy'];
}
