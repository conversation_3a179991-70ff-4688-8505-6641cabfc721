<?php
require dirname(__FILE__) . '/../session_setting.php';
if (!isset($_SESSION['user']) || empty($_SESSION['user']) || $_SESSION['user'] == null || $_SESSION['user'] == '') {
  header("Location:../index.php");
}

require '../helpers/pagesvisited.php'; //

// error_reporting(E_ALL);
// ini_set('display_errors', 1);

ini_set('memory_limit', '1G');

$currentTime = date('Y-m-d H:i:s');
$currentTimeStamp = strtotime($currentTime);
$today = date("Y-m-d");
$tomorrow = date('Y-m-d', strtotime($today . "+1 days"));

$site_specific = fetchSiteSpecific();

$reachA1 = reachA1Left()->rowCount() ?? 0;
$retainA1 = retainA1Left()->rowCount() ?? 0;
$walkinB1 = registerB1Left()->rowCount() ?? 0;
$clinicC1 = clinicC1Left()->rowCount() ?? 0;
$hivRetainD2 = hivRetainD2Left()->rowCount() ?? 0;

$nhso_filter = selectDatabase("nhso_config", [], ["year" => ["=", checkQuarter()['this_fy']]])->fetch();
$nhso_reach_filter = $nhso_filter->nhso_reach_start;
$nhso_clinic_filter = $nhso_filter->nhso_clinic_start;

$startDate = checkQuarter()['q1_start'];
$endDate = checkQuarter()['q4_end'];

$reachNap = count(reachNapLeft($conn, 'fy23', $nhso_reach_filter, '2023', $startDate, $endDate));
$clinicNap = count(caseClinicForNap($conn, true, 'fy23', '2023', $nhso_clinic_filter));

$prepNap = casePrEPForNap($conn, true, 'NapUnDone') ? count(casePrEPForNap($conn, true, 'NapUnDone')) : 0;
$prepOutLab = casePrEPForLabEnter($conn, true, 'NapUnDone') ? count(casePrEPForLabEnter($conn, true, 'NapUnDone')) : 0;

$text = "เวลา : $currentTime

แจ้งเตือนการบันทึก eCascade/NAP คงค้าง

A1 -> Reach [ค้าง $reachA1 เคส]
A1 -> Retain Negative [ค้าง $retainA1 เคส]
B1 -> Walk In [ค้าง $walkinB1 เคส]
C1 -> Clinic [ค้าง $clinicC1 เคส]
D2 -> HIV ติดตาม [ค้าง $hivRetainD2 เคส]
NAP -> สปสช Reach [ค้าง $reachNap เคส]
NAP -> สปสช HIV [ค้าง $clinicNap เคส]
NAP -> PrEP สปสช NAP [ค้าง $prepNap เคส]
LAB -> PrEP OUT-Lab [ค้าง $prepOutLab เคส]";

if (userHasSiteName(['mplus_nma', 'rsat_nsn', 'sisters_ryg'], $site_specific->sitename14)) {
  $text = "เวลา : $currentTime
  
  แจ้งเตือนการบันทึก NAP คงค้าง
  
  NAP -> สปสช Reach [ค้าง $reachNap เคส]
  NAP -> สปสช HIV [ค้าง $clinicNap เคส]";
}

LineNotify($text, true, true);
logging($text);
logging($text, 'monitor');
