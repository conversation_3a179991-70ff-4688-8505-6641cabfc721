<?php

require '../session_setting.php';

// error_reporting(E_ALL);
// ini_set('display_errors', 1);

ini_set('max_execution_time', '1000');
ini_set('memory_limit', '-1');

$nhso_filter = selectDatabase("nhso_config", [], ["year" => ["=", checkQuarter()['this_fy']]])->fetch();
$nhso_reach_filter = $nhso_filter->nhso_reach_start;
$nhso_clinic_filter = $nhso_filter->nhso_clinic_start;

$startDate = checkQuarter()['q1_start'];
$endDate = checkQuarter()['q4_end'];

$allReach = fetchNHSOForReach($conn, true, 'fy26', '2026', $nhso_reach_filter, $startDate, $endDate);
if($allReach) $allReach = array_filter($allReach, fn($item) => !$item['nap_code'] || $item['nap_code'] === '');

$allClinic = caseClinicForNap($conn, true, 'fy26', '2026', $nhso_clinic_filter, $startDate, $endDate);
if($allClinic) $allClinic = array_filter($allClinic, fn($item) => !$item['nap_code'] || $item['nap_code'] === '');

$allSyphilis = caseClinicForNap($conn, true, 'fy26', '2026', $nhso_clinic_filter, $startDate, $endDate, 'syphilis');
if($allSyphilis) $allSyphilis = array_filter($allSyphilis, fn($item) => !$item['nap_code'] || $item['nap_code'] === '');

$allCD4 = caseCD4ForNap($conn, true, 'fy26', '2026', $nhso_clinic_filter, $startDate, $endDate);
if($allCD4) $allCD4 = array_filter($allCD4, fn($item) => !$item['nap_code'] || $item['nap_code'] === '');

$allVL = caseVLForNap($conn, true, 'fy26', '2026', $nhso_clinic_filter, $startDate, $endDate);
if($allVL) $allVL = array_filter($allVL, fn($item) => !$item['nap_code'] || $item['nap_code'] === '');

$allHCV = caseHCVForNap($conn, true, 'fy26', '2026', $nhso_clinic_filter, $startDate, $endDate);
if($allHCV) $allHCV = array_filter($allHCV, fn($item) => !$item['nap_code'] || $item['nap_code'] === '');

// $reachNap = count(reachNapLeft($conn, 'fy24', $nhso_reach_filter, '2024', $startDate, $endDate));
$reachNap = count($allReach);
$clinicNap = count($allClinic);
$syphilisNap = count($allSyphilis);
$cd4Nap = count($allCD4);
$vlNap = count($allVL);
$hcvNap = count($allHCV);

$response = [];
$response['reachNap'] = $reachNap;
$response['clinicNap'] = $clinicNap;
$response['syphilisNap'] = $syphilisNap;
$response['cd4Nap'] = $cd4Nap;
$response['vlNap'] = $vlNap;

$response['hcvNap'] = $hcvNap;
$response['allCD4'] = $allCD4;
$response['allVL'] = $allVL;
$response['allHCV'] = $allHCV;
$response['allSyphilis'] = $allSyphilis;

dd_json($response);