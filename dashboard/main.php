<?php

// use function GuzzleHttp\json_encode;

require '../session_setting.php';

if (!isset($_SESSION['user']) || empty($_SESSION['user']) || $_SESSION['user'] == null || $_SESSION['user'] == '') {
	flash('notlogin', 'กรุณา Login เพื่อใช้งาน', 'alert alert-danger');
	header("Location: ../index.php");
}

require '../helpers/pagesvisited.php'; //
?>

<?php include '../layouts/header.php' ?>

<div class="wrapper">

	<style>
		#performanceTable th,
		#performanceTable td {
			border: 1px solid #000;
			text-align: center;
		}

		#nhsoTable th,
		#nhsoTable td {
			border: 1px solid #000;
			text-align: center;
		}

		.box>.overlay,
		.overlay-wrapper>.overlay,
		.box>.loading-img,
		.overlay-wrapper>.loading-img {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
		}

		.box .overlay,
		.overlay-wrapper .overlay {
			z-index: 50;
			background: rgba(255, 255, 255, 0.7);
			border-radius: 3px;
		}

		.box .overlay>.fa,
		.overlay-wrapper .overlay>.fa {
			position: absolute;
			top: 50%;
			left: 50%;
			margin-left: -15px;
			margin-top: -15px;
			color: #000;
			font-size: 30px;
		}

		.box .overlay.dark,
		.overlay-wrapper .overlay.dark {
			background: rgba(0, 0, 0, 0.5);
		}
	</style>

	<?php include '../layouts/topmenu.php' ?>
	<?php include '../layouts/sidebarmenu.php' ?>

	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header">
			<h1>
				Main short-cut Link
				<small>Link ลัดเข้าสู่ส่วนงาน</small>
			</h1>
			<ol class="breadcrumb">
				<li><a href="#"><i class="fas fa-tachometer-alt"></i> Home</a></li>
				<li class="active">Main</li>
			</ol>
		</section>

		<!-- Main content -->
		<section id="dashboardMainSectionVue" class="content">
			<?php flash('error'); ?>
			<?php flash('login'); ?>
			<?php flash('notAllow'); ?>
			<!-- Info boxes -->
			<div class="row">
				<div class="col-lg-6">
					<div class="box box-primary box-solid" id="">
						<div class="box-header with-border" data-widget="collapse">
							<h3 class="box-title">เมนูลัด + Link จำเป็น</h3>
						</div>
						<div class="box-body">
							<div style="display:flex;justify-content:space-around;flex-wrap:wrap;">
								<div style="margin-bottom:10px">
									<a target="_blank" href="../services/reach.php" class="btn btn-default btn-sm">ฟอร์ม Reach</a>
									<a target="_blank" href="../services/recruit.php" class="btn btn-default btn-sm">ตาราง Reach</a>
								</div>
								<div style="margin-bottom:10px">
									<a target="_blank" href="../services/clinic.php" class="btn btn-default btn-sm">Clinic</a>
									<a target="_blank" href="../report/cliniclog.php" class="btn btn-default btn-sm">Clinic Summary</a>
								</div>
								<div style="margin-bottom:10px">
									<a target="_blank" href="../prep/prep_stock.php" class="btn btn-default btn-sm">PrEP Stock</a>
									<a target="_blank" href="../prep/prep_lists.php" class="btn btn-default btn-sm">PrEP Lists</a>
									<a target="_blank" href="../prep/prep_refill.php" class="btn btn-default btn-sm">PrEP Refill</a>
									<a target="_blank" href="../prep/prep_hospital_request.php" class="btn btn-default btn-sm">PrEP Request Approve</a>
								</div>
								<div style="margin-bottom:10px">
									<a target="_blank" href="../retain/negativeRetain.php" class="btn btn-default btn-sm">Negative Retention</a>
								</div>
								<div style="margin-bottom:10px">
									<a target="_blank" href="https://admin.testmenow.net/" class="btn btn-success btn-sm">TestMeNow</a>
									<a target="_blank" href="https://ecascadeview.org/" class="btn btn-success btn-sm">eCascade View</a>
									<a target="_blank" href="https://www.commcarehq.org/a/epm-thai/login/" class="btn btn-success btn-sm">COMMCARE</a>
								</div>
								<div style="margin-bottom:10px">
									<a target="_blank" href="https://healthcare.ihri.org/" class="btn btn-warning btn-sm">IHRI Services</a>
									<!-- <a target="_blank" href="http://161.82.242.164/iclinic" class="btn btn-warning btn-sm">iClinic</a> -->
									<a target="_blank" href="http://161.82.242.164/weclinic" class="btn btn-warning btn-sm">We Clinic</a>
								</div>
								<div style="margin-bottom:10px">
									<a target="_blank" href="http://dmis.nhso.go.th/NAPPLUS" class="btn btn-info btn-sm">NAP Plus</a>
									<!-- <a target="_blank" href="https://rtcmplus.ddc.moph.go.th/" class="btn btn-info btn-sm">RTCMPLUS +</a> -->
								</div>
								<div style="margin-bottom:10px">
									<a target="_blank" href="https://eservices.nhso.go.th/eServices/mobile/login.xhtml" class="btn btn-primary btn-sm">ตรวจสอบสิทธิรักษา</a>
									<a target="_blank" href="https://hivhub.ddc.moph.go.th/response.php" class="btn btn-primary btn-sm">HIV Info Hub</a>
								</div>
							</div>
						</div>
					</div>

					<!-- ปฎิทินงาน -->
					<div class="box box-primary box-solid" id="workCalendar">
						<div class="box-header with-border" data-widget="collapse">
							<h3 class="box-title">ปฎิทินงาน</h3>
						</div>
						<div class="box-body">
							<div class="row">
								<div class="col-lg-12">
									<div id='calendar'>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="col-lg-6">
					<div class="box box-success box-solid" id="">
						<div class="box-header with-border" data-widget="collapse">
							<h3 class="box-title">Dashboard page</h3>
						</div>
						<div class="box-body">
							<div style="display:flex;justify-content:space-around;flex-wrap:wrap;">
								<div style="margin-bottom:10px">
									<div style="display: inline-block">
										<div class="text-center">
											<a target="_blank" href="../dashboard/dashboard.php" class="btn btn-app bg-blue">
												<span class="hidden badge bg-green">New</span>
												<i class="fa fa-tachometer-alt"></i> <span class="h5">Main Dashboard</span>
											</a>
										</div>
										<div class="text-center">
											<span>สรุปการทำงานหลัก</span>
										</div>
									</div>
									<div style="display: inline-block">
										<div class="text-center">
											<a target="_blank" href="../dashboard/dashboard2.php" class="btn btn-app bg-yellow">
												<span class="hidden badge bg-green">New</span>
												<i class="fa fa-tachometer-alt"></i> <span class="h5">Dashboard 2</span>
											</a>
										</div>
										<div class="text-center">
											<span>สรุปรายละเอียดย่อยคลินิก</span>
										</div>
									</div>
									<div style="display: inline-block">
										<div class="text-center">
											<a target="_blank" href="../dashboard/dashboard3.php" class="btn btn-app bg-purple">
												<span class="hidden badge bg-green">New</span>
												<i class="fa fa-tachometer-alt"></i> <span class="h5">Dashboard PrEP</span>
											</a>
										</div>
										<div class="text-center">
											<span>สรุปข้อมูลบริการ PrEP</span>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>

					<div class="box box-primary box-solid">
						<div class="box-header with-border" data-widget="collapse" style="background:#D81B60;">
							<h3 class="box-title">คงค้างบันทึก NAP</h3>
						</div>
						<div class="box-body">
							<!-- <a href="https://www.commcarehq.org/a/epm-thai" target="_blank" class="btn btn-primary btn-xs" style="margin-bottom:5px;">หน้า COMMCARE</a> -->
							<a href="http://dmis.nhso.go.th/NAPPLUS/login.jsp" target="_blank" class="btn btn-success btn-xs" style="margin-bottom:5px;">ไป NAP หน้าเหลือง</a>
							<button @click="fetchNap()" type='button' class="btn btn-warning btn-xs" style="margin-bottom:5px;"><i class="fas fa-sync"></i> ดึงข้อมูล</button>
<pre>
<span ref="reachNapCount"></span>
<span ref="clinicNapCount"></span>
<span ref="syphilisNapCount"></span>
<span ref="cd4NapCount"></span>
<span ref="vlNapCount"></span>
<span ref="hcvNapCount"></span>
</pre>

							<div class="row hidden">
								<div class="col-lg-6">
									<div class="table-responsive">
										<table class="table table-striped table-bordered text-center">
											<thead class="bg-purple">
												<tr>
													<th>Month</th>
													<th>Reached</th>
													<th>NAP RR</th>
												</tr>
											</thead>
											<tbody>
												<tr v-for="month in ['2023-10', '2023-11', '2023-12', '2024-01', '2024-02', '2024-03', '2024-04', '2024-05', '2024-06', '2024-07', '2024-08', '2024-09']">
													<td>{{ month }}</td>
													<td>1</td>
													<td>1</td>
												</tr>
											</tbody>
										</table>
									</div>
								</div>
								<div class="col-lg-6">
									<div class="table-responsive">
										<table class="table table-striped table-bordered text-center">
											<thead class="bg-primary">
												<tr>
													<th>Month</th>
													<th>Tested</th>
													<th>NAP Request</th>
													<th>NAP Result</th>
												</tr>
											</thead>
											<tbody>
												<tr v-for="month in ['2023-10', '2023-11', '2023-12', '2024-01', '2024-02', '2024-03', '2024-04', '2024-05', '2024-06', '2024-07', '2024-08', '2024-09']">
													<td>{{ month }}</td>
													<td>1</td>
													<td>1</td>
													<td>1</td>
												</tr>
											</tbody>
										</table>
									</div>
								</div>
							</div>
						</div>

						<div class="hidden overlay" ref="overlay">
							<i class="fa fa-refresh fa-spin"></i>
						</div>
					</div>

					<!-- กราฟคลินิก -->
					<div class="box box-primary box-solid" id="clinicChartBox">
						<div class="box-header with-border" data-widget="collapse">
							<h3 class="box-title">สรุปข้อมูลบริการคลินิก</h3>
						</div>
						<div class="box-body">
							<div class="row" style="margin-bottom:10px">
								<div class="col-lg-12" style="display:flex; align-items:center">
									<select name="pickMonth" id="pickMonth">
										<option value="01" <?= date('n') == '01' ? 'selected' : ''; ?>>มกราคม</option>
										<option value="02" <?= date('n') == '02' ? 'selected' : ''; ?>>กุมภาพันธ์</option>
										<option value="03" <?= date('n') == '03' ? 'selected' : ''; ?>>มีนาคม</option>
										<option value="04" <?= date('n') == '04' ? 'selected' : ''; ?>>เมษายน</option>
										<option value="05" <?= date('n') == '05' ? 'selected' : ''; ?>>พฤษภาคม</option>
										<option value="06" <?= date('n') == '06' ? 'selected' : ''; ?>>มิถุนายน</option>
										<option value="07" <?= date('n') == '07' ? 'selected' : ''; ?>>กรกฎาคม</option>
										<option value="08" <?= date('n') == '08' ? 'selected' : ''; ?>>สิงหาคม</option>
										<option value="09" <?= date('n') == '09' ? 'selected' : ''; ?>>กันยายน</option>
										<option value="10" <?= date('n') == '10' ? 'selected' : ''; ?>>ตุลาคม</option>
										<option value="11" <?= date('n') == '11' ? 'selected' : ''; ?>>พฤศจิกายน</option>
										<option value="12" <?= date('n') == '12' ? 'selected' : ''; ?>>ธันวาคม</option>
									</select>
									<select name="pickYear" id="pickYear" style="margin-left:5px;">
										<option value="2019" <?= date('Y') == '2019' ? 'selected' : ''; ?>>2019</option>
										<option value="2020" <?= date('Y') == '2020' ? 'selected' : ''; ?>>2020</option>
										<option value="2021" <?= date('Y') == '2021' ? 'selected' : ''; ?>>2021</option>
										<option value="2022" <?= date('Y') == '2022' ? 'selected' : ''; ?>>2022</option>
										<option value="2023" <?= date('Y') == '2023' ? 'selected' : ''; ?>>2023</option>
										<option value="2024" <?= date('Y') == '2024' ? 'selected' : ''; ?>>2024</option>
										<option value="2025" <?= date('Y') == '2025' ? 'selected' : ''; ?>>2025</option>
									</select>
									<select name="testType" style="margin-left:5px;">
										<option value="All" selected>All</option>
										<option value="DIC">DIC</option>
										<option value="Mobile">Mobile</option>
									</select>
									<select name="pickKp" style="margin-left:5px;">
										<option value="All" selected>All</option>
										<option value="MSM">MSM</option>
										<option value="TG">TG</option>
										<option value="TGM">TGM</option>
										<option value="MSW">MSW</option>
										<option value="TGSW">TGSW</option>
										<option value="FSW">FSW</option>
										<option value="PWID-Male">PWID-Male</option>
										<option value="PWID-Female">PWID-Female</option>
										<option value="Male">Male</option>
										<option value="Female">Female</option>
									</select>
									<select name="pickRoute" style="margin-left:5px;">
										<option value="All" selected>All</option>
										<option value="Reach">Reach</option>
										<option value="walk in">walk in</option>
									</select>
								</div>
							</div>
							<div class="row">
								<div class="col-lg-12">
									<div class="table-responsive" style="margin:0px 10px">
										<table class="table" id="clinicTable">
										</table>
									</div>
								</div>
							</div>
							<div class="row">
								<div class="col-lg-12">
									<div id="clinicChart" style="min-width: 310px; height: 400px; margin: 0 auto"></div>
								</div>
							</div>
						</div>
					</div>

				</div>
			</div>

			<!-- /.row -->
		</section>
		<!-- modal even section start -->
		<section>
			<div class="modal fade" id="addEventModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
				<div class="modal-dialog" role="document">
					<div class="modal-content">
						<!-- <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">บันทึกกิจกรรม/นัดหมาย</h5><span></span>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span>
                </button>
              </div> -->
						<input type="hidden" name="username" value="<?= $_SESSION['user'] ?>">

						<div class="modal-body">
							<form id="eventForm">

								<div class="form-group">
									<label for="event-topic" class="">หมวดหมู่</label><br>
									<div class="pretty p-icon p-round p-tada">
										<input value="นัดตรวจเลือด" class="" name="event-topic" type="radio" required>
										<div class="state p-primary">
											<i class="icon mdi mdi-check"></i>
											<label>นัดตรวจเลือด</label>
										</div>
									</div>
									<div class="pretty p-icon p-round p-tada">
										<input value="Mobile" class="" name="event-topic" type="radio" required>
										<div class="state p-primary">
											<i class="icon mdi mdi-check"></i>
											<label>Mobile Clinic</label>
										</div>
									</div>
									<div class="pretty p-icon p-round p-tada">
										<input value="ประชุม/อบรมสัมนา" class="" name="event-topic" type="radio" required>
										<div class="state p-primary">
											<i class="icon mdi mdi-check"></i>
											<label>ประชุม/อบรมสัมนา</label>
										</div>
									</div>
									<div class="pretty p-icon p-round p-tada">
										<input value="กิจกรรม" class="" name="event-topic" type="radio" required>
										<div class="state p-primary">
											<i class="icon mdi mdi-check"></i>
											<label>กิจกรรม</label>
										</div>
									</div>
									<div class="pretty p-icon p-round p-tada">
										<input value="เจ้าหน้าที่ลางาน" class="" name="event-topic" type="radio" required>
										<div class="state p-primary">
											<i class="icon mdi mdi-check"></i>
											<label>ลางาน/เจ้าหน้าที่หยุด</label>
										</div>
									</div>
								</div>
								<div class="form-group">
									<label for="event-title" class="col-form-label">หัวเรื่อง:</label>
									<input type="text" class="form-control" id="event-title" required autocomplete="off">
								</div>

								<div class="row">
									<div class="col-lg-6">
										<div class="form-group">
											<label for="event-location" class="col-form-label">สถานที่:</label>
											<input type="text" class="form-control" id="event-location" value="" required autocomplete="off">
										</div>
									</div>
									<div class="col-lg-6">
										<div class="form-group">
											<label for="event-cbs" class="col-form-label">ผู้บันทึก:</label>
											<input type="text" class="form-control" id="event-cbs" value="<?= $_SESSION['user'] ?>" required>
										</div>
									</div>
								</div>
								<div class="row">
									<div class="col-lg-6">
										<div class="form-group">
											<label for="event-start" class="col-form-label">เริ่ม:</label>
											<input type="text" class="form-control" id="event-start" required>
										</div>
									</div>
									<div class="col-lg-6">
										<div class="form-group">
											<label for="event-end" class="col-form-label">สิ้นสุด:</label>
											<input type="text" class="form-control" id="event-end" required>
										</div>
									</div>
								</div>
								<div class="form-group">
									<label for="event-description" class="col-form-label">รายละเอียด:</label>
									<textarea class="form-control" id="event-description" rows="6" required></textarea>
								</div>
								<div id="showInfo">
								</div>
						</div>
						<div class="modal-footer">
							<button type="button" class="hidden btn btn-danger pull-left" id="deleteEventBtn">Delete</button>
							<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
							<input type="submit" class="btn btn-primary" id="submitEvent" value="บันทึก">
						</div>
						</form>
					</div>
				</div>
			</div>
		</section>
		<!-- modal even section end -->
		<!-- /.content -->
	</div>
	<!-- /.content-wrapper -->

	<?php include '../layouts/footer.php' ?>
	<!-- /.control-sidebar -->
	<div class="control-sidebar-bg"></div>
</div>
<!-- ./wrapper -->



<?php include '../layouts/javascript.php' ?>
<script src="../dist/js/carlendarappointment.js"></script>
<!-- ********Chart Data Script******** -->

<script>
	$(document).ready(() => {

		const thaiMonth = ['มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน', 'กรกฎาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม'];
		const thaiShortMonth = ['ม.ค.62', 'ก.พ.62', 'มี.ค.62', 'เม.ย.62', 'พ.ค.', 'มิ.ย.62', 'ก.ค.62', 'ส.ค.62', 'ก.ย.62', 'ต.ค.62', 'พ.ย.62', 'ธ.ค.62'];
		const date = new Date();

		// clinic summary graph
		clinicChart();

		$('#pickMonth').on('change', function(e) {
			let pickYear = $('#pickYear').val();
			let pickTestType = $('select[name="testType"]').children('option:selected').val();
			let pickKp = $('select[name="pickKp"]').children('option:selected').val();
			let pickRoute = $('select[name="pickRoute"]').children('option:selected').val();
			clinicChart(this.value, pickYear, pickTestType, pickKp, pickRoute);
		});

		$('#pickYear').on('change', function(e) {
			let pickMonth = $('#pickMonth').val();
			let pickTestType = $('select[name="testType"]').children('option:selected').val();
			let pickKp = $('select[name="pickKp"]').children('option:selected').val();
			let pickRoute = $('select[name="pickRoute"]').children('option:selected').val();
			clinicChart(pickMonth, this.value, pickTestType, pickKp, pickRoute);
		});

		$('select[name="testType"]').on('change', function(e) {
			let pickMonth = $('#pickMonth').val();
			let pickYear = $('#pickYear').val();
			let pickKp = $('select[name="pickKp"]').children('option:selected').val();
			let pickRoute = $('select[name="pickRoute"]').children('option:selected').val();
			clinicChart(pickMonth, pickYear, this.value, pickKp, pickRoute);
		});

		$('select[name="pickKp"]').on('change', function(e) {
			let pickMonth = $('#pickMonth').val();
			let pickYear = $('#pickYear').val();
			let pickTestType = $('select[name="testType"]').children('option:selected').val();
			let pickRoute = $('select[name="pickRoute"]').children('option:selected').val();
			clinicChart(pickMonth, pickYear, pickTestType, this.value, pickRoute);
		});

		$('select[name="pickRoute"]').on('change', function(e) {
			let pickMonth = $('#pickMonth').val();
			let pickYear = $('#pickYear').val();
			let pickTestType = $('select[name="testType"]').children('option:selected').val();
			let pickKp = $('select[name="pickKp"]').children('option:selected').val();
			clinicChart(pickMonth, pickYear, pickTestType, pickKp, this.value);
		});

		function clinicChart(pickMonth = '', pickYear = '', pickTestType = 'All', pickKp = 'All', pickRoute = 'All') {

			pickYear = pickYear === '' ? (new Date()).getFullYear() : pickYear

			$.ajax({
				url: '../report/ajaxFetchClinicSummary.php',
				type: "POST",
				data: {
					pickMonth: pickMonth,
					pickYear: pickYear,
					pickTestType: pickTestType,
					pickKp: pickKp,
					pickRoute: pickRoute
				},
				success: function(data) {
					// console.log(data);
					const responses = JSON.parse(data);
					// console.log(responses.Tested);

					let sumTested = 0;
					let sumHiv = 0;
					let sumTpha = 0;

					if (responses.Tested) {
						sumTested = Math.abs(responses.Tested.reduce((sum, number) => sum + number));
						sumHiv = Math.abs(responses.HIV.reduce((sum, number) => sum + number));
						sumTpha = Math.abs(responses.TPHA.reduce((sum, number) => sum + number));
					}

					$('#clinicTable').html(`
					<tbody>
						<tr>
							<td style="color:#fff;background: #8085E9">ตรวจ</td>
							<td style="color:#fff;background: #8085E9">${sumTested}</td>
							<td style="color:#fff;background: #8085E9">100%</td>
							<td style="color:#fff;background: #F45B5B">HIV+</td>
							<td style="color:#fff;background: #F45B5B">${sumHiv}</td>
							<td style="color:#fff;background: #F45B5B">${Math.round(sumHiv/sumTested*100*100)/100}%</td>
							<td style="color:#fff;background: #8D4654">TPHA+</td>
							<td style="color:#fff;background: #8D4654">${sumTpha}</td>
							<td style="color:#fff;background: #8D4654">${Math.round(sumTpha/sumTested*100*100)/100}%</td>
						</tr>
					</tbody>
				`);

					$('#clinicTable').find('td').css('padding', '0px 8px');

					function sum(total, num) {
						return total - num;
					}

					var clinicChart = Highcharts;

					clinicChart.chart('clinicChart', {
						credits: {
							enabled: false
						},
						chart: {
							type: 'column'
						},
						title: {
							text: `สรุปบริการคลินิก เดือน ${thaiMonth[responses.month - 1]} ${parseInt(pickYear) + 543}`
						},
						subtitle: {
							text: 'Realtime updated'
						},
						yAxis: {
							allowDecimals: false,
							title: {
								text: 'จำนวน'
							}
						},
						xAxis: {
							categories: responses.categories,
							allowDecimals: false,
							title: {
								text: null
							}
						},
						yAxis: {
							min: 0,
							title: {
								text: 'จำนวนเคส',
								align: 'high'
							}
						},
						tooltip: {
							formatter: function() {
								return '<b>' + this.series.name + '</b><br/>' +
									this.point.y + ' เคส';
							}
						},
						plotOptions: {
							column: {
								dataLabels: {
									enabled: true,
									crop: false,
									overflow: 'none'
								}
							}
						},
						series: [{
							name: 'Tested',
							color: '#999EFF',
							data: responses.Tested
						}, {
							name: 'HIV+',
							color: '#FF7474',
							data: responses.HIV
						}, {
							name: 'TPHA+',
							color: '#A65F6D',
							data: responses.TPHA
						}],

					});
				}
			});
		}

		// clinic summary graph
	})

	const dashboardVue = new Vue({
		el: '#dashboardMainSectionVue',
		data: {
			startDate: moment().startOf('quarter').format('YYYY-MM-DD'),
			endDate: moment().endOf('quarter').format('YYYY-MM-DD'),
			startFY: moment().month() >= 9 ? moment().startOf('year').month(9).startOf('month').format('YYYY-MM-DD') : moment().startOf('year').subtract(1, 'year').month(9).startOf('month').format('YYYY-MM-DD'),
			endFY: moment().month() >= 9 ? moment().startOf('year').add(1, 'year').month(9).startOf('month').format('YYYY-MM-DD') : moment().startOf('year').month(9).startOf('month').format('YYYY-MM-DD'),
		},
		methods: {
			fetchNap() {

				this.$refs.overlay.classList.remove('hidden');

				axios.post('./ajaxFetchNapCount.php')
					.then(res => {

						console.log(res.data)

						this.$refs.overlay.classList.add('hidden');

						this.$refs.reachNapCount.className = res.data.reachNap > 0 ? 'text-danger' : 'text-success'
						this.$refs.clinicNapCount.className = res.data.clinicNap > 0 ? 'text-danger' : 'text-success'
						this.$refs.syphilisNapCount.className = res.data.syphilisNap > 0 ? 'text-danger' : 'text-success'
						this.$refs.cd4NapCount.className = res.data.cd4Nap > 0 ? 'text-danger' : 'text-success'
						this.$refs.vlNapCount.className = res.data.vlNap > 0 ? 'text-danger' : 'text-success'
						this.$refs.hcvNapCount.className = res.data.hcvNap > 0 ? 'text-danger' : 'text-success'

						this.$refs.reachNapCount.innerHTML = `สปสช Reach [ค้าง ${res.data.reachNap} เคส] -> <a target="_blank" href="../napktb/nhsoForReach.php?start=${this.startFY}&end=${this.endFY}&ecascade=true">${labelbig('เข้าหน้า NAP, KTB Reach', 'purple', '1rem')} </a>`
						this.$refs.clinicNapCount.innerHTML = `สปสช Clinic [ค้าง ${res.data.clinicNap} เคส] -> <a target="_blank" href="../napktb/nhsoForClinic.php?start=${this.startFY}&end=${this.endFY}&ecascade=true">${labelbig('เข้าหน้า NAP, KTB Clinic', 'purple', '1rem')} </a>`
						this.$refs.syphilisNapCount.innerHTML = `สปสช Syphilis [ค้าง ${res.data.syphilisNap} เคส] -> <a target="_blank" href="../napktb/nhsoForClinicSyphilis.php?start=${this.startFY}&end=${this.endFY}&ecascade=true">${labelbig('เข้าหน้า NAP, KTB Syphilis', 'purple', '1rem')} </a>`
						this.$refs.cd4NapCount.innerHTML = `สปสช CD4 [ค้าง ${res.data.cd4Nap} เคส] -> <a target="_blank" href="../napktb/nhsoForCD4.php?start=${this.startFY}&end=${this.endFY}&ecascade=true">${labelbig('เข้าหน้า NAP, KTB CD4', 'purple', '1rem')} </a>`
						this.$refs.vlNapCount.innerHTML = `สปสช VL [ค้าง ${res.data.vlNap} เคส] -> <a target="_blank" href="../napktb/nhsoForVL.php?start=${this.startFY}&end=${this.endFY}&ecascade=true">${labelbig('เข้าหน้า NAP, KTB VL', 'purple', '1rem')} </a>`
						this.$refs.hcvNapCount.innerHTML = `สปสช HCV [ค้าง ${res.data.hcvNap} เคส] -> <a target="_blank" href="../napktb/nhsoForHCV.php?start=${this.startFY}&end=${this.endFY}&ecascade=true">${labelbig('เข้าหน้า NAP, KTB HCV', 'purple', '1rem')} </a>`
					})
			},
		},
		mounted() {

		}
	})
</script>

<?php include '../layouts/end.php' ?>
