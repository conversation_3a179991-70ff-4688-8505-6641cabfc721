<?php

require '../session_setting.php';

// error_reporting(E_ALL);
// ini_set('display_errors', 1);

// ini_set('memory_limit', '512M');

if (!isset($_SESSION['user']) || empty($_SESSION['user']) || $_SESSION['user'] == null || $_SESSION['user'] == '') {
	flash('notlogin', 'กรุณา Login เพื่อใช้งาน', 'alert alert-danger');
	header("Location: ../index.php");
}

require '../helpers/pagesvisited.php'; //

$thaiMonth = ['มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน', 'กรกฎาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม'];
$currentMonth = date('n') - 1;
$currentYear = date('Y') + 543;
$currentYearChris = date('Y');
?>

<?php include '../layouts/header.php' ?>

<div class="wrapper">

	<style>
		#performanceTable th,
		#performanceTable td {
			border: 1px solid #000;
			text-align: center;
		}

		#nhsoTable th,
		#nhsoTable td {
			border: 1px solid #000;
			text-align: center;
		}

		.box>.overlay,
		.overlay-wrapper>.overlay,
		.box>.loading-img,
		.overlay-wrapper>.loading-img {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
		}

		.box .overlay,
		.overlay-wrapper .overlay {
			z-index: 50;
			background: rgba(255, 255, 255, 0.7);
			border-radius: 3px;
		}

		.box .overlay>.fa,
		.overlay-wrapper .overlay>.fa {
			position: absolute;
			top: 50%;
			left: 50%;
			margin-left: -15px;
			margin-top: -15px;
			color: #000;
			font-size: 30px;
		}

		.box .overlay.dark,
		.overlay-wrapper .overlay.dark {
			background: rgba(0, 0, 0, 0.5);
		}
	</style>

	<?php include '../layouts/topmenu.php' ?>
	<?php include '../layouts/sidebarmenu.php' ?>

	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header">
			<h1>
				Dashboard
				<small>สรุปผลการทำงาน</small>
			</h1>
			<ol class="breadcrumb">
				<li><a href="#"><i class="fas fa-tachometer-alt"></i> Home</a></li>
				<li class="active">Dashboard</li>
			</ol>
		</section>

		<!-- Main content -->
		<section id="dashboardMainSection" class="content">
			<?php flash('error'); ?>
			<?php flash('login'); ?>
			<?php flash('notAllow'); ?>

			<!-- Indicator table start -->

			<!-- Indicator table end -->
			<div class="row">
				<div class="col-lg-6">
					<div class="box box-primary box-solid" id="">
						<div class="box-header with-border" data-widget="collapse">
							<h3 class="box-title">เมนูลัด + Link จำเป็น</h3>
						</div>
						<div class="box-body">
							<div style="display:flex;justify-content:space-around;flex-wrap:wrap;">
								<div style="margin-bottom:10px">
									<a target="_blank" href="../services/reach.php" class="btn btn-default btn-sm">ฟอร์ม Reach</a>
									<a target="_blank" href="../services/recruit.php" class="btn btn-default btn-sm">ตาราง Reach</a>
								</div>
								<div style="margin-bottom:10px">
									<a target="_blank" href="../services/clinic.php" class="btn btn-default btn-sm">Clinic</a>
									<a target="_blank" href="../report/cliniclog.php" class="btn btn-default btn-sm">Clinic Summary</a>
								</div>
								<div style="margin-bottom:10px">
									<a target="_blank" href="../prep/prep_stock.php" class="btn btn-default btn-sm">PrEP Stock</a>
									<a target="_blank" href="../prep/prep_lists.php" class="btn btn-default btn-sm">PrEP Lists</a>
									<a target="_blank" href="../prep/prep_refill.php" class="btn btn-default btn-sm">PrEP Refill</a>
									<a target="_blank" href="../prep/prep_hospital_request.php" class="btn btn-default btn-sm">PrEP Request Approve</a>
								</div>
								<div style="margin-bottom:10px">
									<a target="_blank" href="../retain/negativeRetain.php" class="btn btn-default btn-sm">Negative Retention</a>
								</div>
								<div style="margin-bottom:10px">
									<a target="_blank" href="https://admin.testmenow.net/" class="btn btn-success btn-sm">TestMeNow</a>
									<a target="_blank" href="https://ecascadeview.org/" class="btn btn-success btn-sm">eCascade View</a>
									<a target="_blank" href="https://www.commcarehq.org/a/epm-thai/login/" class="btn btn-success btn-sm">COMMCARE</a>
								</div>
								<div style="margin-bottom:10px">
									<a target="_blank" href="https://healthcare.ihri.org/" class="btn btn-warning btn-sm">IHRI Services</a>
									<a target="_blank" href="http://161.82.242.164/iclinic" class="btn btn-warning btn-sm">iClinic</a>
									<a target="_blank" href="http://161.82.242.164/weclinic" class="btn btn-warning btn-sm">We Clinic</a>
								</div>
								<div style="margin-bottom:10px">
									<a target="_blank" href="http://dmis.nhso.go.th/NAPPLUS" class="btn btn-info btn-sm">NAP Plus</a>
									<a target="_blank" href="https://rtcmplus.ddc.moph.go.th/" class="btn btn-info btn-sm">RTCMPLUS +</a>
								</div>
								<div style="margin-bottom:10px">
									<a target="_blank" href="https://eservices.nhso.go.th/eServices/mobile/login.xhtml" class="btn btn-primary btn-sm">ตรวจสอบสิทธิรักษา</a>
									<a target="_blank" href="https://hivhub.ddc.moph.go.th/response.php" class="btn btn-primary btn-sm">HIV Info Hub</a>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="hidden col-lg-9">
					<div class="box box-primary box-solid" id="performanceTable">
						<div class="box-header with-border" data-widget="collapse">
							<h3 class="box-title">Performance</h3>
						</div>
						<div class="box-body">
							<div class="row">
								<div class="col-lg-12">
									<div class="hidden table-responsive">
										<table class="table table-borderd">
											<thead>
												<tr>
													<th class="bg-orange">LINKAGES</th>
													<th colspan="2">KP_PREV</th>
													<th colspan="2">PrEP_NEW</th>
													<th colspan="2">PrEP_CURR</th>
													<th colspan="2">HTS_TST</th>
													<th colspan="2">HTS_POS</th>
													<th colspan="2">HTS_INDEX</th>
													<th colspan="2">HTS_INDEX_POS</th>
													<th colspan="2">TX_NEW_VERIFY</th>
													<th colspan="2">TX_PVLS</th>
													<th>TX_ML</th>
												</tr>
											</thead>
											<tbody>
												<tr class="bg-success">
													<td>Target</td>
													<td>486</td>
													<td>100%</td>
													<td>52</td>
													<td>100%</td>
													<td>46</td>
													<td>100%</td>
													<td>285</td>
													<td>100%</td>
													<td>39</td>
													<td>100%</td>
													<td>74</td>
													<td>100%</td>
													<td>22</td>
													<td>100%</td>
													<td>39</td>
													<td>100%</td>
													<td>110</td>
													<td>100%</td>
													<td>-</td>
												</tr>
												<tr class="bg-info">
													<td>Q1</td>
													<td>287</td>
													<td>59%</td>
													<td>17</td>
													<td>33%</td>
													<td>65</td>
													<td>141%</td>
													<td>219</td>
													<td>77%</td>
													<td>10</td>
													<td>26%</td>
													<td>2</td>
													<td>3%</td>
													<td>0</td>
													<td>0%</td>
													<td>10</td>
													<td>26%</td>
													<td>31</td>
													<td>28%</td>
													<td>122</td>
												</tr>
												<tr class="bg-info">
													<td>Q2</td>
													<td>227</td>
													<td>46.7%</td>
													<td>13</td>
													<td>25%</td>
													<td>41</td>
													<td>89.13%</td>
													<td>205</td>
													<td>71.93%</td>
													<td>3</td>
													<td>7.69%</td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<td>2</td>
													<td>5.13%</td>
													<td>8</td>
													<td>7.27%</td>
													<td></td>
												</tr>
												<tr class="bg-primary">
													<td>คงเหลือ</td>
													<td>-28</td>
													<td>-5.7%</td>
													<td>22</td>
													<td>42%</td>
													<td>-60</td>
													<td>-130.13%</td>
													<td>-139</td>
													<td>-48.77%</td>
													<td>26</td>
													<td>66.67%</td>
													<td>72</td>
													<td>97%</td>
													<td>22</td>
													<td>100%</td>
													<td>27</td>
													<td>69.23%</td>
													<td>71</td>
													<td>64.73%</td>
													<td>122</td>
												</tr>
											</tbody>
										</table>
									</div>
								</div>
							</div>
							<?php
							// $fetchNHSO = nhsoDataForGraph(2020)->fetchall();
							// $nhsoTotal = nhsoDataForGraph(2020)->rowCount();

							// $nhsoMSM = 0;
							// $nhsoMSW = 0;
							// $nhsoTG = 0;

							// foreach ($fetchNHSO as $nhso) {
							// 	if ($nhso->kp == 'MSM') {
							// 		$nhsoMSM++;
							// 	}
							// 	if ($nhso->kp == 'MSW') {
							// 		$nhsoMSW++;
							// 	}
							// 	if ($nhso->kp == 'TG') {
							// 		$nhsoTG++;
							// 	}
							// }

							// $nhsoRetainData = nhsoRetainDataForGraph(2020)->fetchall();
							// $nhsoRetainTotal = nhsoRetainDataForGraph(2020)->rowCount();

							// $nhsoRetainMSM = 0;
							// $nhsoRetainMSW = 0;
							// $nhsoRetainTG = 0;

							// foreach ($nhsoRetainData as $nhsoRetain) {
							// 	if ($nhsoRetain->kp == 'MSM') {
							// 		$nhsoRetainMSM++;
							// 	}
							// 	if ($nhsoRetain->kp == 'MSW') {
							// 		$nhsoRetainMSW++;
							// 	}
							// 	if ($nhsoRetain->kp == 'TG') {
							// 		$nhsoRetainTG++;
							// 	}
							// }

							?>
							<div class="row">
								<div class="col-lg-12">
									<div class="table-responsive">
										<table class="table table-borderd" id="nhsoTable">
											<thead>
												<tr>
													<th rowspan="2" class="bg-maroon" style="vertical-align : middle;text-align:center;">สปสช <br> <small>&lt;อัพเดทอัตโนมัติ&gt;</small></th>
													<th colspan="8">Main</th>
													<th colspan="8">Retain</th>
												</tr>
												<tr>
													<th colspan="2">MSM</th>
													<th colspan="2">MSW</th>
													<th colspan="2">TG</th>
													<th colspan="2">All</th>
													<th colspan="2">MSM</th>
													<th colspan="2">MSW</th>
													<th colspan="2">TG</th>
													<th colspan="2">All</th>
												</tr>
											</thead>
											<tbody>
												<tr class="bg-success">
													<td>Target</td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<!-- <td>Target</td>
													<td>600</td>
													<td>100%</td>
													<td>100</td>
													<td>100%</td>
													<td>50</td>
													<td>100%</td>
													<td>750</td>
													<td>100%</td>
													<td>480</td>
													<td>100%</td>
													<td>80</td>
													<td>100%</td>
													<td>40</td>
													<td>100%</td>
													<td>600</td>
													<td>100%</td> -->
												</tr>
												<tr class="bg-info">
													<td>ทำได้</td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<!-- <td>ทำได้</td>
													<td><?= $nhsoMSM ?></td>
													<td><?= round($nhsoMSM / 600 * 100, 2) . '%' ?></td>
													<td><?= $nhsoMSW ?></td>
													<td><?= round($nhsoMSW / 100 * 100, 2) . '%' ?></td>
													<td><?= $nhsoTG ?></td>
													<td><?= round($nhsoTG / 50 * 100, 2) . '%' ?></td>
													<td><?= $nhsoTotal ?></td>
													<td><?= round($nhsoTotal / 750 * 100, 2) . '%' ?></td>
													<td><?= $nhsoRetainMSM ?></td>
													<td><?= round($nhsoRetainMSM / 480 * 100, 2) . '%' ?></td>
													<td><?= $nhsoRetainMSW ?></td>
													<td><?= round($nhsoRetainMSW / 80 * 100, 2) . '%' ?></td>
													<td><?= $nhsoRetainTG ?></td>
													<td><?= round($nhsoRetainTG / 40 * 100, 2) . '%' ?></td>
													<td><?= $nhsoRetainTotal ?></td>
													<td><?= round($nhsoRetainTotal / 600 * 100, 2) . '%' ?></td> -->
												</tr>
												<tr class="bg-primary">
													<td>คงเหลือ</td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<!-- <td>คงเหลือ</td>
													<td><?= 600 - $nhsoMSM ?></td>
													<td><?= 100 - round($nhsoMSM / 600 * 100, 2) . '%' ?></td>
													<td><?= 100 - $nhsoMSW ?></td>
													<td><?= 100 - round($nhsoMSW / 100 * 100, 2) . '%' ?></td>
													<td><?= 50 - $nhsoTG ?></td>
													<td><?= 100 - round($nhsoTG / 50 * 100, 2) . '%' ?></td>
													<td><?= 750 - $nhsoTotal ?></td>
													<td><?= 100 - round($nhsoTotal / 750 * 100, 2) . '%' ?></td>
													<td><?= 480 - $nhsoRetainMSM ?></td>
													<td><?= 100 - round($nhsoRetainMSM / 480 * 100, 2) . '%' ?></td>
													<td><?= 80 - $nhsoRetainMSW ?></td>
													<td><?= 100 - round($nhsoRetainMSW / 80 * 100, 2) . '%' ?></td>
													<td><?= 40 - $nhsoRetainTG ?></td>
													<td><?= 100 - round($nhsoRetainTG / 40 * 100, 2) . '%' ?></td>
													<td><?= 600 - $nhsoRetainTotal ?></td>
													<td><?= 100 - round($nhsoRetainTotal / 600 * 100, 2) . '%' ?></td> -->
												</tr>
											</tbody>
										</table>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="col-lg-4">
					<div class="box box-primary box-solid">
						<div class="box-header with-border" data-widget="collapse" style="background:#D81B60;">
							<h3 class="box-title">คงค้างบันทึก NAP ปีงบ 2026</h3>
						</div>
						<div class="box-body">
							<!-- <a href="https://www.commcarehq.org/a/epm-thai" target="_blank" class="btn btn-primary btn-xs" style="margin-bottom:5px;">หน้า COMMCARE</a> -->
							<a href="http://dmis.nhso.go.th/NAPPLUS/login.jsp" target="_blank" class="btn btn-success btn-xs" style="margin-bottom:5px;">ไป NAP หน้าเหลือง</a>
							<button @click="fetchNap()" type='button' class="btn btn-warning btn-xs" style="margin-bottom:5px;"><i class="fas fa-sync"></i> ดึงข้อมูล</button>
							<pre>
<span ref="reachNapCount"></span>
<span ref="clinicNapCount"></span>
<span ref="syphilisNapCount"></span>
<span ref="cd4NapCount"></span>
<span ref="vlNapCount"></span>
<span ref="hcvNapCount"></span>
</pre>
						</div>

						<div class="hidden overlay" ref="overlay">
							<i class="fa fa-refresh fa-spin"></i>
						</div>
					</div>
				</div>
			</div>

			<!-- ปฎิทินกิจกรรม -->
			<div class="row">
				<div class="col-md-6">
					<div class="box box-primary box-solid" id="workCalendar">
						<div class="box-header with-border" data-widget="collapse">
							<h3 class="box-title">ปฎิทินงาน</h3>
						</div>
						<div class="box-body">
							<div class="row">
								<div class="col-lg-12">
									<div id='calendar'>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="box box-primary box-solid">
						<div class="box-header with-border" data-widget="collapse">
							<h3 class="box-title">รายการติดตามเคส ผลลบ</h3>
						</div>
						<div>
						</div>
						<div class="box-body">
							<div class="form-group">
								<button @click="filterQuarterRetainNeg('fy21')" class="btn btn-primary" :class="{'active' : select_fy == 'fy21'}">FY21</button>
								<button @click="filterQuarterRetainNeg('fy22')" class="btn btn-primary" :class="{'active' : select_fy == 'fy22'}">FY22</button>
								<button @click="filterQuarterRetainNeg('fy23')" class="btn btn-primary" :class="{'active' : select_fy == 'fy23'}">FY23</button>
								<button @click="filterQuarterRetainNeg('fy24')" class="btn btn-primary" :class="{'active' : select_fy == 'fy24'}">FY24</button>
								<button @click="filterQuarterRetainNeg('fy25')" class="btn btn-primary" :class="{'active' : select_fy == 'fy25'}">FY25</button>
								<button @click="filterQuarterRetainNeg('fy26')" class="btn btn-primary" :class="{'active' : select_fy == 'fy26'}">FY26</button>
								<button @click="filterQuarterRetainNeg(null, '1')" class="btn btn-info" :class="{'active' : quarter == '1'}">Q1</button>
								<button @click="filterQuarterRetainNeg(null, '2')" class="btn btn-info" :class="{'active' : quarter == '2'}">Q2</button>
								<button @click="filterQuarterRetainNeg(null, '3')" class="btn btn-info" :class="{'active' : quarter == '3'}">Q3</button>
								<button @click="filterQuarterRetainNeg(null, '4')" class="btn btn-info" :class="{'active' : quarter == '4'}">Q4</button>
								<button @click="filterQuarterRetainNeg(null, 'all')" class="btn btn-info" :class="{'active' : quarter == 'all'}">All</button>
							</div>
							<div id="negativeRetainGraph" style="min-width: 310px; height: 400px; margin: 0 auto"></div>
						</div>
					</div>
				</div>
				<div class="col-md-6">
					<div class="box box-primary box-solid <?= !in_array($_SESSION['site_specific']->sitename2, ['CAREMAT']) ? 'hidden' : '' ?>" id="temGraph">
						<div class="box-header with-border" data-widget="collapse">
							<h3 class="box-title">อุณหภูมิ/ความชื้น ห้องเก็บถุงยาง</h3>
						</div>
						<div class="box-body">
							<div class="row">
								<div class="col-lg-12">
									<table id="tempTable" class="table table-striped">
										<thead>
											<tr>
												<th>ตำแหน่ง</th>
												<th>อุณหภูมิ</th>
												<th>ความชื้น</th>
												<th>วันที่</th>
												<th>เวลา</th>
											</tr>
										</thead>
										<tbody>
										</tbody>
									</table>
								</div>
							</div>
							<div class="row">
								<div class="col-lg-12">
									<div id="tempGraph" style="min-width: 310px; height: 400px; margin: 0 auto"></div>
								</div>
							</div>
						</div>
					</div>
					<div class="box box-primary box-solid" id="retainPostive">
						<div class="box-header with-border" data-widget="collapse">
							<h3 class="box-title">การติดตามเคส ผลบวก ไตรมาส <?= checkQuarter()['this_quarter'] ?> ปีงบประมาณ <?= checkQuarter()['this_fy'] ?></h3>
						</div>
						<div class="box-body">
							<div class="row">
								<div class="col-lg-12">
									<?php
									echo labelbig('กราฟแสดง รายการเคส จำนวนติดตามได้|จำนวนครั้งที่ตาม', 'success') . '<a target="_blank" href="/caresupport/hiv.php" class="btn btn-sm btn-primary pull-right">เข้าหน้าติดตามเคส</a><br><br>';
									// fetch retain hiv if cbs exist echo out progressbar
									$duration = checkQuarter()['this_quarter_start'];

									$retainData = getRetainHivRecords($duration)->fetchall();

									$retainCbsLists = [];
									$progressColorLists = ['yellow', 'teal', 'success', 'maroon', 'danger', 'purple', 'info'];

									foreach ($retainData as $item) {
										if (!in_array($item->cbs, $retainCbsLists)) {
											$retainCbsLists[] = $item->cbs;
										}
									}

									foreach ($retainCbsLists as $key => $retainCbs) {

										$color = $key % count($progressColorLists);

										echo retainProgressBar($retainCbs, $progressColorLists[$color], getRetainCountSuccess($retainCbs, $duration)->rowCount(), 300, getRetainCountAll($retainCbs, $duration)->rowCount());
									}

									?>
								</div>
							</div>
						</div>
					</div>
					<div class="hidden box box-danger box-solid" id="positiveYieldBox">
						<div class="box-header with-border" data-widget="collapse">
							<h3 class="box-title">HIV & TPHA Positive Yield</h3>
						</div>
						<div class="box-body">
							<div class="row">
								<div class="col-lg-12">
									<div id="yieldChart" style="min-width: 310px; height: 400px; margin: 0 auto"></div>
								</div>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col-lg-12">
							<div class="box box-success box-solid" id="mobileClinicBox">
								<div class="box-header with-border" data-widget="collapse">
									<h3 class="box-title">ข้อมูลสรุป สิทธิรักษา</h3>
								</div>
								<div class="box-body">
									<div class="row">
										<div class="col-lg-5">
											<div class="form-group">
												<label for="healthcare_range">เลือกช่วงเวลา</label>
												<input type="text" class="form-control" id="healthcare_range">
											</div>
										</div>
									</div>
									<div class="row">
										<div class="col-lg-12">
											<div id="healthcare_pie" style="min-width: 310px; height: 400px; margin: 0 auto"></div>
										</div>
									</div>
									<div class="row">
										<div class="col-lg-12">
											<table style="max-width: 500px;margin:auto; border:1px #ccc solid" id="healthCareChart" class="table text-center table-striped">
												<thead>
													<tr>
														<th>No</th>
														<th>สิทธิรักษา</th>
														<th v-html="`จำนวนรวม (<span class='text-danger'>${healthcare_total}</span>)`"></th>
														<th>%</th>
													</tr>
												</thead>
												<tbody>
												</tbody>
											</table>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="box box-primary box-solid" id="clinicChartBox">
						<div class="box-header with-border" data-widget="collapse">
							<h3 class="box-title">สรุปข้อมูลบริการคลินิก</h3>
						</div>
						<div class="box-body">
							<div class="row" style="margin-bottom:10px">
								<div class="col-lg-12" style="display:flex; align-items:center">
									<select name="pickMonth" id="pickMonth">
										<option value="01" <?= date('n') == '01' ? 'selected' : ''; ?>>มกราคม</option>
										<option value="02" <?= date('n') == '02' ? 'selected' : ''; ?>>กุมภาพันธ์</option>
										<option value="03" <?= date('n') == '03' ? 'selected' : ''; ?>>มีนาคม</option>
										<option value="04" <?= date('n') == '04' ? 'selected' : ''; ?>>เมษายน</option>
										<option value="05" <?= date('n') == '05' ? 'selected' : ''; ?>>พฤษภาคม</option>
										<option value="06" <?= date('n') == '06' ? 'selected' : ''; ?>>มิถุนายน</option>
										<option value="07" <?= date('n') == '07' ? 'selected' : ''; ?>>กรกฎาคม</option>
										<option value="08" <?= date('n') == '08' ? 'selected' : ''; ?>>สิงหาคม</option>
										<option value="09" <?= date('n') == '09' ? 'selected' : ''; ?>>กันยายน</option>
										<option value="10" <?= date('n') == '10' ? 'selected' : ''; ?>>ตุลาคม</option>
										<option value="11" <?= date('n') == '11' ? 'selected' : ''; ?>>พฤศจิกายน</option>
										<option value="12" <?= date('n') == '12' ? 'selected' : ''; ?>>ธันวาคม</option>
									</select>
									<select name="pickYear" id="pickYear" style="margin-left:5px;">
										<option value="2019" <?= date('Y') == '2019' ? 'selected' : ''; ?>>2019</option>
										<option value="2020" <?= date('Y') == '2020' ? 'selected' : ''; ?>>2020</option>
										<option value="2021" <?= date('Y') == '2021' ? 'selected' : ''; ?>>2021</option>
										<option value="2022" <?= date('Y') == '2022' ? 'selected' : ''; ?>>2022</option>
										<option value="2023" <?= date('Y') == '2023' ? 'selected' : ''; ?>>2023</option>
										<option value="2024" <?= date('Y') == '2024' ? 'selected' : ''; ?>>2024</option>
										<option value="2025" <?= date('Y') == '2025' ? 'selected' : ''; ?>>2025</option>
										<option value="2026" <?= date('Y') == '2026' ? 'selected' : ''; ?>>2026</option>
									</select>
									<select name="testType" style="margin-left:5px;">
										<option value="All" selected>All</option>
										<option value="DIC">DIC</option>
										<option value="Mobile">Mobile</option>
									</select>
									<select name="pickKp" style="margin-left:5px;">
										<option value="All" selected>All</option>
										<option value="MSM">MSM</option>
										<option value="TG">TG</option>
										<option value="TGM">TGM</option>
										<option value="MSW">MSW</option>
										<option value="TGSW">TGSW</option>
										<option value="FSW">FSW</option>
										<option value="PWID-Male">PWID-Male</option>
										<option value="PWID-Female">PWID-Female</option>
										<option value="Male">Male</option>
										<option value="Female">Female</option>
									</select>
									<select name="pickRoute" style="margin-left:5px;">
										<option value="All" selected>All</option>
										<option value="Reach">Reach</option>
										<option value="walk in">walk in</option>
									</select>
								</div>
							</div>
							<div class="row">
								<div class="col-lg-12">
									<div class="table-responsive" style="margin:0px 10px">
										<table class="table" id="clinicTable">
										</table>
									</div>
								</div>
							</div>
							<div class="row">
								<div class="col-lg-12">
									<div id="clinicChart" style="min-width: 310px; height: 400px; margin: 0 auto"></div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<!-- Reach -->
			<div class="row">
				<div class="col-md-12">
					<div class="box box-danger box-solid" id="boxThisMonth">
						<div class="box-header with-border" data-widget="collapse">
							<h3 class="box-title"><?= "Reach " . $thaiMonth[$currentMonth] . " " . $currentYear ?></h3>
							<!-- <div class="box-tools pull-right">
									<button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i></button>
								</div> -->
						</div>
						<div class="box-body">
							<div class="row">
								<div class="col-md-8">
									<div class="chart">
										<div id="containerThisMonth" style="width:100%; height:300px;"></div>
									</div>
								</div>
								<div class="col-md-4">
									<?php
									// fetch retain hiv if cbs exist echo out progressbar
									$thisYear = date('Y');
									$thisMonth = date('m');

									$setThisMonthDate = date("Y-m-01");

									$data = $conn->prepare("SELECT cbs FROM reach where reachdate >= ?");
									$data->execute([$setThisMonthDate]);

									$reachData = $data->fetchall();

									$reachCbsLists = [];
									$progressColorLists = ['yellow', 'teal', 'success', 'maroon', 'danger', 'purple', 'info'];

									foreach ($reachData as $item) {
										if (!in_array($item->cbs, $reachCbsLists)) {
											$reachCbsLists[] = $item->cbs;
										}
									}

									$cbsCount = 0;
									$color = 0;

									foreach ($reachCbsLists as $key => $reachCbs) {

										$color = $key % count($progressColorLists);

										echo cbsprogressbar($reachCbs, $progressColorLists[$color], $thisMonth, $thisYear);
									}
									?>
								</div>
							</div>
						</div>
					</div>
					<div class="box box-success box-solid" id="boxLastMonth">
						<div class="box-header with-border" data-widget="collapse">
							<h3 class="box-title"><?= "Reach " . $thaiMonth[$currentMonth == 0 ? 11 : $currentMonth - 1] . " " . ($currentMonth == 0 ? $currentYear - 1 : $currentYear) ?></h3>
						</div>
						<div class="box-body">
							<div class="row">
								<div class="col-md-8">
									<div class="chart">
										<div id="containerLastMonth" style="width:100%; height:300px;"></div>
									</div>
								</div>
								<div class="col-md-4">
									<?php
									// fetch retain hiv if cbs exist echo out progressbar
									$lastYear = date('m') == 1 ? date('Y') - 1 : date('Y');
									$lastMonth = date('m') == 1 ? 12 : date('m') - 1;

									$setThisMonthDate = date("Y-m-01");
									$setLastMonthDate = $lastYear . '-' . $lastMonth . '-01';

									$data = $conn->prepare("SELECT cbs FROM reach where reachdate >= ? AND reachdate < ?");
									$data->execute([$setLastMonthDate, $setThisMonthDate]);

									$reachData = $data->fetchall();

									$reachCbsLists = [];
									$progressColorLists = ['yellow', 'teal', 'success', 'maroon', 'danger', 'purple', 'info'];

									foreach ($reachData as $item) {
										if (!in_array($item->cbs, $reachCbsLists)) {
											$reachCbsLists[] = $item->cbs;
										}
									}

									$cbsCount = 0;
									$color = 0;

									foreach ($reachCbsLists as $key => $reachCbs) {

										$color = $key % count($progressColorLists);

										echo cbsprogressbar($reachCbs, $progressColorLists[$color], $lastMonth, $lastYear);
									}
									?>
								</div>
							</div>
						</div>
						<!-- <div class="box-footer">
							<div class="">

							</div>
						</div> -->
					</div>
				</div>
				<div class="hidden col-md-4">
					<div class="box box-info box-solid" id="retainLast4Month">
						<div class="box-header with-border" data-widget="collapse">
							<h3 class="box-title">Retain Positive 4 เดือนล่าสุด</h3>
						</div>
						<div class="box-body">
							<div class="row">
								<div class="col-md-12">
									<div id="retainposSummary" style="min-width: 310px; height: 400px; margin: 0 auto"></div>
								</div>
							</div>
						</div>
						<!-- <div class="box-footer">
							<div class="">

							</div>
						</div> -->
					</div>
				</div>
			</div>
			<!-- Reach -->
		</section>

		<!-- modal even section start -->
		<section>
			<div class="modal fade" id="addEventModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
				<div class="modal-dialog" role="document">
					<div class="modal-content">
						<!-- <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">บันทึกกิจกรรม/นัดหมาย</h5><span></span>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span>
                </button>
              </div> -->
						<input type="hidden" name="username" value="<?= $_SESSION['user'] ?>">

						<div class="modal-body">
							<form id="eventForm">

								<div class="form-group">
									<label for="event-topic" class="">หมวดหมู่</label><br>
									<div class="pretty p-icon p-round p-tada">
										<input value="นัดตรวจเลือด" class="" name="event-topic" type="radio" required>
										<div class="state p-primary">
											<i class="icon mdi mdi-check"></i>
											<label>นัดตรวจเลือด</label>
										</div>
									</div>
									<div class="pretty p-icon p-round p-tada">
										<input value="Mobile" class="" name="event-topic" type="radio" required>
										<div class="state p-primary">
											<i class="icon mdi mdi-check"></i>
											<label>Mobile Clinic</label>
										</div>
									</div>
									<div class="pretty p-icon p-round p-tada">
										<input value="ประชุม/อบรมสัมนา" class="" name="event-topic" type="radio" required>
										<div class="state p-primary">
											<i class="icon mdi mdi-check"></i>
											<label>ประชุม/อบรมสัมนา</label>
										</div>
									</div>
									<div class="pretty p-icon p-round p-tada">
										<input value="กิจกรรม" class="" name="event-topic" type="radio" required>
										<div class="state p-primary">
											<i class="icon mdi mdi-check"></i>
											<label>กิจกรรม</label>
										</div>
									</div>
									<div class="pretty p-icon p-round p-tada">
										<input value="เจ้าหน้าที่ลางาน" class="" name="event-topic" type="radio" required>
										<div class="state p-primary">
											<i class="icon mdi mdi-check"></i>
											<label>ลางาน/เจ้าหน้าที่หยุด</label>
										</div>
									</div>
								</div>
								<div class="form-group">
									<label for="event-title" class="col-form-label">หัวเรื่อง:</label>
									<input type="text" class="form-control" id="event-title" required autocomplete="off">
								</div>

								<div class="row">
									<div class="col-lg-6">
										<div class="form-group">
											<label for="event-location" class="col-form-label">สถานที่:</label>
											<input type="text" class="form-control" id="event-location" value="" required autocomplete="off">
										</div>
									</div>
									<div class="col-lg-6">
										<div class="form-group">
											<label for="event-cbs" class="col-form-label">ผู้บันทึก:</label>
											<input type="text" class="form-control" id="event-cbs" value="<?= $_SESSION['user'] ?>" required>
										</div>
									</div>
								</div>
								<div class="row">
									<div class="col-lg-6">
										<div class="form-group">
											<label for="event-start" class="col-form-label">เริ่ม:</label>
											<input type="text" class="form-control" id="event-start" required>
										</div>
									</div>
									<div class="col-lg-6">
										<div class="form-group">
											<label for="event-end" class="col-form-label">สิ้นสุด:</label>
											<input type="text" class="form-control" id="event-end" required>
										</div>
									</div>
								</div>
								<div class="form-group">
									<label for="event-description" class="col-form-label">รายละเอียด:</label>
									<textarea class="form-control" id="event-description" rows="6" required></textarea>
								</div>
								<div id="showInfo">
								</div>
						</div>
						<div class="modal-footer">
							<button type="button" class="hidden btn btn-danger pull-left" id="deleteEventBtn">Delete</button>
							<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
							<input type="submit" class="btn btn-primary" id="submitEvent" value="บันทึก">
						</div>
						</form>
					</div>
				</div>
			</div>
		</section>
		<!-- modal even section end -->
		<!-- /.content -->
	</div>
	<!-- /.content-wrapper -->

	<?php include '../layouts/footer.php' ?>
	<!-- /.control-sidebar -->
	<div class="control-sidebar-bg"></div>
</div>
<!-- ./wrapper -->



<?php include '../layouts/javascript.php' ?>
<script src="../dist/js/carlendarappointment.js"></script>

<script>
	//show box
</script>

<!-- ********Chart Data Script******** -->
<script>
	const thaiMonth = ['มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน', 'กรกฎาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม'];
	const thaiShortMonth = ['ม.ค.62', 'ก.พ.62', 'มี.ค.62', 'เม.ย.62', 'พ.ค.', 'มิ.ย.62', 'ก.ค.62', 'ส.ค.62', 'ก.ย.62', 'ต.ค.62', 'พ.ย.62', 'ธ.ค.62'];
	const date = new Date();

	//Reach Chart
	//LastMonth
	$(function() {
		var containerLastMonth = Highcharts;

		containerLastMonth.chart('containerLastMonth', {
			chart: {
				type: 'column'
			},
			title: {
				text: ''
			},
			credits: {
				enabled: false
			},
			subtitle: {
				text: '<strong>ยอด การเข้าถึงรายวัน ประจำเดือน ' + thaiMonth[(date.getMonth() == 0 ? date.getMonth() + 11 : date.getMonth() - 1)] + ' ' + (date.getMonth() == 0 ? date.getUTCFullYear() - 1 : date.getUTCFullYear()) + ' ( <span style="color:red;font-weight: bold;">รวม : ' + <?php echo sumcbs((date('n') == 1 ? date('n') + 11 : date('n') - 1), (date('n') == 1 ? date('Y') - 1 : date('Y'))); ?> + ' เคส </span>)</strong>',
			},
			xAxis: {
				categories: [
					<?php
					for ($i = 1; $i <= 30; $i++) {
						echo $i . ",";
					}
					echo 31;
					?>

				],
				crosshair: true
			},
			yAxis: {
				min: 0,
				title: {
					text: 'จำนวนเคส'
				}
			},
			tooltip: {
				headerFormat: '<span style="font-size:10px">วันที่ {point.key}</span><table>',
				pointFormat: '<tr><td style="color:{series.color};padding:0">{series.name}: </td>' +
					'<td style="padding:0"><b>{point.y} เคส</b></td></tr>',
				footerFormat: '</table>',
				shared: true,
				useHTML: true
			},
			plotOptions: {
				column: {
					pointPadding: 0,
					borderWidth: 0
				}
			},
			series: [{
				name: 'ยอดรวมรายวัน',
				data: [
					<?php
					$month = date('n') == 1 ? date('n') + 11 : date('n') - 1;
					$year = date('n') == 1 ? date('Y') - 1 : date('Y');
					for ($i = 1; $i <= cal_days_in_month(CAL_GREGORIAN, $month, $year) - 1; $i++) {
						echo total($i, $month, $year) . ",";
					}
					echo total(cal_days_in_month(CAL_GREGORIAN, $month, $year), $month, $year);
					?>
				]

			}]
		});
	});
	//ThisMonth
	$(function() {
		var containerThisMonth = Highcharts;

		containerThisMonth.chart('containerThisMonth', {
			chart: {
				type: 'column'
			},
			title: {
				text: ''
			},
			credits: {
				enabled: false
			},
			subtitle: {
				text: '<strong>ยอด การเข้าถึงรายวัน ประจำเดือน ' + thaiMonth[date.getMonth()] + ' ' + date.getUTCFullYear() + ' ( <span style="color:red;font-weight: bold;">รวม : ' + <?php echo sumcbs(date('n'), date('Y')); ?> + ' เคส </span>)</strong>',
			},
			xAxis: {
				categories: [
					<?php
					for ($i = 1; $i <= 30; $i++) {
						echo $i . ",";
					}
					echo 31;
					?>

				],
				crosshair: true
			},
			yAxis: {
				min: 0,
				title: {
					text: 'จำนวนเคส'
				}
			},
			tooltip: {
				headerFormat: '<span style="font-size:10px">วันที่ {point.key}</span><table>',
				pointFormat: '<tr><td style="color:{series.color};padding:0">{series.name}: </td>' +
					'<td style="padding:0"><b>{point.y} เคส</b></td></tr>',
				footerFormat: '</table>',
				shared: true,
				useHTML: true
			},
			plotOptions: {
				column: {
					pointPadding: 0,
					borderWidth: 0
				}
			},
			series: [{
				name: 'ยอดรวมรายวัน',
				data: [
					<?php
					$month = date('n');
					$year = date('Y');
					for ($i = 1; $i <= cal_days_in_month(CAL_GREGORIAN, date('n'), date('Y')) - 1; $i++) {
						echo total($i, $month, $year) . ",";
					}
					echo total(cal_days_in_month(CAL_GREGORIAN, date('n'), date('Y')), $month, $year);
					?>
				]

			}]
		});
	});
</script>

<script>
	$(document).ready(() => {

		// clinic summary graph
		clinicChart();

		$('#pickMonth').on('change', function(e) {
			let pickYear = $('#pickYear').val();
			let pickTestType = $('select[name="testType"]').children('option:selected').val();
			let pickKp = $('select[name="pickKp"]').children('option:selected').val();
			let pickRoute = $('select[name="pickRoute"]').children('option:selected').val();
			clinicChart(this.value, pickYear, pickTestType, pickKp, pickRoute);
		});

		$('#pickYear').on('change', function(e) {
			let pickMonth = $('#pickMonth').val();
			let pickTestType = $('select[name="testType"]').children('option:selected').val();
			let pickKp = $('select[name="pickKp"]').children('option:selected').val();
			let pickRoute = $('select[name="pickRoute"]').children('option:selected').val();
			clinicChart(pickMonth, this.value, pickTestType, pickKp, pickRoute);
		});

		$('select[name="testType"]').on('change', function(e) {
			let pickMonth = $('#pickMonth').val();
			let pickYear = $('#pickYear').val();
			let pickKp = $('select[name="pickKp"]').children('option:selected').val();
			let pickRoute = $('select[name="pickRoute"]').children('option:selected').val();
			clinicChart(pickMonth, pickYear, this.value, pickKp, pickRoute);
		});

		$('select[name="pickKp"]').on('change', function(e) {
			let pickMonth = $('#pickMonth').val();
			let pickYear = $('#pickYear').val();
			let pickTestType = $('select[name="testType"]').children('option:selected').val();
			let pickRoute = $('select[name="pickRoute"]').children('option:selected').val();
			clinicChart(pickMonth, pickYear, pickTestType, this.value, pickRoute);
		});

		$('select[name="pickRoute"]').on('change', function(e) {
			let pickMonth = $('#pickMonth').val();
			let pickYear = $('#pickYear').val();
			let pickTestType = $('select[name="testType"]').children('option:selected').val();
			let pickKp = $('select[name="pickKp"]').children('option:selected').val();
			clinicChart(pickMonth, pickYear, pickTestType, pickKp, this.value);
		});

		function clinicChart(pickMonth = '', pickYear = '', pickTestType = 'All', pickKp = 'All', pickRoute = 'All') {

			pickYear = pickYear === '' ? (new Date()).getFullYear() : pickYear

			$.ajax({
				url: '../report/ajaxFetchClinicSummary.php',
				type: "POST",
				data: {
					pickMonth: pickMonth,
					pickYear: pickYear,
					pickTestType: pickTestType,
					pickKp: pickKp,
					pickRoute: pickRoute
				},
				success: function(data) {
					// console.log(data);
					const responses = JSON.parse(data);
					// console.log(responses.Tested);

					let sumTested = 0;
					let sumHiv = 0;
					let sumTpha = 0;

					if (responses.Tested) {
						sumTested = Math.abs(responses.Tested.reduce((sum, number) => sum + number));
						sumHiv = Math.abs(responses.HIV.reduce((sum, number) => sum + number));
						sumTpha = Math.abs(responses.TPHA.reduce((sum, number) => sum + number));
					}

					$('#clinicTable').html(`
					<tbody>
						<tr>
							<td style="color:#fff;background: #8085E9">ตรวจ</td>
							<td style="color:#fff;background: #8085E9">${sumTested}</td>
							<td style="color:#fff;background: #8085E9">100%</td>
							<td style="color:#fff;background: #F45B5B">HIV+</td>
							<td style="color:#fff;background: #F45B5B">${sumHiv}</td>
							<td style="color:#fff;background: #F45B5B">${Math.round(sumHiv/sumTested*100*100)/100}%</td>
							<td style="color:#fff;background: #8D4654">TPHA+</td>
							<td style="color:#fff;background: #8D4654">${sumTpha}</td>
							<td style="color:#fff;background: #8D4654">${Math.round(sumTpha/sumTested*100*100)/100}%</td>
						</tr>
					</tbody>
				`);

					$('#clinicTable').find('td').css('padding', '0px 8px');

					function sum(total, num) {
						return total - num;
					}

					var clinicChart = Highcharts;

					clinicChart.chart('clinicChart', {
						credits: {
							enabled: false
						},
						chart: {
							type: 'column'
						},
						title: {
							text: `สรุปบริการคลินิก เดือน ${thaiMonth[responses.month - 1]} ${parseInt(pickYear) + 543}`
						},
						subtitle: {
							text: 'Realtime updated'
						},
						yAxis: {
							allowDecimals: false,
							title: {
								text: 'จำนวน'
							}
						},
						xAxis: {
							categories: responses.categories,
							allowDecimals: false,
							title: {
								text: null
							}
						},
						yAxis: {
							min: 0,
							title: {
								text: 'จำนวนเคส',
								align: 'high'
							}
						},
						tooltip: {
							formatter: function() {
								return '<b>' + this.series.name + '</b><br/>' +
									this.point.y + ' เคส';
							}
						},
						plotOptions: {
							column: {
								dataLabels: {
									enabled: true,
									crop: false,
									overflow: 'none'
								}
							}
						},
						series: [{
							name: 'Tested',
							color: '#999EFF',
							data: responses.Tested
						}, {
							name: 'HIV+',
							color: '#FF7474',
							data: responses.HIV
						}, {
							name: 'TPHA+',
							color: '#A65F6D',
							data: responses.TPHA
						}],

					});
				}
			});
		}

		// clinic summary graph

		// HIV & TPHA Positive Yield
		positiveYieldChart()

		function positiveYieldChart() {
			$.ajax({
				url: '../report/ajaxFetchPositiveYield.php',
				type: "POST",
				data: {},
				success: function(data) {

					const responses = JSON.parse(data);

					const dataList = []
					let yearList = []
					responses.clinicFetch[0].forEach(item => {

						let service_date = sqlToJsDate(item.service_date)

						if (!yearList.includes(service_date.getFullYear())) {
							yearList.push(service_date.getFullYear())
						}
					})

					let hivEachYear = []
					let tphaEachYear = []
					let monthInYear = []
					let monthList = []

					yearList.forEach(year => {

						monthInYear.push(responses.clinicFetch[0].filter(item => {
							if (item.hiv_result == 'R' && sqlToJsDate(item.service_date).getFullYear() == year && !monthList.includes(sqlToJsDate(item.service_date).getMonth())) {

								monthList.push(sqlToJsDate(item.service_date).getMonth())

								return true

							}
						}))

						monthList = []

						hivEachYear.push((responses.clinicFetch[0].filter(item => item.hiv_result == 'R' && sqlToJsDate(item.service_date).getFullYear() == year).length))
						tphaEachYear.push((responses.clinicFetch[0].filter(item => item.sti_result == 'R' && sqlToJsDate(item.service_date).getFullYear() == year).length))
					})

					// console.log(yearList)
					// console.log(hivEachYear)
					// console.log(tphaEachYear)
					// console.log(monthInYear)

					var yieldChart = Highcharts;

					yieldChart.chart('yieldChart', {
						credits: {
							enabled: false
						},
						chart: {
							type: 'line'
						},
						title: {
							text: `กราฟแสดงยอดเคส ผลบวก`
						},
						subtitle: {
							text: 'Realtime updated'
						},
						yAxis: {
							allowDecimals: false,
							title: {
								text: 'จำนวน'
							}
						},
						xAxis: {
							categories: ['v', 'w', 'y', 'x', 'z'],
							allowDecimals: false,
							title: {
								text: null
							}
						},
						yAxis: {
							min: 0,
							title: {
								text: 'จำนวนเคส',
								align: 'high'
							}
						},
						tooltip: {
							formatter: function() {
								return '<b>' + this.series.name + '</b><br/>' +
									this.point.y + ' เคส';
							}
						},
						plotOptions: {
							column: {
								dataLabels: {
									enabled: true,
									crop: false,
									overflow: 'none'
								}
							}
						},
						series: [{
							name: 'Tested',
							color: '#999EFF',
							data: [1, 2, 3, 4, 5]
						}, {
							name: 'HIV+',
							color: '#FF7474',
							data: [5, 4, 3, 2, 1]
						}, {
							name: 'TPHA+',
							color: '#A65F6D',
							data: [9, 8, 7, 6, 5]
						}],

					});
				}
			})
		}
		// HIV & TPHA Positive Yield

		// NHSO summary graph

		$.ajax({
			url: '../report/ajaxFetchTempData.php',
			type: "GET",
			success: function(data) {
				// console.log(data);
				const responses = JSON.parse(data);
				const tempDatas = [];
				const humidityDatas = [];
				const dateDatas = [];

				if (responses.length > 0) {
					responses.reverse().forEach(response => {
						if (parseFloat(response.temperature) >= 40) {
							tempDatas.push({
								marker: {
									fillColor: '#605CA8',
									lineWidth: 3,
									lineColor: "#605CA8"
								},
								y: parseFloat(response.temperature)
							});
						} else if (parseFloat(response.temperature) >= 35) {
							tempDatas.push({
								marker: {
									fillColor: '#FF0000',
									lineWidth: 3,
									lineColor: "#FF0000"
								},
								y: parseFloat(response.temperature)
							});
						} else if (parseFloat(response.temperature) > 30 && parseFloat(response.temperature) < 35) {
							tempDatas.push({
								marker: {
									fillColor: '#ffcf42',
									lineWidth: 3,
									lineColor: "#ffcf42"
								},
								y: parseFloat(response.temperature)
							});
						} else if (parseFloat(response.temperature) <= 17) {
							tempDatas.push({
								marker: {
									fillColor: '#00FF00',
									lineWidth: 3,
									lineColor: "#00FF00"
								},
								y: parseFloat(response.temperature)
							});
						} else {
							tempDatas.push(parseFloat(response.temperature));
						}
						humidityDatas.push(parseFloat(response.humidity));
						dateDatas.push(response.Time.substring(0, 5));
					});

					const tempTable = document.querySelector('#tempTable');
					let temperatureValue;
					temperatureValue = responses[responses.length - 1].temperature < 35 ? responses[responses.length - 1].temperature : `<span class="label label-danger" style = "font-size: 1.4rem;font-weight: normal;">${responses[responses.length - 1].temperature}</span>`;
					tempTable.children[1].innerHTML = '';
					tempTable.children[1].innerHTML = `
							<tr>
								<td>${responses[responses.length - 1].station}</td>
								<td>${temperatureValue}°C</td>
								<td>${responses[responses.length - 1].humidity}%</td>
								<td>${responses[responses.length - 1].Date}</td>
								<td>${responses[responses.length - 1].Time}</td>
							</tr>
						`;

					//render graph
					var tempGraph = Highcharts;

					tempGraph.chart('tempGraph', {
						title: {
							// text: 'กราฟอุณหภูมิ ห้องเก็บถุงยางอนามัย'
							text: ''
						},

						subtitle: {
							// text: 'Source: thesolarfoundation.com'
						},

						yAxis: {
							title: {
								text: 'ค่าอุณหภูมิ และ ความชื้น'
							}
						},
						xAxis: {
							categories: dateDatas,
							labels: {
								rotation: -45
							}
						},
						legend: {
							align: 'center',
							verticalAlign: 'top',
							borderWidth: 0
						},
						tooltip: {
							shared: true,
							crosshairs: true
						},
						plotOptions: {
							// line: {
							// 		dataLabels: {
							// 				enabled: false
							// 		},
							// 		enableMouseTracking: false
							// },
							cursor: 'pointer',
							point: {
								events: {
									click: function(e) {
										hs.htmlExpand(null, {
											pageOrigin: {
												x: e.pageX || e.clientX,
												y: e.pageY || e.clientY
											},
											headingText: this.series.name,
											maincontentText: tempGraph.dateFormat('%A, %b %e, %Y', this.x) + ':<br/> ' +
												this.y + ' sessions',
											width: 200
										});
									}
								}
							},
							marker: {
								lineWidth: 1
							}
						},

						series: [{
							name: 'อุณหภูมิ',
							data: tempDatas
						}, {
							name: 'ความชื้น',
							data: humidityDatas
						}],

						responsive: {
							rules: [{
								condition: {
									maxWidth: 50
								},
								chartOptions: {
									legend: {
										layout: 'horizontal',
										align: 'center',
										verticalAlign: 'bottom'
									}
								}
							}]
						},
						credits: {
							enabled: false
						}


					});
				}
			}
		});

		setInterval(() => {
			$.ajax({
				url: '../report/ajaxFetchTempData.php',
				type: "GET",
				success: function(data) {
					// console.log(data);
					const responses = JSON.parse(data);
					const tempDatas = [];
					const humidityDatas = [];
					const dateDatas = [];
					if (responses.length > 0) {
						responses.reverse().forEach(response => {
							if (parseFloat(response.temperature) > 30 && parseFloat(response.temperature) < 35) {
								tempDatas.push({
									marker: {
										fillColor: '#ffcf42',
										lineWidth: 3,
										lineColor: "#ffcf42"
									},
									y: parseFloat(response.temperature)
								});
							} else if (parseFloat(response.temperature) >= 35) {
								tempDatas.push({
									marker: {
										fillColor: '#FF0000',
										lineWidth: 3,
										lineColor: "#FF0000"
									},
									y: parseFloat(response.temperature)
								});
							} else if (parseFloat(response.temperature) <= 17) {
								tempDatas.push({
									marker: {
										fillColor: '#00FF00',
										lineWidth: 3,
										lineColor: "#00FF00"
									},
									y: parseFloat(response.temperature)
								});
							} else {
								tempDatas.push(parseFloat(response.temperature));
							}
							humidityDatas.push(parseFloat(response.humidity));
							dateDatas.push(response.Time);
						});
						const tempTable = document.querySelector('#tempTable');
						let temperatureValue;
						temperatureValue = responses[responses.length - 1].temperature < 35 ? responses[responses.length - 1].temperature : `<span class="label label-danger" style = "font-size: 1.4rem;font-weight: normal;">${responses[responses.length - 1].temperature}</span>`;
						tempTable.children[1].innerHTML = '';
						tempTable.children[1].innerHTML = `
							<tr>
								<td>${responses[responses.length - 1].station}</td>
								<td>${temperatureValue}°C</td>
								<td>${responses[responses.length - 1].humidity}%</td>
								<td>${responses[responses.length - 1].Date}</td>
								<td>${responses[responses.length - 1].Time}</td>
							</tr>
						`;

						//render graph

						var tempGraph = Highcharts;

						tempGraph.chart('tempGraph', {
							title: {
								// text: 'กราฟอุณหภูมิ ห้องเก็บถุงยางอนามัย'
								text: ''
							},

							subtitle: {
								// text: 'Source: thesolarfoundation.com'
							},

							yAxis: {
								title: {
									text: 'ค่าอุณหภูมิ และ ความชื้น'
								}
							},
							xAxis: {
								categories: dateDatas,
								labels: {
									rotation: -45
								}
							},
							legend: {
								align: 'center',
								verticalAlign: 'top',
								borderWidth: 0
							},
							tooltip: {
								shared: true,
								crosshairs: true
							},
							plotOptions: {
								// line: {
								// 		dataLabels: {
								// 				enabled: false
								// 		},
								// 		enableMouseTracking: false
								// },
								cursor: 'pointer',
								point: {
									events: {
										click: function(e) {
											hs.htmlExpand(null, {
												pageOrigin: {
													x: e.pageX || e.clientX,
													y: e.pageY || e.clientY
												},
												headingText: this.series.name,
												maincontentText: tempGraph.dateFormat('%A, %b %e, %Y', this.x) + ':<br/> ' +
													this.y + ' sessions',
												width: 200
											});
										}
									}
								},
								marker: {
									lineWidth: 1
								}
							},

							series: [{
								name: 'อุณหภูมิ',
								data: tempDatas
							}, {
								name: 'ความชื้น',
								data: humidityDatas
							}],

							responsive: {
								rules: [{
									condition: {
										maxWidth: 50
									},
									chartOptions: {
										legend: {
											layout: 'horizontal',
											align: 'center',
											verticalAlign: 'bottom'
										}
									}
								}]
							},
							credits: {
								enabled: false
							}


						});
					}
				}
			});
		}, 300000);

		$('#clinicRedraw').on('click', function() {
			var xxxx = $('#clinicChart').highcharts();

			// series: [{
			// 			name: 'Tested',
			// 			data: responses.Tested
			// 		}, {
			// 			name: 'HIV+',
			// 			data: responses.HIV
			// 		}, {
			// 			name: 'TPHA+',
			// 			data: responses.TPHA
			// 		}],

			xxxx.series[0].update({
				data: [10, 9]
			}, false);

			xxxx.redraw();
		});

		$('#clinicRedraw2').on('click', function() {
			var xxxx = $('#clinicChart').highcharts();

			// เลือกเดือน-ปี จาก dropbox แล้วกดปุ่ม redraw
			// fetch ข้อมูล

			xxxx.series[0].update({
				data: [30, 25, 15]
			}, false);
			xxxx.series[1].update({
				data: [3, 1, 0]
			}, false);
			xxxx.series[2].update({
				data: [5, 0, 3]
			}, false);

			xxxx.redraw();
		});

	});


	const dashboardVue = new Vue({
		el: '#dashboardMainSection',
		data: {
			quarter: getRequests()['quarter'] ? getRequests()['quarter'] : '<?= checkQuarter()['this_quarter'] ?>',
			select_fy: 'fy26',
			quarterName: 'Quarter <?= checkQuarter()['this_quarter'] ?>',
			startDate: moment().startOf('quarter').format('YYYY-MM-DD'),
			endDate: moment().endOf('quarter').format('YYYY-MM-DD'),
			startFY: moment().month() >= 9 ? moment().startOf('year').month(9).startOf('month').format('YYYY-MM-DD') : moment().startOf('year').subtract(1, 'year').month(9).startOf('month').format('YYYY-MM-DD'),
			endFY: moment().month() >= 9 ? moment().startOf('year').add(1, 'year').month(9).startOf('month').format('YYYY-MM-DD') : moment().startOf('year').month(9).startOf('month').format('YYYY-MM-DD'),
			healthcare_start: '',
			healthcare_end: '',
			healthcare_total: 0,
		},
		methods: {

			fetchNap() {

				this.$refs.overlay.classList.remove('hidden');

				axios.post('./ajaxFetchNapCount.php')
					.then(res => {

						this.$refs.overlay.classList.add('hidden');

						this.$refs.reachNapCount.className = res.data.reachNap > 0 ? 'text-danger' : 'text-success'
						this.$refs.clinicNapCount.className = res.data.clinicNap > 0 ? 'text-danger' : 'text-success'
						this.$refs.syphilisNapCount.className = res.data.syphilisNap > 0 ? 'text-danger' : 'text-success'
						this.$refs.cd4NapCount.className = res.data.cd4Nap > 0 ? 'text-danger' : 'text-success'
						this.$refs.vlNapCount.className = res.data.vlNap > 0 ? 'text-danger' : 'text-success'
						this.$refs.hcvNapCount.className = res.data.hcvNap > 0 ? 'text-danger' : 'text-success'

						this.$refs.reachNapCount.innerHTML = `สปสช Reach [ค้าง ${res.data.reachNap} เคส] -> <a target=\"_blank\" href=\"../napktb/nhsoForReach.php?start=${this.startFY}&end=${this.endFY}&ecascade=true\">${labelbig('เข้าหน้า NAP Reach', 'purple', '1rem')} </a>`
						this.$refs.clinicNapCount.innerHTML = `สปสช Clinic [ค้าง ${res.data.clinicNap} เคส] -> <a target=\"_blank\" href=\"../napktb/nhsoForClinic.php?start=${this.startFY}&end=${this.endFY}&ecascade=true\">${labelbig('เข้าหน้า NAP Clinic', 'purple', '1rem')} </a>`
						this.$refs.syphilisNapCount.innerHTML = `สปสช Syphilis [ค้าง ${res.data.syphilisNap} เคส] -> <a target=\"_blank\" href=\"../napktb/nhsoForClinicSyphilis.php?start=${this.startFY}&end=${this.endFY}&ecascade=true\">${labelbig('เข้าหน้า NAP Syphilis', 'purple', '1rem')} </a>`
						this.$refs.cd4NapCount.innerHTML = `สปสช CD4 [ค้าง ${res.data.cd4Nap} เคส] -> <a target=\"_blank\" href=\"../napktb/nhsoForCD4.php?start=${this.startFY}&end=${this.endFY}&ecascade=true\">${labelbig('เข้าหน้า NAP CD4', 'purple', '1rem')} </a>`
						this.$refs.vlNapCount.innerHTML = `สปสช VL [ค้าง ${res.data.vlNap} เคส] -> <a target=\"_blank\" href=\"../napktb/nhsoForVL.php?start=${this.startFY}&end=${this.endFY}&ecascade=true\">${labelbig('เข้าหน้า NAP VL', 'purple', '1rem')} </a>`
						this.$refs.hcvNapCount.innerHTML = `สปสช HCV [ค้าง ${res.data.hcvNap} เคส] -> <a target=\"_blank\" href=\"../napktb/nhsoForHCV.php?start=${this.startFY}&end=${this.endFY}&ecascade=true\">${labelbig('เข้าหน้า NAP HCV', 'purple', '1rem')} </a>`
					})
			},

			filterQuarterRetainNeg(fy = null, quarter = null) {
				if (fy !== null) {
					this.select_fy = fy
				}
				if (quarter !== null) {
					this.quarter = quarter
				}

				const fyYear = parseInt(this.select_fy.replace('fy', '')) + 2000

				if (this.quarter === '1') {
					this.quarterName = 'Quarter 1'
					this.startDate = `${fyYear - 1}-10-01`
					this.endDate = `${fyYear}-01-01`
				} else if (this.quarter === '2') {
					this.quarterName = 'Quarter 2'
					this.startDate = `${fyYear}-01-01`
					this.endDate = `${fyYear}-04-01`
				} else if (this.quarter === '3') {
					this.quarterName = 'Quarter 3'
					this.startDate = `${fyYear}-04-01`
					this.endDate = `${fyYear}-07-01`
				} else if (this.quarter === '4') {
					this.quarterName = 'Quarter 4'
					this.startDate = `${fyYear}-07-01`
					this.endDate = `${fyYear}-10-01`
				} else if (this.quarter === 'all') {
					this.quarterName = 'ทั้งปีงบประมาณ'
					this.startDate = `${fyYear - 1}-10-01`
					this.endDate = `${fyYear}-10-01`
				}

				this.renderRetainNegGraph()
			},

			renderRetainNegGraph() {

				axios.get(`./ajaxFetchRetainNegative.php?start=${this.startDate}&end=${this.endDate}`)
					.then(responses => {
						// console.log(responses.data)

						const retainCbsArray = [];
						const retainDateArray = [];

						const dataRetainMain = [];
						let dataRetainMainDetail = {};

						const dataRetainMainSuccess = [];
						let dataRetainMainSuccessDetail = {};

						const dataRetainMainTested = [];
						let dataRetainMainTestedDetail = {};

						const dataRetainSub = [];
						let dataRetainSubDetail = {};
						const cbsSubDataDetail = []; // contain date (date/month) and retain value of that date

						let dataRetainSubSuccessDetail = {};
						const cbsSubDataSuccessDetail = []; // contain date (date/month) and retain value of that date

						let dataRetainSubTestedDetail = {};
						const cbsSubDataTestedDetail = []; // contain date (date/month) and retain value of that date

						const cbsCount = [];
						const cbsCountSuccess = [];
						const cbsCountTested = [];

						let retainDate;

						responses.data.forEach(data => {

							retainDate = data.retainDate.split(' ')[0]

							if (!retainCbsArray.includes(data.cbs)) {

								cbsCount[data.cbs] = 1

								retainCbsArray.push(data.cbs)
								cbsCountSuccess[data.cbs] = (![null, ''].includes(data.inviteToTest)) ? 1 : 0
								cbsCountTested[data.cbs] = (![null, ''].includes(data.hiv_result)) ? 1 : 0

							} else {

								cbsCount[data.cbs]++

								if (![null, ''].includes(data.inviteToTest)) cbsCountSuccess[data.cbs]++
								if (![null, ''].includes(data.hiv_result)) cbsCountTested[data.cbs]++

							}

							if (!retainDateArray.includes(retainDate)) retainDateArray.push(retainDate)
						})

						const dateCount = [];
						const dateCountSuccess = [];
						const dateCountTested = [];

						retainCbsArray.forEach(cbs => {

							dateCount[cbs] = []
							cbsSubDataDetail[cbs] = []

							dateCountSuccess[cbs] = []
							cbsSubDataSuccessDetail[cbs] = []

							dateCountTested[cbs] = []
							cbsSubDataTestedDetail[cbs] = []

							retainDateArray.forEach(date => {

								dateCount[cbs][date] = 0

								dateCountSuccess[cbs][date] = 0

								dateCountTested[cbs][date] = 0

								responses.data.forEach(data => {

									if (date === data.retainDate.split(' ')[0] && cbs == data.cbs) {
										dateCount[cbs][date]++

										if (![null, ''].includes(data.inviteToTest)) dateCountSuccess[cbs][date]++
										if (data.hiv_result != null) dateCountTested[cbs][date]++

									}

								})

								if (dateCount[cbs][date]) {
									cbsSubDataDetail[cbs].push([date, dateCount[cbs][date]])
									cbsSubDataSuccessDetail[cbs].push([date, dateCountSuccess[cbs][date]])
									cbsSubDataTestedDetail[cbs].push([date, dateCountTested[cbs][date]])
								}

							})
						})

						retainCbsArray.forEach(cbs => {

							dataRetainMainDetail.name = cbs
							dataRetainMainDetail.y = cbsCount[cbs]
							dataRetainMainDetail.drilldown = cbs + '-all'

							dataRetainMain.push(dataRetainMainDetail)

							dataRetainMainDetail = {}

							dataRetainMainSuccessDetail.name = cbs
							dataRetainMainSuccessDetail.y = cbsCountSuccess[cbs]

							dataRetainMainSuccessDetail.drilldown = cbs + '-succeeded'

							dataRetainMainSuccess.push(dataRetainMainSuccessDetail)

							dataRetainMainSuccessDetail = {}

							dataRetainMainTestedDetail.name = cbs
							dataRetainMainTestedDetail.y = cbsCountTested[cbs]
							dataRetainMainTestedDetail.drilldown = cbs + '-tested'

							dataRetainMainTested.push(dataRetainMainTestedDetail)

							dataRetainMainTestedDetail = {}

							dataRetainSubDetail.name = cbs + '-All'
							dataRetainSubDetail.id = cbs + '-all'
							dataRetainSubDetail.data = cbsSubDataDetail[cbs]

							dataRetainSub.push(dataRetainSubDetail)

							dataRetainSubDetail = {}

							dataRetainSubSuccessDetail.name = cbs + '-Succeeded'
							dataRetainSubSuccessDetail.id = cbs + '-succeeded'
							dataRetainSubSuccessDetail.data = cbsSubDataSuccessDetail[cbs]

							dataRetainSub.push(dataRetainSubSuccessDetail)

							dataRetainSubSuccessDetail = {}

							dataRetainSubTestedDetail.name = cbs + '-Tested'
							dataRetainSubTestedDetail.id = cbs + '-tested'
							dataRetainSubTestedDetail.data = cbsSubDataTestedDetail[cbs]

							dataRetainSub.push(dataRetainSubTestedDetail)

							dataRetainSubTestedDetail = {}
						})

						// negative retain graph
						const negativeRetainGraph = Highcharts;

						// console.log(dataRetainSub)

						negativeRetainGraph.chart('negativeRetainGraph', {
							chart: {
								type: 'column'
							},
							credits: {
								enabled: false
							},
							title: {
								text: 'Negative Retention'
							},
							subtitle: {
								text: `Realtime updated : selected ${this.quarterName}`
							},
							// subtitle: {
							//     text: 'Click the columns to view versions. Source: <a href="http://statcounter.com" target="_blank">statcounter.com</a>'
							// },
							accessibility: {
								announceNewData: {
									enabled: true
								}
							},
							xAxis: {
								type: 'category'
							},
							yAxis: {
								title: {
									text: 'จำนวนเคสที่โทรหา'
								}

							},
							legend: {
								enabled: true
							},
							plotOptions: {
								series: {
									borderWidth: 0,
									dataLabels: {
										enabled: true,
										format: '{point.y}'
										// format: '{point.y:.1f}%' // แสดง % และ 1 digits
									}
								}
							},

							tooltip: {
								headerFormat: '<span style="font-size:11px">{series.name}</span><br>',
								pointFormat: '<span style="color:{point.color}">{point.name}</span>: <b>{point.y}</b> Cases<br/>'
							},

							series: [{
									name: "Retain All",
									color: '#3C8DBC',
									// colorByPoint: true,
									data: dataRetainMain
								},
								{
									name: "Retain Success",
									color: '#00A65A',
									// colorByPoint: true,
									data: dataRetainMainSuccess
								},
								{
									name: "Tested",
									color: '#D81B60',
									// colorByPoint: true,
									data: dataRetainMainTested
								}
							],
							drilldown: {
								allowPointDrilldown: false,
								series: dataRetainSub
							}
						});
						// negative retain graph

					})
					.catch(error => console.log(error.response.status))
			},
			pullMobile(start, end) {
				this.healthcare_start = start
				this.healthcare_end = end

				this.genGraphHealthCare()
			},
			genGraphHealthCare() {
				$.ajax({
					url: '../dashboard/ajaxFetcHealthcareChart.php',
					type: "POST",
					data: {
						start: this.healthcare_start,
						end: this.healthcare_end
					},
					success: (data) => {
						// console.log(data);
						const responses = JSON.parse(data);

						// console.log(responses);

						let healthcare_data = responses

						const healthCareChart = document.querySelector('#healthCareChart');

						this.healthcare_total = healthcare_data.map(number => number.y).reduce((a, b) => a + b, 0);

						let healthcare_percentages = healthcare_data.map(number => (number.y / this.healthcare_total * 100).toFixed(2) + '%');

						let table_row = ''

						healthcare_data.forEach((item, index) => {
							table_row += `<tr>
											<td>${index + 1}</td>
											<td>${item.name}</td>
											<td>${item.y}</td>
											<td>${healthcare_percentages[index]}</td>
										</tr>`
						})

						healthCareChart.children[1].innerHTML = table_row
						// Mobile Cinic Total Table

						Highcharts.chart('healthcare_pie', {
							chart: {
								plotBackgroundColor: null,
								plotBorderWidth: null,
								plotShadow: false,
								type: 'pie'
							},
							credits: {
								enabled: false
							},
							title: {
								text: `สัดส่วนสิทธิรักษา`
							},
							subtitle: {
								text: 'ข้อมูลเริ่มจัดเก็บ 2022-12-22'
							},
							tooltip: {
								pointFormat: '{series.name}: <b>{point.y:1f}<b> เคส </b></b>'
							},
							accessibility: {
								point: {
									valueSuffix: ''
								}
							},
							plotOptions: {
								pie: {
									allowPointSelect: true,
									cursor: 'pointer',
									dataLabels: {
										enabled: true,
										// format: '<b>{point.name}</b>: {point.y:1f} <b> เคส ({percentage.toFixed(2)} %)</b>',
										formatter: function() {
											return `<b>${this.point.name}</b>: ${this.point.y} <b> เคส (${this.percentage.toFixed(2)} %)</b>`;
										}
									}
								}
							},
							series: [{
								name: 'Daily Ondemand',
								colorByPoint: true,
								data: healthcare_data
							}]
						});
					}
				});
			},
		},
		mounted() {

			this.renderRetainNegGraph()
			this.genGraphHealthCare()

			// let default_start = checkQuarter().q1_start
			// let default_end = checkQuarter().q4_end

			let default_start = checkQuarter().this_quarter_start
			let default_end = checkQuarter().this_quarter_end

			$(`#healthcare_range`).flatpickr({
				mode: "range",
				// minDate: "today",
				dateFormat: "Y-m-d",
				defaultDate: [default_start, default_end],

				plugins: [
					ShortcutButtonsPlugin({
						button: [{
								label: "Month"
							},
							{
								label: "Quarter"
							},
							{
								label: "FY"
							},
							{
								label: "last FY"
							},
						],
						// label: "or",
						onClick: (index, fp) => {

							let start = moment().startOf('month').format('YYYY-MM-DD')
							let end = moment().endOf('month').format('YYYY-MM-DD')

							if (index == 0) {
								start = moment().startOf('month').format('YYYY-MM-DD')
								end = moment().endOf('month').format('YYYY-MM-DD')
							}

							if (index == 1) {
								start = moment().startOf('quarter').format('YYYY-MM-DD')
								end = moment().endOf('quarter').format('YYYY-MM-DD')
							}

							if (index == 2) {
								start = moment().startOf('year').subtract(3, 'months').format('YYYY-MM-DD')
								end = moment().endOf('year').subtract(3, 'months').format('YYYY-MM-DD')

								if (moment().month() > 8) {
									start = moment().add(1, 'y').startOf('year').subtract(3, 'months').format('YYYY-MM-DD')
									end = moment().add(1, 'y').endOf('year').subtract(3, 'months').format('YYYY-MM-DD')
								}
							}

							if (index == 3) {
								start = moment().subtract(1, 'y').startOf('year').subtract(3, 'months').format('YYYY-MM-DD')
								end = moment().subtract(1, 'y').endOf('year').subtract(3, 'months').format('YYYY-MM-DD')

								if (moment().month() > 8) {
									start = moment().startOf('year').subtract(3, 'months').format('YYYY-MM-DD')
									end = moment().endOf('year').subtract(3, 'months').format('YYYY-MM-DD')
								}
							}

							fp.setDate([start, end]);
							this.pullMobile(start, end)

						}
					})
				],

				// enable: [
				//   {
				//     from: moment().subtract(7, 'days').format('YYYY-MM-DD'),
				//     to: moment().add(7, 'd').format('YYYY-MM-DD')
				//   },
				// ],
				onChange: (selectedDates, dateStr, instance) => {
					// console.log(selectedDates)
					if (selectedDates.length == 2) {
						this.pullMobile(moment(selectedDates[0]).format('YYYY-MM-DD'), moment(selectedDates[1]).format('YYYY-MM-DD'))
					}
				}
			});

			$('#boxLastMonth, #temGraph, #retainLast4Month').boxWidget('collapse');
			$('.box-header').css('cursor', 'pointer');
		}
	})
</script>

<!-- ********Chart Data Script******** -->

<?php include '../layouts/end.php' ?>
